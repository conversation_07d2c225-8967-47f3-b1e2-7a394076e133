Imports MySql.Data.MySqlClient
Imports System.Data

''' <summary>
''' 数据库管理器 - 单例模式
''' 负责数据库连接和基本操作
''' </summary>
Public Class DatabaseManager
    Private Shared _instance As DatabaseManager
    Private Shared ReadOnly _lock As New Object()
    Private _connectionString As String

    ' 单例模式实现
    Public Shared ReadOnly Property Instance As DatabaseManager
        Get
            If _instance Is Nothing Then
                SyncLock _lock
                    If _instance Is Nothing Then
                        _instance = New DatabaseManager()
                    End If
                End SyncLock
            End If
            Return _instance
        End Get
    End Property

    Private Sub New()
        ' 数据库连接字符串
        _connectionString = "Server=127.0.0.1;Port=3306;Database=ERPSystem;Uid=root;Pwd=******;CharSet=utf8mb4;"
    End Sub

    ''' <summary>
    ''' 初始化数据库连接和表结构
    ''' </summary>
    Public Shared Sub Initialize()
        Instance.CreateDatabaseIfNotExists()
        Instance.CreateTables()
        Instance.InsertTestData()
    End Sub

    ''' <summary>
    ''' 创建数据库（如果不存在）
    ''' </summary>
    Private Sub CreateDatabaseIfNotExists()
        Dim connectionStringWithoutDb As String = "Server=127.0.0.1;Port=3306;Uid=root;Pwd=******;"
        
        Using connection As New MySqlConnection(connectionStringWithoutDb)
            connection.Open()
            Dim command As New MySqlCommand("CREATE DATABASE IF NOT EXISTS ERPSystem CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", connection)
            command.ExecuteNonQuery()
        End Using
    End Sub

    ''' <summary>
    ''' 获取数据库连接
    ''' </summary>
    Public Function GetConnection() As MySqlConnection
        Return New MySqlConnection(_connectionString)
    End Function

    ''' <summary>
    ''' 执行查询并返回DataTable
    ''' </summary>
    Public Function ExecuteQuery(sql As String, Optional parameters As Dictionary(Of String, Object) = Nothing) As DataTable
        Using connection = GetConnection()
            connection.Open()
            Using command As New MySqlCommand(sql, connection)
                If parameters IsNot Nothing Then
                    For Each param In parameters
                        command.Parameters.AddWithValue(param.Key, param.Value)
                    Next
                End If
                
                Using adapter As New MySqlDataAdapter(command)
                    Dim dataTable As New DataTable()
                    adapter.Fill(dataTable)
                    Return dataTable
                End Using
            End Using
        End Using
    End Function

    ''' <summary>
    ''' 执行非查询命令（INSERT, UPDATE, DELETE）
    ''' </summary>
    Public Function ExecuteNonQuery(sql As String, Optional parameters As Dictionary(Of String, Object) = Nothing) As Integer
        Using connection = GetConnection()
            connection.Open()
            Using command As New MySqlCommand(sql, connection)
                If parameters IsNot Nothing Then
                    For Each param In parameters
                        command.Parameters.AddWithValue(param.Key, param.Value)
                    Next
                End If
                
                Return command.ExecuteNonQuery()
            End Using
        End Using
    End Function

    ''' <summary>
    ''' 执行标量查询（返回单个值）
    ''' </summary>
    Public Function ExecuteScalar(sql As String, Optional parameters As Dictionary(Of String, Object) = Nothing) As Object
        Using connection = GetConnection()
            connection.Open()
            Using command As New MySqlCommand(sql, connection)
                If parameters IsNot Nothing Then
                    For Each param In parameters
                        command.Parameters.AddWithValue(param.Key, param.Value)
                    Next
                End If

                Return command.ExecuteScalar()
            End Using
        End Using
    End Function

    ''' <summary>
    ''' 创建数据库表结构
    ''' </summary>
    Private Sub CreateTables()
        ' 用户表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                full_name VARCHAR(100),
                email VARCHAR(100),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 客户表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS customers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customer_code VARCHAR(20) UNIQUE NOT NULL,
                customer_name VARCHAR(100) NOT NULL,
                contact_person VARCHAR(50),
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                tax_number VARCHAR(50),
                payment_terms VARCHAR(50),
                credit_limit DECIMAL(15,2) DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 供应商表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS suppliers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                supplier_code VARCHAR(20) UNIQUE NOT NULL,
                supplier_name VARCHAR(100) NOT NULL,
                contact_person VARCHAR(50),
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                tax_number VARCHAR(50),
                payment_terms VARCHAR(50),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 库位表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS locations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                location_code VARCHAR(20) UNIQUE NOT NULL,
                location_name VARCHAR(100) NOT NULL,
                location_type VARCHAR(20) DEFAULT 'storage',
                parent_id INT,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES locations(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 物料表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS materials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                material_code VARCHAR(50) UNIQUE NOT NULL,
                material_name VARCHAR(200) NOT NULL,
                drawing_number VARCHAR(50),
                version VARCHAR(10),
                unit VARCHAR(10) NOT NULL DEFAULT 'PCS',
                category VARCHAR(50),
                specification TEXT,
                safety_stock DECIMAL(15,3) DEFAULT 0,
                min_order_qty DECIMAL(15,3) DEFAULT 1,
                standard_price DECIMAL(15,4) DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 订单表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS orders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_number VARCHAR(50) UNIQUE NOT NULL,
                customer_id INT NOT NULL,
                order_date DATE NOT NULL,
                delivery_date DATE,
                order_status VARCHAR(20) DEFAULT 'pending',
                total_amount DECIMAL(15,2) DEFAULT 0,
                tax_rate DECIMAL(5,4) DEFAULT 0.13,
                remarks TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 订单明细表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS order_details (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NOT NULL,
                line_number VARCHAR(10) NOT NULL,
                work_order_number VARCHAR(50),
                material_id INT NOT NULL,
                location_id INT NOT NULL,
                order_quantity DECIMAL(15,3) NOT NULL,
                invoiced_quantity DECIMAL(15,3) DEFAULT 0,
                delivered_quantity DECIMAL(15,3) DEFAULT 0,
                unit_price_with_tax DECIMAL(15,4) NOT NULL,
                unit_price_without_tax DECIMAL(15,4) NOT NULL,
                line_total DECIMAL(15,2) NOT NULL,
                invoiced_amount DECIMAL(15,2) DEFAULT 0,
                delivered_amount DECIMAL(15,2) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(id),
                FOREIGN KEY (material_id) REFERENCES materials(id),
                FOREIGN KEY (location_id) REFERENCES locations(id),
                UNIQUE KEY unique_order_line (order_id, line_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 库存表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS inventory (
                id INT AUTO_INCREMENT PRIMARY KEY,
                material_id INT NOT NULL,
                location_id INT NOT NULL,
                current_stock DECIMAL(15,3) DEFAULT 0,
                reserved_stock DECIMAL(15,3) DEFAULT 0,
                available_stock DECIMAL(15,3) DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (material_id) REFERENCES materials(id),
                FOREIGN KEY (location_id) REFERENCES locations(id),
                UNIQUE KEY unique_material_location (material_id, location_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 入库单表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS inbound_orders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                inbound_number VARCHAR(50) UNIQUE NOT NULL,
                supplier_id INT,
                inbound_date DATE NOT NULL,
                inbound_type VARCHAR(20) DEFAULT 'purchase',
                status VARCHAR(20) DEFAULT 'pending',
                total_amount DECIMAL(15,2) DEFAULT 0,
                remarks TEXT,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 入库单明细表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS inbound_details (
                id INT AUTO_INCREMENT PRIMARY KEY,
                inbound_id INT NOT NULL,
                material_id INT NOT NULL,
                location_id INT NOT NULL,
                planned_quantity DECIMAL(15,3) NOT NULL,
                actual_quantity DECIMAL(15,3) DEFAULT 0,
                unit_price DECIMAL(15,4) DEFAULT 0,
                line_total DECIMAL(15,2) DEFAULT 0,
                batch_number VARCHAR(50),
                expiry_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (inbound_id) REFERENCES inbound_orders(id),
                FOREIGN KEY (material_id) REFERENCES materials(id),
                FOREIGN KEY (location_id) REFERENCES locations(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 出库单表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS outbound_orders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                outbound_number VARCHAR(50) UNIQUE NOT NULL,
                customer_id INT,
                outbound_date DATE NOT NULL,
                outbound_type VARCHAR(20) DEFAULT 'sales',
                status VARCHAR(20) DEFAULT 'pending',
                total_amount DECIMAL(15,2) DEFAULT 0,
                remarks TEXT,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 出库单明细表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS outbound_details (
                id INT AUTO_INCREMENT PRIMARY KEY,
                outbound_id INT NOT NULL,
                material_id INT NOT NULL,
                location_id INT NOT NULL,
                planned_quantity DECIMAL(15,3) NOT NULL,
                actual_quantity DECIMAL(15,3) DEFAULT 0,
                unit_price DECIMAL(15,4) DEFAULT 0,
                line_total DECIMAL(15,2) DEFAULT 0,
                batch_number VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (outbound_id) REFERENCES outbound_orders(id),
                FOREIGN KEY (material_id) REFERENCES materials(id),
                FOREIGN KEY (location_id) REFERENCES locations(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 创建财务相关表
        CreateFinancialTables()
    End Sub

    ''' <summary>
    ''' 创建财务管理相关表
    ''' </summary>
    Private Sub CreateFinancialTables()
        ' 创建应收账款表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS accounts_receivable (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ar_number VARCHAR(50) NOT NULL UNIQUE COMMENT '应收单号',
                customer_id INT NOT NULL,
                order_id INT NULL COMMENT '关联订单ID',
                invoice_number VARCHAR(50) NULL COMMENT '发票号码',
                invoice_date DATE NULL COMMENT '开票日期',
                due_date DATE NOT NULL COMMENT '到期日期',
                original_amount DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '原始金额',
                paid_amount DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '已付金额',
                outstanding_amount DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '未付金额',
                currency VARCHAR(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
                exchange_rate DECIMAL(10,4) NOT NULL DEFAULT 1.0000 COMMENT '汇率',
                status ENUM('pending', 'partial', 'paid', 'overdue', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
                payment_terms VARCHAR(100) NULL COMMENT '付款条件',
                description TEXT NULL COMMENT '备注说明',
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (order_id) REFERENCES orders(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                INDEX idx_ar_customer_id (customer_id),
                INDEX idx_ar_order_id (order_id),
                INDEX idx_ar_status (status),
                INDEX idx_ar_due_date (due_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 创建应付账款表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS accounts_payable (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ap_number VARCHAR(50) NOT NULL UNIQUE COMMENT '应付单号',
                supplier_id INT NOT NULL,
                purchase_order_number VARCHAR(50) NULL COMMENT '采购订单号',
                invoice_number VARCHAR(50) NULL COMMENT '供应商发票号',
                invoice_date DATE NULL COMMENT '发票日期',
                due_date DATE NOT NULL COMMENT '到期日期',
                original_amount DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '原始金额',
                paid_amount DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '已付金额',
                outstanding_amount DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '未付金额',
                currency VARCHAR(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
                exchange_rate DECIMAL(10,4) NOT NULL DEFAULT 1.0000 COMMENT '汇率',
                status ENUM('pending', 'partial', 'paid', 'overdue', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
                payment_terms VARCHAR(100) NULL COMMENT '付款条件',
                description TEXT NULL COMMENT '备注说明',
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                INDEX idx_ap_supplier_id (supplier_id),
                INDEX idx_ap_status (status),
                INDEX idx_ap_due_date (due_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")

        ' 创建收付款记录表
        ExecuteNonQuery("
            CREATE TABLE IF NOT EXISTS payment_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                payment_number VARCHAR(50) NOT NULL UNIQUE COMMENT '收付款单号',
                payment_type ENUM('receivable', 'payable') NOT NULL COMMENT '类型：应收/应付',
                reference_id INT NOT NULL COMMENT '关联应收/应付账款ID',
                payment_date DATE NOT NULL COMMENT '收付款日期',
                payment_amount DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '收付款金额',
                payment_method VARCHAR(50) NULL COMMENT '收付款方式',
                bank_account VARCHAR(100) NULL COMMENT '银行账户',
                reference_number VARCHAR(100) NULL COMMENT '参考号码',
                description TEXT NULL COMMENT '备注说明',
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id),
                INDEX idx_payment_type (payment_type),
                INDEX idx_payment_date (payment_date),
                INDEX idx_reference_id (reference_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ")
    End Sub

    ''' <summary>
    ''' 插入测试数据
    ''' </summary>
    Private Sub InsertTestData()
        ' 插入默认用户
        ExecuteNonQuery("
            INSERT IGNORE INTO users (username, password, role, full_name, email) VALUES
            ('admin', 'admin123', 'admin', '系统管理员', '<EMAIL>'),
            ('user1', 'user123', 'user', '普通用户', '<EMAIL>'),
            ('manager', 'manager123', 'manager', '部门经理', '<EMAIL>'),
            ('readonly', 'readonly123', 'readonly', '只读用户', '<EMAIL>'),
            ('test_user', 'test123', 'user', '测试用户', '<EMAIL>')
        ")

        ' 插入客户数据
        ExecuteNonQuery("
            INSERT IGNORE INTO customers (customer_code, customer_name, contact_person, phone, address) VALUES
            ('C001', '卓郎', '张经理', '021-12345678', '上海市浦东新区'),
            ('C002', '华为技术', '李经理', '0755-28780808', '深圳市龙岗区'),
            ('C003', '中兴通讯', '王经理', '0755-26770000', '深圳市南山区')
        ")

        ' 插入供应商数据
        ExecuteNonQuery("
            INSERT IGNORE INTO suppliers (supplier_code, supplier_name, contact_person, phone, address) VALUES
            ('S001', '上海钢铁', '赵经理', '021-55555555', '上海市宝山区'),
            ('S002', '深圳电子', '钱经理', '0755-88888888', '深圳市福田区'),
            ('S003', '北京机械', '孙经理', '010-66666666', '北京市朝阳区')
        ")

        ' 插入库位数据
        ExecuteNonQuery("
            INSERT IGNORE INTO locations (location_code, location_name, location_type, description) VALUES
            ('T01', 'T01库位', 'storage', '主要存储区域T01'),
            ('T02', 'T02库位', 'storage', '主要存储区域T02'),
            ('T03', 'T03库位', 'storage', '主要存储区域T03'),
            ('WIP', '在制品区', 'wip', '在制品临时存储区'),
            ('QC', '质检区', 'qc', '质量检验区域')
        ")

        ' 插入物料数据（基于提供的订单信息）
        ExecuteNonQuery("
            INSERT IGNORE INTO materials (material_code, material_name, drawing_number, version, unit, category, standard_price) VALUES
            ('*********', '线槽T670', '*********', 'B', 'PCS', '机械件', 171.26),
            ('*********', '隔纱板T670', '*********', 'B', 'PCS', '机械件', 147.24),
            ('*********', '覆板T670', '*********', 'A', 'PCS', '机械件', 75.57)
        ")

        ' 插入测试订单数据
        ExecuteNonQuery("
            INSERT IGNORE INTO orders (order_number, customer_id, order_date, delivery_date, order_status, total_amount, tax_rate, remarks) VALUES
            ('SO20250704001', 1, '2025-07-04', '2025-07-15', 'pending', 15000.00, 0.13, '测试订单1'),
            ('SO20250704002', 2, '2025-07-04', '2025-07-20', 'confirmed', 25000.00, 0.13, '测试订单2'),
            ('SO20250704003', 3, '2025-07-05', '2025-07-25', 'in_production', 18000.00, 0.13, '测试订单3')
        ")

        ' 插入测试订单明细数据
        ExecuteNonQuery("
            INSERT IGNORE INTO order_details (order_id, line_number, work_order_number, material_id, location_id,
                                             order_quantity, invoiced_quantity, delivered_quantity, unit_price_with_tax,
                                             unit_price_without_tax, line_total, invoiced_amount, delivered_amount) VALUES
            (1, '001', 'WO2025001', 1, 1, 50, 0, 0, 171.26, 151.55, 8563.00, 0, 0),
            (1, '002', 'WO2025001', 2, 1, 30, 0, 0, 147.24, 130.30, 4417.20, 0, 0),
            (2, '001', 'WO2025002', 1, 2, 80, 20, 10, 171.26, 151.55, 13700.80, 3425.20, 1712.60),
            (2, '002', 'WO2025002', 3, 2, 100, 50, 25, 75.57, 66.87, 7557.00, 3778.50, 1889.25),
            (3, '001', 'WO2025003', 2, 1, 60, 0, 0, 147.24, 130.30, 8834.40, 0, 0),
            (3, '002', 'WO2025003', 3, 3, 80, 0, 0, 75.57, 66.87, 6045.60, 0, 0)
        ")
    End Sub
End Class
