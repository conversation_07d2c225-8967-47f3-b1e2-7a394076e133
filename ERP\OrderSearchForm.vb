Imports System.Windows.Forms

Public Class OrderSearchForm
    Inherits Form

    Private txtOrderNumber As TextBox
    Private cmbCustomer As ComboBox
    Private cmbOrderStatus As ComboBox
    Private dtpDateFrom As DateTimePicker
    Private dtpDateTo As DateTimePicker
    Private chkDateRange As CheckBox

    Private btnSearch As Button
    Private btnClear As Button
    Private btnCancel As Button

    Public Property SearchCriteria As Dictionary(Of String, Object)

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadCustomers()
        SetupOrderStatus()
        ClearForm()
    End Sub

    Private Sub SetupUI()
        Me.Text = "订单搜索"
        Me.Size = New Size(450, 300)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False

        ' 订单编号
        Dim lblOrderNumber As New Label()
        lblOrderNumber.Text = "订单编号:"
        lblOrderNumber.Location = New Point(12, 20)
        lblOrderNumber.Size = New Size(80, 23)
        Me.Controls.Add(lblOrderNumber)

        txtOrderNumber = New TextBox()
        txtOrderNumber.Location = New Point(100, 18)
        txtOrderNumber.Size = New Size(200, 23)
        Me.Controls.Add(txtOrderNumber)

        ' 客户
        Dim lblCustomer As New Label()
        lblCustomer.Text = "客户:"
        lblCustomer.Location = New Point(12, 60)
        lblCustomer.Size = New Size(50, 23)
        Me.Controls.Add(lblCustomer)

        cmbCustomer = New ComboBox()
        cmbCustomer.Location = New Point(100, 58)
        cmbCustomer.Size = New Size(200, 23)
        cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbCustomer)

        ' 订单状态
        Dim lblOrderStatus As New Label()
        lblOrderStatus.Text = "订单状态:"
        lblOrderStatus.Location = New Point(12, 100)
        lblOrderStatus.Size = New Size(80, 23)
        Me.Controls.Add(lblOrderStatus)

        cmbOrderStatus = New ComboBox()
        cmbOrderStatus.Location = New Point(100, 98)
        cmbOrderStatus.Size = New Size(200, 23)
        cmbOrderStatus.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbOrderStatus)

        ' 日期范围
        chkDateRange = New CheckBox()
        chkDateRange.Text = "按日期范围搜索"
        chkDateRange.Location = New Point(12, 140)
        chkDateRange.Size = New Size(150, 23)
        Me.Controls.Add(chkDateRange)

        Dim lblDateFrom As New Label()
        lblDateFrom.Text = "从:"
        lblDateFrom.Location = New Point(12, 170)
        lblDateFrom.Size = New Size(30, 23)
        Me.Controls.Add(lblDateFrom)

        dtpDateFrom = New DateTimePicker()
        dtpDateFrom.Location = New Point(50, 168)
        dtpDateFrom.Size = New Size(120, 23)
        dtpDateFrom.Format = DateTimePickerFormat.Short
        dtpDateFrom.Enabled = False
        Me.Controls.Add(dtpDateFrom)

        Dim lblDateTo As New Label()
        lblDateTo.Text = "到:"
        lblDateTo.Location = New Point(180, 170)
        lblDateTo.Size = New Size(30, 23)
        Me.Controls.Add(lblDateTo)

        dtpDateTo = New DateTimePicker()
        dtpDateTo.Location = New Point(220, 168)
        dtpDateTo.Size = New Size(120, 23)
        dtpDateTo.Format = DateTimePickerFormat.Short
        dtpDateTo.Enabled = False
        Me.Controls.Add(dtpDateTo)

        ' 按钮
        btnSearch = New Button()
        btnSearch.Text = "搜索"
        btnSearch.Location = New Point(100, 220)
        btnSearch.Size = New Size(80, 30)
        btnSearch.DialogResult = DialogResult.OK
        Me.Controls.Add(btnSearch)

        btnClear = New Button()
        btnClear.Text = "清空"
        btnClear.Location = New Point(190, 220)
        btnClear.Size = New Size(80, 30)
        Me.Controls.Add(btnClear)

        btnCancel = New Button()
        btnCancel.Text = "取消"
        btnCancel.Location = New Point(280, 220)
        btnCancel.Size = New Size(80, 30)
        btnCancel.DialogResult = DialogResult.Cancel
        Me.Controls.Add(btnCancel)

        ' 事件处理
        AddHandler chkDateRange.CheckedChanged, AddressOf ChkDateRange_CheckedChanged
        AddHandler btnSearch.Click, AddressOf BtnSearch_Click
        AddHandler btnClear.Click, AddressOf BtnClear_Click

        Me.AcceptButton = btnSearch
        Me.CancelButton = btnCancel
    End Sub

    Private Sub LoadCustomers()
        Try
            Dim query As String = "SELECT id, customer_name FROM customers WHERE is_active = TRUE ORDER BY customer_name"
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)

            ' 添加"全部"选项
            Dim allRow As DataRow = dt.NewRow()
            allRow("id") = DBNull.Value
            allRow("customer_name") = "-- 全部客户 --"
            dt.Rows.InsertAt(allRow, 0)

            cmbCustomer.DisplayMember = "customer_name"
            cmbCustomer.ValueMember = "id"
            cmbCustomer.DataSource = dt

        Catch ex As Exception
            MessageBox.Show($"加载客户数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetupOrderStatus()
        Dim statusItems As New List(Of KeyValuePair(Of String, String)) From {
            New KeyValuePair(Of String, String)("", "-- 全部状态 --"),
            New KeyValuePair(Of String, String)("pending", "待处理"),
            New KeyValuePair(Of String, String)("confirmed", "已确认"),
            New KeyValuePair(Of String, String)("in_production", "生产中"),
            New KeyValuePair(Of String, String)("ready", "待发货"),
            New KeyValuePair(Of String, String)("shipped", "已发货"),
            New KeyValuePair(Of String, String)("delivered", "已交货"),
            New KeyValuePair(Of String, String)("completed", "已完成"),
            New KeyValuePair(Of String, String)("cancelled", "已取消")
        }

        cmbOrderStatus.DisplayMember = "Value"
        cmbOrderStatus.ValueMember = "Key"
        cmbOrderStatus.DataSource = statusItems
    End Sub

    Private Sub ChkDateRange_CheckedChanged(sender As Object, e As EventArgs)
        dtpDateFrom.Enabled = chkDateRange.Checked
        dtpDateTo.Enabled = chkDateRange.Checked
    End Sub

    Private Sub BtnSearch_Click(sender As Object, e As EventArgs)
        SearchCriteria = New Dictionary(Of String, Object)

        ' 订单编号
        If Not String.IsNullOrWhiteSpace(txtOrderNumber.Text) Then
            SearchCriteria("order_number") = txtOrderNumber.Text.Trim()
        End If

        ' 客户
        If cmbCustomer.SelectedValue IsNot Nothing AndAlso Not IsDBNull(cmbCustomer.SelectedValue) Then
            SearchCriteria("customer_id") = cmbCustomer.SelectedValue
        End If

        ' 订单状态
        If Not String.IsNullOrWhiteSpace(cmbOrderStatus.SelectedValue?.ToString()) Then
            SearchCriteria("order_status") = cmbOrderStatus.SelectedValue
        End If

        ' 日期范围
        If chkDateRange.Checked Then
            SearchCriteria("date_from") = dtpDateFrom.Value.Date
            SearchCriteria("date_to") = dtpDateTo.Value.Date

            If dtpDateTo.Value < dtpDateFrom.Value Then
                MessageBox.Show("结束日期不能早于开始日期！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                dtpDateTo.Focus()
                Return
            End If
        End If
    End Sub

    Private Sub BtnClear_Click(sender As Object, e As EventArgs)
        ClearForm()
    End Sub

    Private Sub ClearForm()
        txtOrderNumber.Clear()
        If cmbCustomer.Items.Count > 0 Then cmbCustomer.SelectedIndex = 0
        If cmbOrderStatus.Items.Count > 0 Then cmbOrderStatus.SelectedIndex = 0
        chkDateRange.Checked = False
        dtpDateFrom.Value = DateTime.Now.AddMonths(-1)
        dtpDateTo.Value = DateTime.Now
    End Sub
End Class
