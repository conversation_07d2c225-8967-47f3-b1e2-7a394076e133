-- 库存盘点相关数据库表创建脚本
-- 执行前请确保已连接到ERPSystem数据库

USE ERPSystem;

-- 创建库存盘点主表
CREATE TABLE IF NOT EXISTS stock_counts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    count_number VARCHAR(50) NOT NULL UNIQUE COMMENT '盘点单号',
    count_date DATE NOT NULL COMMENT '盘点日期',
    count_person VARCHAR(100) NOT NULL COMMENT '盘点人',
    count_status ENUM('planned', 'in_progress', 'completed', 'cancelled') DEFAULT 'planned' COMMENT '盘点状态',
    remarks TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_count_number (count_number),
    INDEX idx_count_date (count_date),
    INDEX idx_count_status (count_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存盘点主表';

-- 创建库存盘点明细表
CREATE TABLE IF NOT EXISTS stock_count_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stock_count_id INT NOT NULL COMMENT '盘点单ID',
    material_id INT NOT NULL COMMENT '物料ID',
    location_id INT NOT NULL COMMENT '库位ID',
    book_quantity DECIMAL(15,3) NOT NULL DEFAULT 0 COMMENT '账面数量',
    actual_quantity DECIMAL(15,3) NOT NULL DEFAULT 0 COMMENT '实盘数量',
    remarks TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (stock_count_id) REFERENCES stock_counts(id) ON DELETE CASCADE,
    FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
    UNIQUE KEY uk_count_material_location (stock_count_id, material_id, location_id),
    INDEX idx_stock_count_id (stock_count_id),
    INDEX idx_material_id (material_id),
    INDEX idx_location_id (location_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存盘点明细表';

-- 创建盘点处理历史表（可选，用于记录预警处理历史）
CREATE TABLE IF NOT EXISTS stock_alert_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_id INT NOT NULL COMMENT '物料ID',
    location_id INT NOT NULL COMMENT '库位ID',
    alert_type ENUM('safety_stock', 'out_of_stock') NOT NULL COMMENT '预警类型',
    alert_level ENUM('warning', 'critical') NOT NULL COMMENT '预警级别',
    current_stock DECIMAL(15,3) NOT NULL COMMENT '当前库存',
    safety_stock DECIMAL(15,3) NOT NULL COMMENT '安全库存',
    handled_by VARCHAR(100) COMMENT '处理人',
    handle_method VARCHAR(200) COMMENT '处理方式',
    handle_remarks TEXT COMMENT '处理备注',
    handled_at TIMESTAMP NULL COMMENT '处理时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
    INDEX idx_material_location (material_id, location_id),
    INDEX idx_alert_type (alert_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存预警处理历史表';

-- 插入测试数据
INSERT INTO stock_counts (count_number, count_date, count_person, count_status, remarks) VALUES
('SC202501010001', '2025-01-01', '张三', 'completed', '年度盘点'),
('SC202501020001', '2025-01-02', '李四', 'in_progress', '月度盘点'),
('SC202501030001', '2025-01-03', '王五', 'planned', '临时盘点');

-- 为测试盘点单添加明细数据（假设物料和库位已存在）
-- 注意：这里的material_id和location_id需要根据实际数据调整
INSERT INTO stock_count_details (stock_count_id, material_id, location_id, book_quantity, actual_quantity, remarks) 
SELECT 
    1 as stock_count_id,
    m.id as material_id,
    l.id as location_id,
    COALESCE(i.current_stock, 0) as book_quantity,
    COALESCE(i.current_stock, 0) + (RAND() * 20 - 10) as actual_quantity,  -- 模拟实盘数量（有随机差异）
    '测试数据' as remarks
FROM materials m
CROSS JOIN locations l
LEFT JOIN inventory i ON m.id = i.material_id AND l.id = i.location_id
WHERE m.is_active = TRUE AND l.is_active = TRUE
LIMIT 10;

-- 创建视图：库存预警视图
CREATE OR REPLACE VIEW v_safety_stock_alerts AS
SELECT 
    m.id as material_id,
    m.material_code,
    m.material_name,
    l.id as location_id,
    l.location_code,
    l.location_name,
    i.current_stock,
    m.safety_stock,
    (m.safety_stock - i.current_stock) as shortage_quantity,
    CASE 
        WHEN i.current_stock <= m.safety_stock * 0.5 THEN 'critical'
        WHEN i.current_stock <= m.safety_stock THEN 'warning'
        ELSE 'normal'
    END as alert_level,
    m.unit,
    m.standard_price,
    ((m.safety_stock - i.current_stock) * m.standard_price) as shortage_value,
    i.last_updated
FROM inventory i
INNER JOIN materials m ON i.material_id = m.id
INNER JOIN locations l ON i.location_id = l.id
WHERE i.current_stock <= m.safety_stock 
AND m.is_active = TRUE 
AND l.is_active = TRUE;

-- 创建存储过程：自动生成盘点单明细
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS sp_generate_count_details(
    IN p_count_id INT,
    IN p_location_id INT DEFAULT NULL
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_material_id INT;
    DECLARE v_location_id INT;
    DECLARE v_current_stock DECIMAL(15,3);
    
    -- 声明游标
    DECLARE cur_inventory CURSOR FOR
        SELECT i.material_id, i.location_id, i.current_stock
        FROM inventory i
        INNER JOIN materials m ON i.material_id = m.id
        INNER JOIN locations l ON i.location_id = l.id
        WHERE m.is_active = TRUE 
        AND l.is_active = TRUE
        AND (p_location_id IS NULL OR i.location_id = p_location_id);
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开始事务
    START TRANSACTION;
    
    -- 清除已存在的明细
    DELETE FROM stock_count_details WHERE stock_count_id = p_count_id;
    
    -- 打开游标
    OPEN cur_inventory;
    
    read_loop: LOOP
        FETCH cur_inventory INTO v_material_id, v_location_id, v_current_stock;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 插入盘点明细
        INSERT INTO stock_count_details (stock_count_id, material_id, location_id, book_quantity, actual_quantity)
        VALUES (p_count_id, v_material_id, v_location_id, v_current_stock, 0);
        
    END LOOP;
    
    CLOSE cur_inventory;
    
    -- 提交事务
    COMMIT;
    
END //
DELIMITER ;

-- 创建触发器：盘点完成后自动更新库存（可选）
DELIMITER //
CREATE TRIGGER IF NOT EXISTS tr_stock_count_completed
AFTER UPDATE ON stock_counts
FOR EACH ROW
BEGIN
    -- 当盘点状态变为已完成时，可以选择是否自动更新库存
    -- 这里只是示例，实际使用时需要根据业务需求决定
    IF NEW.count_status = 'completed' AND OLD.count_status != 'completed' THEN
        -- 记录日志或发送通知
        INSERT INTO stock_alert_history (material_id, location_id, alert_type, alert_level, current_stock, safety_stock, handled_by, handle_method, handled_at)
        SELECT 
            scd.material_id,
            scd.location_id,
            'safety_stock' as alert_type,
            'warning' as alert_level,
            scd.actual_quantity as current_stock,
            m.safety_stock,
            NEW.count_person as handled_by,
            CONCAT('盘点调整：', NEW.count_number) as handle_method,
            NOW() as handled_at
        FROM stock_count_details scd
        INNER JOIN materials m ON scd.material_id = m.id
        WHERE scd.stock_count_id = NEW.id
        AND scd.actual_quantity != scd.book_quantity;
    END IF;
END //
DELIMITER ;

-- 授权语句（根据需要调整）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.stock_counts TO 'erp_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.stock_count_details TO 'erp_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.stock_alert_history TO 'erp_user'@'localhost';

-- 查询语句示例
-- 查看当前所有预警
-- SELECT * FROM v_safety_stock_alerts ORDER BY alert_level DESC, shortage_quantity DESC;

-- 查看盘点单及其统计信息
-- SELECT 
--     sc.*,
--     COUNT(scd.id) as detail_count,
--     SUM(CASE WHEN scd.actual_quantity != scd.book_quantity THEN 1 ELSE 0 END) as difference_count
-- FROM stock_counts sc
-- LEFT JOIN stock_count_details scd ON sc.id = scd.stock_count_id
-- GROUP BY sc.id
-- ORDER BY sc.created_at DESC;
