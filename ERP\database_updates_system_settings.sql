-- 系统设置相关数据库表创建脚本
-- 执行前请确保已连接到ERPSystem数据库

USE ERPSystem;

-- 创建系统参数表
CREATE TABLE IF NOT EXISTS system_params (
    id INT AUTO_INCREMENT PRIMARY KEY,
    param_key VARCHAR(100) NOT NULL UNIQUE COMMENT '参数键',
    param_value TEXT NOT NULL COMMENT '参数值',
    param_type ENUM('string', 'number', 'boolean', 'date') DEFAULT 'string' COMMENT '参数类型',
    description VARCHAR(500) COMMENT '参数描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_param_key (param_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统参数表';

-- 创建业务参数表
CREATE TABLE IF NOT EXISTS business_params (
    id INT AUTO_INCREMENT PRIMARY KEY,
    param_key VARCHAR(100) NOT NULL COMMENT '参数键',
    param_value TEXT NOT NULL COMMENT '参数值',
    category VARCHAR(50) NOT NULL COMMENT '参数分类',
    description VARCHAR(500) COMMENT '参数描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_category_key (category, param_key),
    INDEX idx_category (category),
    INDEX idx_param_key (param_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务参数表';

-- 创建数据字典表
CREATE TABLE IF NOT EXISTS data_dictionary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dict_type VARCHAR(50) NOT NULL COMMENT '字典类型',
    dict_code VARCHAR(50) NOT NULL COMMENT '字典编码',
    dict_name VARCHAR(100) NOT NULL COMMENT '字典名称',
    dict_value VARCHAR(200) COMMENT '字典值',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_type_code (dict_type, dict_code),
    INDEX idx_dict_type (dict_type),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据字典表';

-- 插入默认系统参数
INSERT INTO system_params (param_key, param_value, param_type, description, is_active) VALUES
('system_name', 'ERP库存管理系统', 'string', '系统名称', TRUE),
('system_version', '1.0.0', 'string', '系统版本', TRUE),
('company_name', '您的公司名称', 'string', '公司名称', TRUE),
('company_address', '公司地址', 'string', '公司地址', TRUE),
('company_phone', '公司电话', 'string', '公司电话', TRUE),
('company_email', '<EMAIL>', 'string', '公司邮箱', TRUE),
('default_currency', 'CNY', 'string', '默认货币', TRUE),
('decimal_places', '2', 'number', '小数位数', TRUE),
('date_format', 'yyyy-MM-dd', 'string', '日期格式', TRUE),
('session_timeout', '30', 'number', '会话超时时间(分钟)', TRUE),
('max_login_attempts', '5', 'number', '最大登录尝试次数', TRUE),
('password_min_length', '6', 'number', '密码最小长度', TRUE),
('auto_backup_enabled', 'false', 'boolean', '是否启用自动备份', TRUE),
('backup_retention_days', '30', 'number', '备份保留天数', TRUE),
('email_smtp_server', '', 'string', '邮件SMTP服务器', TRUE),
('email_smtp_port', '587', 'number', '邮件SMTP端口', TRUE),
('email_username', '', 'string', '邮件用户名', TRUE),
('email_password', '', 'string', '邮件密码', TRUE),
('enable_audit_log', 'true', 'boolean', '是否启用审计日志', TRUE),
('log_retention_days', '90', 'number', '日志保留天数', TRUE)
ON DUPLICATE KEY UPDATE 
    param_value = VALUES(param_value),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- 插入默认业务参数
INSERT INTO business_params (param_key, param_value, category, description) VALUES
('default_tax_rate', '0.13', '财务管理', '默认税率'),
('auto_generate_order_number', 'true', '订单管理', '是否自动生成订单编号'),
('order_number_prefix', 'SO', '订单管理', '订单编号前缀'),
('inbound_number_prefix', 'IN', '库存管理', '入库单编号前缀'),
('outbound_number_prefix', 'OUT', '库存管理', '出库单编号前缀'),
('count_number_prefix', 'SC', '库存管理', '盘点单编号前缀'),
('safety_stock_alert_enabled', 'true', '库存管理', '是否启用安全库存预警'),
('low_stock_threshold_percent', '50', '库存管理', '低库存预警阈值百分比'),
('auto_create_customer_code', 'true', '客户管理', '是否自动生成客户编码'),
('customer_code_prefix', 'C', '客户管理', '客户编码前缀'),
('auto_create_supplier_code', 'true', '供应商管理', '是否自动生成供应商编码'),
('supplier_code_prefix', 'S', '供应商管理', '供应商编码前缀'),
('auto_create_material_code', 'true', '物料管理', '是否自动生成物料编码'),
('material_code_prefix', 'M', '物料管理', '物料编码前缀'),
('default_location_type', 'storage', '库存管理', '默认库位类型'),
('enable_batch_management', 'false', '库存管理', '是否启用批次管理'),
('enable_serial_number', 'false', '库存管理', '是否启用序列号管理'),
('default_payment_terms', '30天', '财务管理', '默认付款条件'),
('credit_limit_check_enabled', 'true', '财务管理', '是否启用信用额度检查'),
('auto_invoice_generation', 'false', '财务管理', '是否自动生成发票')
ON DUPLICATE KEY UPDATE 
    param_value = VALUES(param_value),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- 插入默认数据字典
INSERT INTO data_dictionary (dict_type, dict_code, dict_name, dict_value, sort_order, is_active) VALUES
-- 订单状态
('order_status', 'pending', '待处理', 'pending', 1, TRUE),
('order_status', 'confirmed', '已确认', 'confirmed', 2, TRUE),
('order_status', 'in_production', '生产中', 'in_production', 3, TRUE),
('order_status', 'ready', '待发货', 'ready', 4, TRUE),
('order_status', 'shipped', '已发货', 'shipped', 5, TRUE),
('order_status', 'delivered', '已交货', 'delivered', 6, TRUE),
('order_status', 'completed', '已完成', 'completed', 7, TRUE),
('order_status', 'cancelled', '已取消', 'cancelled', 8, TRUE),

-- 库位类型
('location_type', 'storage', '存储', 'storage', 1, TRUE),
('location_type', 'wip', '在制品', 'wip', 2, TRUE),
('location_type', 'qc', '质检', 'qc', 3, TRUE),
('location_type', 'shipping', '发货', 'shipping', 4, TRUE),
('location_type', 'receiving', '收货', 'receiving', 5, TRUE),

-- 入库类型
('inbound_type', 'purchase', '采购入库', 'purchase', 1, TRUE),
('inbound_type', 'return', '退货入库', 'return', 2, TRUE),
('inbound_type', 'transfer', '调拨入库', 'transfer', 3, TRUE),
('inbound_type', 'adjustment', '调整入库', 'adjustment', 4, TRUE),
('inbound_type', 'production', '生产入库', 'production', 5, TRUE),

-- 出库类型
('outbound_type', 'sales', '销售出库', 'sales', 1, TRUE),
('outbound_type', 'return', '退货出库', 'return', 2, TRUE),
('outbound_type', 'transfer', '调拨出库', 'transfer', 3, TRUE),
('outbound_type', 'adjustment', '调整出库', 'adjustment', 4, TRUE),
('outbound_type', 'production', '生产出库', 'production', 5, TRUE),

-- 物料类别
('material_category', 'raw_material', '原材料', 'raw_material', 1, TRUE),
('material_category', 'semi_finished', '半成品', 'semi_finished', 2, TRUE),
('material_category', 'finished_goods', '成品', 'finished_goods', 3, TRUE),
('material_category', 'consumables', '耗材', 'consumables', 4, TRUE),
('material_category', 'spare_parts', '备件', 'spare_parts', 5, TRUE),

-- 计量单位
('unit', 'pcs', '个', 'pcs', 1, TRUE),
('unit', 'kg', '千克', 'kg', 2, TRUE),
('unit', 'g', '克', 'g', 3, TRUE),
('unit', 'm', '米', 'm', 4, TRUE),
('unit', 'cm', '厘米', 'cm', 5, TRUE),
('unit', 'mm', '毫米', 'mm', 6, TRUE),
('unit', 'l', '升', 'l', 7, TRUE),
('unit', 'ml', '毫升', 'ml', 8, TRUE),
('unit', 'box', '箱', 'box', 9, TRUE),
('unit', 'pack', '包', 'pack', 10, TRUE),

-- 用户角色
('user_role', 'admin', '系统管理员', 'admin', 1, TRUE),
('user_role', 'manager', '部门经理', 'manager', 2, TRUE),
('user_role', 'user', '普通用户', 'user', 3, TRUE),
('user_role', 'readonly', '只读用户', 'readonly', 4, TRUE),

-- 盘点状态
('count_status', 'planned', '计划中', 'planned', 1, TRUE),
('count_status', 'in_progress', '进行中', 'in_progress', 2, TRUE),
('count_status', 'completed', '已完成', 'completed', 3, TRUE),
('count_status', 'cancelled', '已取消', 'cancelled', 4, TRUE),

-- 预警级别
('alert_level', 'info', '信息', 'info', 1, TRUE),
('alert_level', 'warning', '警告', 'warning', 2, TRUE),
('alert_level', 'critical', '严重', 'critical', 3, TRUE),

-- 货币类型
('currency', 'CNY', '人民币', 'CNY', 1, TRUE),
('currency', 'USD', '美元', 'USD', 2, TRUE),
('currency', 'EUR', '欧元', 'EUR', 3, TRUE),
('currency', 'JPY', '日元', 'JPY', 4, TRUE)

ON DUPLICATE KEY UPDATE 
    dict_name = VALUES(dict_name),
    dict_value = VALUES(dict_value),
    sort_order = VALUES(sort_order),
    is_active = VALUES(is_active),
    updated_at = CURRENT_TIMESTAMP;

-- 创建视图：系统配置概览
CREATE OR REPLACE VIEW v_system_config AS
SELECT 
    'system_params' as config_type,
    param_key as config_key,
    param_value as config_value,
    description,
    CASE WHEN is_active = 1 THEN '启用' ELSE '禁用' END as status,
    updated_at
FROM system_params
WHERE is_active = TRUE
UNION ALL
SELECT 
    'business_params' as config_type,
    CONCAT(category, '.', param_key) as config_key,
    param_value as config_value,
    description,
    '启用' as status,
    updated_at
FROM business_params
ORDER BY config_type, config_key;

-- 创建存储过程：获取系统参数值
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS sp_get_system_param(
    IN p_param_key VARCHAR(100),
    OUT p_param_value TEXT
)
BEGIN
    SELECT param_value INTO p_param_value
    FROM system_params 
    WHERE param_key = p_param_key AND is_active = TRUE
    LIMIT 1;
END //
DELIMITER ;

-- 创建存储过程：获取业务参数值
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS sp_get_business_param(
    IN p_category VARCHAR(50),
    IN p_param_key VARCHAR(100),
    OUT p_param_value TEXT
)
BEGIN
    SELECT param_value INTO p_param_value
    FROM business_params 
    WHERE category = p_category AND param_key = p_param_key
    LIMIT 1;
END //
DELIMITER ;

-- 创建函数：获取数据字典显示名称
DELIMITER //
CREATE FUNCTION IF NOT EXISTS fn_get_dict_name(
    p_dict_type VARCHAR(50),
    p_dict_code VARCHAR(50)
) RETURNS VARCHAR(100)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_dict_name VARCHAR(100) DEFAULT p_dict_code;
    
    SELECT dict_name INTO v_dict_name
    FROM data_dictionary 
    WHERE dict_type = p_dict_type 
    AND dict_code = p_dict_code 
    AND is_active = TRUE
    LIMIT 1;
    
    RETURN v_dict_name;
END //
DELIMITER ;

-- 授权语句（根据需要调整）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.system_params TO 'erp_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.business_params TO 'erp_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.data_dictionary TO 'erp_user'@'localhost';

-- 查询语句示例
-- 查看所有系统配置
-- SELECT * FROM v_system_config;

-- 获取系统参数示例
-- CALL sp_get_system_param('default_tax_rate', @tax_rate);
-- SELECT @tax_rate;

-- 获取业务参数示例
-- CALL sp_get_business_param('财务管理', 'default_tax_rate', @tax_rate);
-- SELECT @tax_rate;

-- 使用字典函数示例
-- SELECT fn_get_dict_name('order_status', 'pending') as status_name;
