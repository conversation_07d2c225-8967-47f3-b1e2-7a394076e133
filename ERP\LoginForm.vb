Imports System.Windows.Forms
Imports System.Drawing
Imports FontAwesome.Sharp

''' <summary>
''' 登录窗体
''' </summary>
Public Class LoginForm
    Inherits Form

    Private txtUsername As TextBox
    Private txtPassword As TextBox
    Private btnLogin As Button
    Private btnCancel As Button
    Private lblUsername As Label
    Private lblPassword As Label
    Private lblTitle As Label

    Public Sub New()
        InitializeComponent()

        ' 应用现代化主题
        UIThemeManager.ApplyTheme(Me)
        UIThemeManager.SetAutoScale(Me)

        ' 美化登录按钮
        UIThemeManager.ApplyPrimaryButtonTheme(btnLogin)
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "ERP库存管理系统 - 登录"
        Me.Size = New Size(400, 300)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False

        ' 标题标签
        lblTitle = New Label()
        lblTitle.Text = "ERP库存管理系统"
        lblTitle.Font = New Font("微软雅黑", 16, FontStyle.Bold)
        lblTitle.ForeColor = Color.DarkBlue
        lblTitle.Size = New Size(300, 30)
        lblTitle.Location = New Point(50, 30)
        lblTitle.TextAlign = ContentAlignment.MiddleCenter
        Me.Controls.Add(lblTitle)

        ' 用户名标签
        lblUsername = New Label()
        lblUsername.Text = "用户名:"
        lblUsername.Size = New Size(60, 20)
        lblUsername.Location = New Point(50, 80)
        Me.Controls.Add(lblUsername)

        ' 用户名文本框
        txtUsername = New TextBox()
        txtUsername.Size = New Size(200, 25)
        txtUsername.Location = New Point(120, 78)
        txtUsername.Font = New Font("微软雅黑", 10)
        Me.Controls.Add(txtUsername)

        ' 密码标签
        lblPassword = New Label()
        lblPassword.Text = "密码:"
        lblPassword.Size = New Size(60, 20)
        lblPassword.Location = New Point(50, 120)
        Me.Controls.Add(lblPassword)

        ' 密码文本框
        txtPassword = New TextBox()
        txtPassword.Size = New Size(200, 25)
        txtPassword.Location = New Point(120, 118)
        txtPassword.Font = New Font("微软雅黑", 10)
        txtPassword.PasswordChar = "*"c
        Me.Controls.Add(txtPassword)

        ' 登录按钮
        btnLogin = New Button()
        btnLogin.Text = "登录"
        btnLogin.Size = New Size(80, 30)
        btnLogin.Location = New Point(120, 170)
        btnLogin.Font = New Font("微软雅黑", 10)
        btnLogin.BackColor = Color.LightBlue
        AddHandler btnLogin.Click, AddressOf btnLogin_Click
        Me.Controls.Add(btnLogin)

        ' 取消按钮
        btnCancel = New Button()
        btnCancel.Text = "取消"
        btnCancel.Size = New Size(80, 30)
        btnCancel.Location = New Point(220, 170)
        btnCancel.Font = New Font("微软雅黑", 10)
        AddHandler btnCancel.Click, AddressOf btnCancel_Click
        Me.Controls.Add(btnCancel)

        ' 设置默认值
        txtUsername.Text = "admin"
        txtPassword.Text = "admin123"

        ' 设置Tab顺序
        txtUsername.TabIndex = 0
        txtPassword.TabIndex = 1
        btnLogin.TabIndex = 2
        btnCancel.TabIndex = 3

        ' 设置默认按钮
        Me.AcceptButton = btnLogin
        Me.CancelButton = btnCancel
    End Sub

    Private Sub btnLogin_Click(sender As Object, e As EventArgs)
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("请输入用户名", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return
        End If

        If String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("请输入密码", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return
        End If

        Try
            ' 验证用户登录
            Dim userInfo As Dictionary(Of String, Object) = ValidateUser(txtUsername.Text.Trim(), txtPassword.Text)
            If userInfo IsNot Nothing Then
                ' 设置用户会话信息
                UserSession.SetCurrentUser(
                    Convert.ToInt32(userInfo("id")),
                    userInfo("username").ToString(),
                    userInfo("role").ToString(),
                    userInfo("full_name").ToString(),
                    userInfo("email").ToString(),
                    Convert.ToBoolean(userInfo("is_active"))
                )

                ' 登录成功，打开主窗体
                Dim mainForm As New MainForm()
                Me.Hide()
                mainForm.ShowDialog()
                Me.Close()
            Else
                MessageBox.Show("用户名或密码错误", "登录失败", MessageBoxButtons.OK, MessageBoxIcon.Error)
                txtPassword.Clear()
                txtUsername.Focus()
            End If
        Catch ex As Exception
            MessageBox.Show($"登录时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        Application.Exit()
    End Sub

    Private Function ValidateUser(username As String, password As String) As Dictionary(Of String, Object)
        Try
            Dim sql = "SELECT id, username, role, full_name, email, is_active FROM users WHERE username = @username AND password = @password AND is_active = TRUE"
            Dim parameters As New Dictionary(Of String, Object) From {
                {"@username", username},
                {"@password", password}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(sql, parameters)
            If dt.Rows.Count > 0 Then
                Dim row As DataRow = dt.Rows(0)
                Return New Dictionary(Of String, Object) From {
                    {"id", row("id")},
                    {"username", row("username")},
                    {"role", row("role")},
                    {"full_name", If(row("full_name") Is DBNull.Value, "", row("full_name"))},
                    {"email", If(row("email") Is DBNull.Value, "", row("email"))},
                    {"is_active", row("is_active")}
                }
            Else
                Return Nothing
            End If
        Catch ex As Exception
            Throw New Exception($"验证用户时发生错误: {ex.Message}")
        End Try
    End Function
End Class
