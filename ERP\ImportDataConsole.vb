Module ImportDataConsole
    Sub Main()
        Console.WriteLine("=== ERP系统数据导入工具 ===")
        Console.WriteLine()
        
        Try
            ' 导入模拟数据
            DataImporter.ImportSimulationData()
            
            Console.WriteLine()
            Console.WriteLine("数据导入完成！按任意键退出...")
            Console.ReadKey()
            
        Catch ex As Exception
            Console.WriteLine($"程序执行出错: {ex.Message}")
            Console.WriteLine("按任意键退出...")
            Console.ReadKey()
        End Try
    End Sub
End Module
