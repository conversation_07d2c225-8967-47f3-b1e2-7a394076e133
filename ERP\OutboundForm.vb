Imports System.Windows.Forms
Imports System.Data

''' <summary>
''' 出库管理窗体
''' </summary>
Public Class OutboundForm
    Inherits Form

    Private dgvOutbound As DataGridView
    Private txtOutboundNumber As TextBox
    Private dtpOutboundDate As DateTimePicker
    Private cmbCustomer As ComboBox
    Private cmbOutboundType As ComboBox
    Private cmbStatus As ComboBox
    Private txtRemarks As TextBox
    Private txtTotalAmount As TextBox

    Private btnNew As Button
    Private btnEdit As Button
    Private btnSave As Button
    Private btnCancel As Button
    Private btnDelete As Button
    Private btnRefresh As Button
    Private btnViewDetails As Button

    Private isEditMode As Boolean = False
    Private currentOutboundId As Integer = 0

    Public Sub New()
        InitializeComponent()
        LoadCustomers()
        LoadOutboundOrders()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "出库管理"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterParent

        ' 创建数据网格
        dgvOutbound = New DataGridView()
        dgvOutbound.Location = New Point(10, 10)
        dgvOutbound.Size = New Size(800, 400)
        dgvOutbound.ReadOnly = True
        dgvOutbound.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvOutbound.MultiSelect = False
        dgvOutbound.AllowUserToAddRows = False
        dgvOutbound.AllowUserToDeleteRows = False
        AddHandler dgvOutbound.SelectionChanged, AddressOf dgvOutbound_SelectionChanged
        Me.Controls.Add(dgvOutbound)

        ' 创建输入控件
        CreateInputControls()

        ' 创建按钮
        CreateButtons()

        ' 设置初始状态
        SetFormMode(False)
    End Sub

    Private Sub CreateInputControls()
        Dim startX As Integer = 830
        Dim startY As Integer = 30
        Dim labelWidth As Integer = 80
        Dim textWidth As Integer = 200
        Dim rowHeight As Integer = 35

        ' 出库单号
        Dim lblOutboundNumber As New Label()
        lblOutboundNumber.Text = "出库单号:"
        lblOutboundNumber.Location = New Point(startX, startY)
        lblOutboundNumber.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblOutboundNumber)

        txtOutboundNumber = New TextBox()
        txtOutboundNumber.Location = New Point(startX + labelWidth + 10, startY)
        txtOutboundNumber.Size = New Size(textWidth, 25)
        txtOutboundNumber.ReadOnly = True
        Me.Controls.Add(txtOutboundNumber)

        ' 出库日期
        startY += rowHeight
        Dim lblOutboundDate As New Label()
        lblOutboundDate.Text = "出库日期:"
        lblOutboundDate.Location = New Point(startX, startY)
        lblOutboundDate.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblOutboundDate)

        dtpOutboundDate = New DateTimePicker()
        dtpOutboundDate.Location = New Point(startX + labelWidth + 10, startY)
        dtpOutboundDate.Size = New Size(textWidth, 25)
        dtpOutboundDate.Format = DateTimePickerFormat.Short
        Me.Controls.Add(dtpOutboundDate)

        ' 客户
        startY += rowHeight
        Dim lblCustomer As New Label()
        lblCustomer.Text = "客户:"
        lblCustomer.Location = New Point(startX, startY)
        lblCustomer.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblCustomer)

        cmbCustomer = New ComboBox()
        cmbCustomer.Location = New Point(startX + labelWidth + 10, startY)
        cmbCustomer.Size = New Size(textWidth, 25)
        cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbCustomer)

        ' 出库类型
        startY += rowHeight
        Dim lblOutboundType As New Label()
        lblOutboundType.Text = "出库类型:"
        lblOutboundType.Location = New Point(startX, startY)
        lblOutboundType.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblOutboundType)

        cmbOutboundType = New ComboBox()
        cmbOutboundType.Location = New Point(startX + labelWidth + 10, startY)
        cmbOutboundType.Size = New Size(textWidth, 25)
        cmbOutboundType.DropDownStyle = ComboBoxStyle.DropDownList
        cmbOutboundType.Items.AddRange({"sales", "return", "transfer", "adjustment"})
        cmbOutboundType.SelectedIndex = 0
        Me.Controls.Add(cmbOutboundType)

        ' 状态
        startY += rowHeight
        Dim lblStatus As New Label()
        lblStatus.Text = "状态:"
        lblStatus.Location = New Point(startX, startY)
        lblStatus.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblStatus)

        cmbStatus = New ComboBox()
        cmbStatus.Location = New Point(startX + labelWidth + 10, startY)
        cmbStatus.Size = New Size(textWidth, 25)
        cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList
        cmbStatus.Items.AddRange({"pending", "confirmed", "completed", "cancelled"})
        cmbStatus.SelectedIndex = 0
        Me.Controls.Add(cmbStatus)

        ' 总金额
        startY += rowHeight
        Dim lblTotalAmount As New Label()
        lblTotalAmount.Text = "总金额:"
        lblTotalAmount.Location = New Point(startX, startY)
        lblTotalAmount.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblTotalAmount)

        txtTotalAmount = New TextBox()
        txtTotalAmount.Location = New Point(startX + labelWidth + 10, startY)
        txtTotalAmount.Size = New Size(textWidth, 25)
        txtTotalAmount.Text = "0"
        Me.Controls.Add(txtTotalAmount)

        ' 备注
        startY += rowHeight
        Dim lblRemarks As New Label()
        lblRemarks.Text = "备注:"
        lblRemarks.Location = New Point(startX, startY)
        lblRemarks.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblRemarks)

        txtRemarks = New TextBox()
        txtRemarks.Location = New Point(startX + labelWidth + 10, startY)
        txtRemarks.Size = New Size(textWidth, 80)
        txtRemarks.Multiline = True
        Me.Controls.Add(txtRemarks)
    End Sub

    Private Sub CreateButtons()
        Dim buttonY As Integer = 450
        Dim buttonWidth As Integer = 80
        Dim buttonHeight As Integer = 30
        Dim buttonSpacing As Integer = 90

        btnNew = New Button()
        btnNew.Text = "新增"
        btnNew.Location = New Point(10, buttonY)
        btnNew.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnNew.Click, AddressOf btnNew_Click
        Me.Controls.Add(btnNew)

        btnEdit = New Button()
        btnEdit.Text = "编辑"
        btnEdit.Location = New Point(10 + buttonSpacing, buttonY)
        btnEdit.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnEdit.Click, AddressOf btnEdit_Click
        Me.Controls.Add(btnEdit)

        btnSave = New Button()
        btnSave.Text = "保存"
        btnSave.Location = New Point(10 + buttonSpacing * 2, buttonY)
        btnSave.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnSave.Click, AddressOf btnSave_Click
        Me.Controls.Add(btnSave)

        btnCancel = New Button()
        btnCancel.Text = "取消"
        btnCancel.Location = New Point(10 + buttonSpacing * 3, buttonY)
        btnCancel.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnCancel.Click, AddressOf btnCancel_Click
        Me.Controls.Add(btnCancel)

        btnDelete = New Button()
        btnDelete.Text = "删除"
        btnDelete.Location = New Point(10 + buttonSpacing * 4, buttonY)
        btnDelete.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnDelete.Click, AddressOf btnDelete_Click
        Me.Controls.Add(btnDelete)

        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(10 + buttonSpacing * 5, buttonY)
        btnRefresh.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnRefresh.Click, AddressOf btnRefresh_Click
        Me.Controls.Add(btnRefresh)

        btnViewDetails = New Button()
        btnViewDetails.Text = "查看明细"
        btnViewDetails.Location = New Point(10 + buttonSpacing * 6, buttonY)
        btnViewDetails.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnViewDetails.Click, AddressOf btnViewDetails_Click
        Me.Controls.Add(btnViewDetails)
    End Sub

    Private Sub LoadCustomers()
        Try
            cmbCustomer.Items.Clear()
            cmbCustomer.Items.Add("")
            
            Dim sql = "SELECT id, customer_name FROM customers WHERE is_active = TRUE ORDER BY customer_name"
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            
            For Each row As DataRow In dt.Rows
                cmbCustomer.Items.Add(New ComboBoxItem(row("customer_name").ToString(), Convert.ToInt32(row("id"))))
            Next
            
            cmbCustomer.SelectedIndex = 0
        Catch ex As Exception
            MessageBox.Show($"加载客户数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadOutboundOrders()
        Try
            Dim sql = "SELECT oo.id, oo.outbound_number AS '出库单号', oo.outbound_date AS '出库日期', " &
                     "c.customer_name AS '客户', oo.outbound_type AS '出库类型', " &
                     "oo.status AS '状态', oo.total_amount AS '总金额' " &
                     "FROM outbound_orders oo " &
                     "LEFT JOIN customers c ON oo.customer_id = c.id " &
                     "ORDER BY oo.outbound_date DESC, oo.outbound_number DESC"
            
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvOutbound.DataSource = dt
            
            ' 隐藏ID列
            If dgvOutbound.Columns.Contains("id") Then
                dgvOutbound.Columns("id").Visible = False
            End If
            
            ' 设置列宽
            dgvOutbound.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        Catch ex As Exception
            MessageBox.Show($"加载出库单数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub dgvOutbound_SelectionChanged(sender As Object, e As EventArgs)
        If dgvOutbound.CurrentRow IsNot Nothing AndAlso Not isEditMode Then
            LoadOutboundDetails()
        End If
    End Sub

    Private Sub LoadOutboundDetails()
        Try
            If dgvOutbound.CurrentRow Is Nothing Then Return
            
            currentOutboundId = Convert.ToInt32(dgvOutbound.CurrentRow.Cells("id").Value)
            
            Dim sql = "SELECT * FROM outbound_orders WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentOutboundId}}
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql, parameters)
            
            If dt.Rows.Count > 0 Then
                Dim row = dt.Rows(0)
                txtOutboundNumber.Text = row("outbound_number").ToString()
                dtpOutboundDate.Value = Convert.ToDateTime(row("outbound_date"))
                
                ' 设置客户
                Dim customerId = If(IsDBNull(row("customer_id")), 0, Convert.ToInt32(row("customer_id")))
                For i As Integer = 0 To cmbCustomer.Items.Count - 1
                    If TypeOf cmbCustomer.Items(i) Is ComboBoxItem Then
                        Dim item = DirectCast(cmbCustomer.Items(i), ComboBoxItem)
                        If item.Value = customerId Then
                            cmbCustomer.SelectedIndex = i
                            Exit For
                        End If
                    End If
                Next
                
                cmbOutboundType.Text = row("outbound_type").ToString()
                cmbStatus.Text = row("status").ToString()
                txtTotalAmount.Text = row("total_amount").ToString()
                txtRemarks.Text = row("remarks").ToString()
            End If
        Catch ex As Exception
            MessageBox.Show($"加载出库单详情时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode

        ' 设置输入控件的启用状态
        dtpOutboundDate.Enabled = editMode
        cmbCustomer.Enabled = editMode
        cmbOutboundType.Enabled = editMode
        cmbStatus.Enabled = editMode
        txtTotalAmount.Enabled = editMode
        txtRemarks.Enabled = editMode

        ' 设置按钮的启用状态
        btnNew.Enabled = Not editMode
        btnEdit.Enabled = Not editMode AndAlso currentOutboundId > 0
        btnDelete.Enabled = Not editMode AndAlso currentOutboundId > 0
        btnSave.Enabled = editMode
        btnCancel.Enabled = editMode
        btnRefresh.Enabled = Not editMode
        btnViewDetails.Enabled = Not editMode AndAlso currentOutboundId > 0

        dgvOutbound.Enabled = Not editMode
    End Sub

    Private Sub ClearForm()
        txtOutboundNumber.Clear()
        dtpOutboundDate.Value = DateTime.Now
        cmbCustomer.SelectedIndex = 0
        cmbOutboundType.SelectedIndex = 0
        cmbStatus.SelectedIndex = 0
        txtTotalAmount.Text = "0"
        txtRemarks.Clear()
        currentOutboundId = 0
    End Sub

    Private Function GenerateOutboundNumber() As String
        Return "OUT" & DateTime.Now.ToString("yyyyMMddHHmmss")
    End Function

    ' 按钮事件处理
    Private Sub btnNew_Click(sender As Object, e As EventArgs)
        ClearForm()
        txtOutboundNumber.Text = GenerateOutboundNumber()
        SetFormMode(True)
        dtpOutboundDate.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs)
        If currentOutboundId <= 0 Then
            MessageBox.Show("请先选择要编辑的出库单", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        SetFormMode(True)
        dtpOutboundDate.Focus()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs)
        If Not ValidateForm() Then Return

        Try
            Dim customerId As Integer? = Nothing
            If cmbCustomer.SelectedIndex > 0 AndAlso TypeOf cmbCustomer.SelectedItem Is ComboBoxItem Then
                customerId = DirectCast(cmbCustomer.SelectedItem, ComboBoxItem).Value
            End If

            If currentOutboundId = 0 Then
                ' 新增
                Dim sql = "INSERT INTO outbound_orders (outbound_number, outbound_date, customer_id, outbound_type, status, total_amount, remarks) " &
                         "VALUES (@outbound_number, @outbound_date, @customer_id, @outbound_type, @status, @total_amount, @remarks)"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@outbound_number", txtOutboundNumber.Text.Trim()},
                    {"@outbound_date", dtpOutboundDate.Value.Date},
                    {"@customer_id", If(customerId, DBNull.Value)},
                    {"@outbound_type", cmbOutboundType.Text},
                    {"@status", cmbStatus.Text},
                    {"@total_amount", Convert.ToDecimal(txtTotalAmount.Text)},
                    {"@remarks", txtRemarks.Text.Trim()}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("出库单保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' 更新
                Dim sql = "UPDATE outbound_orders SET outbound_date = @outbound_date, customer_id = @customer_id, " &
                         "outbound_type = @outbound_type, status = @status, total_amount = @total_amount, " &
                         "remarks = @remarks WHERE id = @id"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@outbound_date", dtpOutboundDate.Value.Date},
                    {"@customer_id", If(customerId, DBNull.Value)},
                    {"@outbound_type", cmbOutboundType.Text},
                    {"@status", cmbStatus.Text},
                    {"@total_amount", Convert.ToDecimal(txtTotalAmount.Text)},
                    {"@remarks", txtRemarks.Text.Trim()},
                    {"@id", currentOutboundId}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("出库单更新成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            SetFormMode(False)
            LoadOutboundOrders()
        Catch ex As Exception
            MessageBox.Show($"保存出库单时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        SetFormMode(False)
        If currentOutboundId > 0 Then
            LoadOutboundDetails()
        Else
            ClearForm()
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs)
        If currentOutboundId <= 0 Then
            MessageBox.Show("请先选择要删除的出库单", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If MessageBox.Show("确定要删除选中的出库单吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim sql = "DELETE FROM outbound_orders WHERE id = @id"
                Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentOutboundId}}

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("出库单删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ClearForm()
                LoadOutboundOrders()
            Catch ex As Exception
                MessageBox.Show($"删除出库单时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs)
        LoadOutboundOrders()
    End Sub

    Private Sub btnViewDetails_Click(sender As Object, e As EventArgs)
        MessageBox.Show("出库明细功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtOutboundNumber.Text) Then
            MessageBox.Show("请输入出库单号", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOutboundNumber.Focus()
            Return False
        End If

        If Not IsNumeric(txtTotalAmount.Text) Then
            MessageBox.Show("请输入有效的总金额", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtTotalAmount.Focus()
            Return False
        End If

        Return True
    End Function
End Class
