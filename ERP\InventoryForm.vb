Imports System.Windows.Forms
Imports System.Data

''' <summary>
''' 库存查询窗体
''' </summary>
Public Class InventoryForm
    Inherits Form

    Private dgvInventory As DataGridView
    Private txtMaterialCodeFilter As TextBox
    Private txtMaterialNameFilter As TextBox
    Private cmbLocationFilter As ComboBox
    Private btnSearch As Button
    Private btnRefresh As Button
    Private btnExport As Button
    Private lblTotalRecords As Label

    Public Sub New()
        InitializeComponent()
        LoadLocationFilter()
        LoadInventory()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "库存查询"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterParent

        ' 创建搜索面板
        CreateSearchPanel()

        ' 创建数据网格
        dgvInventory = New DataGridView()
        dgvInventory.Location = New Point(10, 80)
        dgvInventory.Size = New Size(1160, 500)
        dgvInventory.ReadOnly = True
        dgvInventory.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvInventory.MultiSelect = False
        dgvInventory.AllowUserToAddRows = False
        dgvInventory.AllowUserToDeleteRows = False
        dgvInventory.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        Me.Controls.Add(dgvInventory)

        ' 创建状态栏
        CreateStatusBar()

        ' 创建按钮
        CreateButtons()
    End Sub

    Private Sub CreateSearchPanel()
        Dim panel As New Panel()
        panel.Location = New Point(10, 10)
        panel.Size = New Size(1160, 60)
        panel.BorderStyle = BorderStyle.FixedSingle
        Me.Controls.Add(panel)

        ' 物料编码筛选
        Dim lblMaterialCode As New Label()
        lblMaterialCode.Text = "物料编码:"
        lblMaterialCode.Location = New Point(10, 20)
        lblMaterialCode.Size = New Size(70, 20)
        panel.Controls.Add(lblMaterialCode)

        txtMaterialCodeFilter = New TextBox()
        txtMaterialCodeFilter.Location = New Point(85, 18)
        txtMaterialCodeFilter.Size = New Size(120, 25)
        panel.Controls.Add(txtMaterialCodeFilter)

        ' 物料名称筛选
        Dim lblMaterialName As New Label()
        lblMaterialName.Text = "物料名称:"
        lblMaterialName.Location = New Point(220, 20)
        lblMaterialName.Size = New Size(70, 20)
        panel.Controls.Add(lblMaterialName)

        txtMaterialNameFilter = New TextBox()
        txtMaterialNameFilter.Location = New Point(295, 18)
        txtMaterialNameFilter.Size = New Size(150, 25)
        panel.Controls.Add(txtMaterialNameFilter)

        ' 库位筛选
        Dim lblLocation As New Label()
        lblLocation.Text = "库位:"
        lblLocation.Location = New Point(460, 20)
        lblLocation.Size = New Size(40, 20)
        panel.Controls.Add(lblLocation)

        cmbLocationFilter = New ComboBox()
        cmbLocationFilter.Location = New Point(505, 18)
        cmbLocationFilter.Size = New Size(120, 25)
        cmbLocationFilter.DropDownStyle = ComboBoxStyle.DropDownList
        panel.Controls.Add(cmbLocationFilter)

        ' 搜索按钮
        btnSearch = New Button()
        btnSearch.Text = "搜索"
        btnSearch.Location = New Point(640, 17)
        btnSearch.Size = New Size(70, 27)
        AddHandler btnSearch.Click, AddressOf btnSearch_Click
        panel.Controls.Add(btnSearch)

        ' 刷新按钮
        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(720, 17)
        btnRefresh.Size = New Size(70, 27)
        AddHandler btnRefresh.Click, AddressOf btnRefresh_Click
        panel.Controls.Add(btnRefresh)

        ' 导出按钮
        btnExport = New Button()
        btnExport.Text = "导出"
        btnExport.Location = New Point(800, 17)
        btnExport.Size = New Size(70, 27)
        AddHandler btnExport.Click, AddressOf btnExport_Click
        panel.Controls.Add(btnExport)
    End Sub

    Private Sub CreateStatusBar()
        lblTotalRecords = New Label()
        lblTotalRecords.Text = "总记录数: 0"
        lblTotalRecords.Location = New Point(10, 590)
        lblTotalRecords.Size = New Size(200, 20)
        Me.Controls.Add(lblTotalRecords)
    End Sub

    Private Sub CreateButtons()
        ' 可以添加其他操作按钮，如库存调整等
    End Sub

    Private Sub LoadLocationFilter()
        Try
            cmbLocationFilter.Items.Clear()
            cmbLocationFilter.Items.Add("全部")
            
            Dim sql = "SELECT location_code, location_name FROM locations WHERE is_active = TRUE ORDER BY location_code"
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            
            For Each row As DataRow In dt.Rows
                cmbLocationFilter.Items.Add($"{row("location_code")} - {row("location_name")}")
            Next
            
            cmbLocationFilter.SelectedIndex = 0
        Catch ex As Exception
            MessageBox.Show($"加载库位筛选数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadInventory(Optional whereClause As String = "")
        Try
            Dim sql = "SELECT " &
                     "m.material_code AS '物料编码', " &
                     "m.material_name AS '物料名称', " &
                     "m.drawing_number AS '图号', " &
                     "m.version AS '版本', " &
                     "l.location_code AS '库位编码', " &
                     "l.location_name AS '库位名称', " &
                     "i.current_stock AS '当前库存', " &
                     "i.reserved_stock AS '预留库存', " &
                     "i.available_stock AS '可用库存', " &
                     "m.unit AS '单位', " &
                     "m.safety_stock AS '安全库存', " &
                     "CASE WHEN i.current_stock <= m.safety_stock THEN '是' ELSE '否' END AS '低于安全库存', " &
                     "i.last_updated AS '最后更新时间' " &
                     "FROM inventory i " &
                     "INNER JOIN materials m ON i.material_id = m.id " &
                     "INNER JOIN locations l ON i.location_id = l.id " &
                     "WHERE m.is_active = TRUE AND l.is_active = TRUE"
            
            If Not String.IsNullOrEmpty(whereClause) Then
                sql &= " AND " & whereClause
            End If
            
            sql &= " ORDER BY m.material_code, l.location_code"
            
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvInventory.DataSource = dt
            
            ' 设置数字列的格式
            For Each column As DataGridViewColumn In dgvInventory.Columns
                If column.Name.Contains("库存") Then
                    column.DefaultCellStyle.Format = "N3"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                End If
            Next
            
            ' 设置低于安全库存的行颜色
            For Each row As DataGridViewRow In dgvInventory.Rows
                If row.Cells("低于安全库存").Value?.ToString() = "是" Then
                    row.DefaultCellStyle.BackColor = Color.LightPink
                End If
            Next
            
            ' 更新记录数
            lblTotalRecords.Text = $"总记录数: {dt.Rows.Count}"
            
        Catch ex As Exception
            MessageBox.Show($"加载库存数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs)
        Dim whereConditions As New List(Of String)
        
        ' 物料编码筛选
        If Not String.IsNullOrWhiteSpace(txtMaterialCodeFilter.Text) Then
            whereConditions.Add($"m.material_code LIKE '%{txtMaterialCodeFilter.Text.Trim()}%'")
        End If
        
        ' 物料名称筛选
        If Not String.IsNullOrWhiteSpace(txtMaterialNameFilter.Text) Then
            whereConditions.Add($"m.material_name LIKE '%{txtMaterialNameFilter.Text.Trim()}%'")
        End If
        
        ' 库位筛选
        If cmbLocationFilter.SelectedIndex > 0 Then
            Dim locationCode = cmbLocationFilter.Text.Split(" "c)(0)
            whereConditions.Add($"l.location_code = '{locationCode}'")
        End If
        
        Dim whereClause = String.Join(" AND ", whereConditions)
        LoadInventory(whereClause)
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs)
        ' 清空筛选条件
        txtMaterialCodeFilter.Clear()
        txtMaterialNameFilter.Clear()
        cmbLocationFilter.SelectedIndex = 0
        
        ' 重新加载数据
        LoadInventory()
    End Sub

    Private Sub btnExport_Click(sender As Object, e As EventArgs)
        MessageBox.Show("导出功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
End Class
