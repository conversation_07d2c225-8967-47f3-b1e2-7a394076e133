''' <summary>
''' 用户会话管理类 - 单例模式
''' 管理当前登录用户的信息和权限
''' </summary>
Public Class UserSession
    Private Shared _instance As UserSession
    Private Shared ReadOnly _lock As New Object()

    ' 当前用户信息
    Public Shared Property CurrentUserId As Integer = 0
    Public Shared Property CurrentUsername As String = ""
    Public Shared Property CurrentRole As String = ""
    Public Shared Property CurrentFullName As String = ""
    Public Shared Property CurrentEmail As String = ""
    Public Shared Property IsActive As Boolean = False

    ' 单例模式实现
    Public Shared ReadOnly Property Instance As UserSession
        Get
            If _instance Is Nothing Then
                SyncLock _lock
                    If _instance Is Nothing Then
                        _instance = New UserSession()
                    End If
                End SyncLock
            End If
            Return _instance
        End Get
    End Property

    Private Sub New()
        ' 私有构造函数
    End Sub

    ''' <summary>
    ''' 设置当前用户信息
    ''' </summary>
    Public Shared Sub SetCurrentUser(userId As Integer, username As String, role As String, fullName As String, email As String, isActive As Boolean)
        CurrentUserId = userId
        CurrentUsername = username
        CurrentRole = role
        CurrentFullName = fullName
        CurrentEmail = email
        UserSession.IsActive = isActive
    End Sub

    ''' <summary>
    ''' 清除当前用户信息
    ''' </summary>
    Public Shared Sub ClearCurrentUser()
        CurrentUserId = 0
        CurrentUsername = ""
        CurrentRole = ""
        CurrentFullName = ""
        CurrentEmail = ""
        IsActive = False
    End Sub

    ''' <summary>
    ''' 检查用户是否已登录
    ''' </summary>
    Public Shared Function IsLoggedIn() As Boolean
        Return CurrentUserId > 0 AndAlso Not String.IsNullOrEmpty(CurrentUsername)
    End Function

    ''' <summary>
    ''' 获取当前用户显示名称
    ''' </summary>
    Public Shared Function GetDisplayName() As String
        If Not String.IsNullOrEmpty(CurrentFullName) Then
            Return CurrentFullName
        Else
            Return CurrentUsername
        End If
    End Function

    ''' <summary>
    ''' 获取当前用户角色显示名称
    ''' </summary>
    Public Shared Function GetRoleName() As String
        Return PermissionManager.GetRoleDisplayName(CurrentRole)
    End Function
End Class

''' <summary>
''' 权限管理类
''' 定义不同角色的权限和权限检查方法
''' </summary>
Public Class PermissionManager
    ' 权限枚举
    Public Enum Permission
        ' 基础数据管理
        ViewCustomers = 1
        ManageCustomers = 2
        ViewSuppliers = 3
        ManageSuppliers = 4
        ViewMaterials = 5
        ManageMaterials = 6
        ViewLocations = 7
        ManageLocations = 8

        ' 库存管理
        ViewInventory = 10
        ManageInbound = 11
        ManageOutbound = 12
        ManageInventoryAdjustment = 13

        ' 订单管理
        ViewOrders = 20
        ManageOrders = 21
        ApproveOrders = 22

        ' 财务管理
        ViewFinancial = 30
        ManageFinancial = 31
        ViewReports = 32

        ' 系统管理
        ViewUsers = 40
        ManageUsers = 41
        SystemSettings = 42
        ViewLogs = 43
    End Enum

    ' 角色权限映射
    Private Shared ReadOnly RolePermissions As New Dictionary(Of String, List(Of Permission)) From {
        {"admin", New List(Of Permission) From {
            Permission.ViewCustomers, Permission.ManageCustomers,
            Permission.ViewSuppliers, Permission.ManageSuppliers,
            Permission.ViewMaterials, Permission.ManageMaterials,
            Permission.ViewLocations, Permission.ManageLocations,
            Permission.ViewInventory, Permission.ManageInbound, Permission.ManageOutbound, Permission.ManageInventoryAdjustment,
            Permission.ViewOrders, Permission.ManageOrders, Permission.ApproveOrders,
            Permission.ViewFinancial, Permission.ManageFinancial, Permission.ViewReports,
            Permission.ViewUsers, Permission.ManageUsers, Permission.SystemSettings, Permission.ViewLogs
        }},
        {"manager", New List(Of Permission) From {
            Permission.ViewCustomers, Permission.ManageCustomers,
            Permission.ViewSuppliers, Permission.ManageSuppliers,
            Permission.ViewMaterials, Permission.ManageMaterials,
            Permission.ViewLocations, Permission.ManageLocations,
            Permission.ViewInventory, Permission.ManageInbound, Permission.ManageOutbound, Permission.ManageInventoryAdjustment,
            Permission.ViewOrders, Permission.ManageOrders, Permission.ApproveOrders,
            Permission.ViewFinancial, Permission.ViewReports,
            Permission.ViewUsers
        }},
        {"user", New List(Of Permission) From {
            Permission.ViewCustomers,
            Permission.ViewSuppliers,
            Permission.ViewMaterials,
            Permission.ViewLocations,
            Permission.ViewInventory, Permission.ManageInbound, Permission.ManageOutbound,
            Permission.ViewOrders, Permission.ManageOrders
        }},
        {"readonly", New List(Of Permission) From {
            Permission.ViewCustomers,
            Permission.ViewSuppliers,
            Permission.ViewMaterials,
            Permission.ViewLocations,
            Permission.ViewInventory,
            Permission.ViewOrders
        }}
    }

    ''' <summary>
    ''' 检查当前用户是否有指定权限
    ''' </summary>
    Public Shared Function HasPermission(permission As Permission) As Boolean
        If Not UserSession.IsLoggedIn() Then
            Return False
        End If

        Dim userRole As String = UserSession.CurrentRole.ToLower()
        If RolePermissions.ContainsKey(userRole) Then
            Return RolePermissions(userRole).Contains(permission)
        End If

        Return False
    End Function

    ''' <summary>
    ''' 检查当前用户是否有多个权限中的任意一个
    ''' </summary>
    Public Shared Function HasAnyPermission(ParamArray permissions As Permission()) As Boolean
        For Each permission As Permission In permissions
            If HasPermission(permission) Then
                Return True
            End If
        Next
        Return False
    End Function

    ''' <summary>
    ''' 检查当前用户是否有所有指定权限
    ''' </summary>
    Public Shared Function HasAllPermissions(ParamArray permissions As Permission()) As Boolean
        For Each permission As Permission In permissions
            If Not HasPermission(permission) Then
                Return False
            End If
        Next
        Return True
    End Function

    ''' <summary>
    ''' 获取角色的中文显示名称
    ''' </summary>
    Public Shared Function GetRoleDisplayName(role As String) As String
        Select Case role.ToLower()
            Case "admin"
                Return "系统管理员"
            Case "manager"
                Return "部门经理"
            Case "user"
                Return "普通用户"
            Case "readonly"
                Return "只读用户"
            Case Else
                Return role
        End Select
    End Function

    ''' <summary>
    ''' 权限检查失败时显示提示信息
    ''' </summary>
    Public Shared Sub ShowPermissionDeniedMessage()
        MessageBox.Show("您没有执行此操作的权限！", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
    End Sub
End Class
