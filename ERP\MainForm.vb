Imports System.Windows.Forms

''' <summary>
''' 主窗体 - 系统主界面
''' </summary>
Public Class MainForm
    Inherits Form

    Private menuStrip As MenuStrip
    Private statusStrip As StatusStrip
    Private toolStrip As ToolStrip
    Private lblStatus As ToolStripStatusLabel
    Private lblTime As ToolStripStatusLabel

    Public Sub New()
        InitializeComponent()
        InitializeTimer()
        SetMenuPermissions()
        UpdateStatusBar()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "ERP库存管理系统"
        Me.Size = New Size(1200, 800)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
        Me.IsMdiContainer = True

        ' 创建菜单栏
        CreateMenuStrip()

        ' 创建工具栏
        CreateToolStrip()

        ' 创建状态栏
        CreateStatusStrip()

        ' 设置背景色
        Me.BackColor = Color.LightGray
    End Sub

    Private Sub CreateMenuStrip()
        menuStrip = New MenuStrip()

        ' 基础数据菜单
        Dim masterDataMenu As New ToolStripMenuItem("基础数据(&M)")
        masterDataMenu.DropDownItems.Add(New ToolStripMenuItem("客户管理", Nothing, AddressOf OpenCustomerForm))
        masterDataMenu.DropDownItems.Add(New ToolStripMenuItem("供应商管理", Nothing, AddressOf OpenSupplierForm))
        masterDataMenu.DropDownItems.Add(New ToolStripMenuItem("物料管理", Nothing, AddressOf OpenMaterialForm))
        masterDataMenu.DropDownItems.Add(New ToolStripMenuItem("库位管理", Nothing, AddressOf OpenLocationForm))
        masterDataMenu.DropDownItems.Add(New ToolStripSeparator())
        masterDataMenu.DropDownItems.Add(New ToolStripMenuItem("用户管理", Nothing, AddressOf OpenUserForm) With {.Name = "mnuUserManagement"})

        ' 库存管理菜单
        Dim inventoryMenu As New ToolStripMenuItem("库存管理(&I)")
        inventoryMenu.DropDownItems.Add(New ToolStripMenuItem("库存查询", Nothing, AddressOf OpenInventoryForm))
        inventoryMenu.DropDownItems.Add(New ToolStripMenuItem("入库管理", Nothing, AddressOf OpenInboundForm))
        inventoryMenu.DropDownItems.Add(New ToolStripMenuItem("出库管理", Nothing, AddressOf OpenOutboundForm))
        inventoryMenu.DropDownItems.Add(New ToolStripSeparator())
        inventoryMenu.DropDownItems.Add(New ToolStripMenuItem("库存盘点", Nothing, AddressOf OpenStockCountForm))
        inventoryMenu.DropDownItems.Add(New ToolStripMenuItem("安全库存预警", Nothing, AddressOf OpenSafetyStockForm))

        ' 订单管理菜单
        Dim orderMenu As New ToolStripMenuItem("订单管理(&O)")
        orderMenu.DropDownItems.Add(New ToolStripMenuItem("订单查询", Nothing, AddressOf OpenOrderForm))
        orderMenu.DropDownItems.Add(New ToolStripMenuItem("订单分析", Nothing, AddressOf OpenOrderAnalysisForm))

        ' 财务管理菜单
        Dim financeMenu As New ToolStripMenuItem("财务管理(&F)")
        financeMenu.DropDownItems.Add(New ToolStripMenuItem("应收账款", Nothing, AddressOf OpenAccountsReceivableForm) With {.Name = "mnuAccountsReceivable"})
        financeMenu.DropDownItems.Add(New ToolStripMenuItem("应付账款", Nothing, AddressOf OpenAccountsPayableForm) With {.Name = "mnuAccountsPayable"})
        financeMenu.DropDownItems.Add(New ToolStripSeparator())
        financeMenu.DropDownItems.Add(New ToolStripMenuItem("收付款记录", Nothing, AddressOf OpenPaymentRecordsForm) With {.Name = "mnuPaymentRecords"})

        ' 报表菜单
        Dim reportMenu As New ToolStripMenuItem("报表(&R)")
        reportMenu.DropDownItems.Add(New ToolStripMenuItem("库存报表", Nothing, AddressOf OpenInventoryReport))
        reportMenu.DropDownItems.Add(New ToolStripMenuItem("出入库报表", Nothing, AddressOf OpenInOutReport))
        reportMenu.DropDownItems.Add(New ToolStripMenuItem("订单报表", Nothing, AddressOf OpenOrderReport))
        reportMenu.DropDownItems.Add(New ToolStripSeparator())
        reportMenu.DropDownItems.Add(New ToolStripMenuItem("财务报表", Nothing, AddressOf OpenFinanceReport) With {.Name = "mnuFinanceReport"})

        ' 系统菜单
        Dim systemMenu As New ToolStripMenuItem("系统(&S)")
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("系统设置", Nothing, AddressOf OpenSystemSettings))
        systemMenu.DropDownItems.Add(New ToolStripSeparator())
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("关于系统", Nothing, AddressOf ShowAbout))
        systemMenu.DropDownItems.Add(New ToolStripSeparator())
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("退出系统", Nothing, AddressOf ExitSystem))

        ' 窗口菜单
        Dim windowMenu As New ToolStripMenuItem("窗口(&W)")
        windowMenu.DropDownItems.Add(New ToolStripMenuItem("层叠", Nothing, AddressOf CascadeWindows))
        windowMenu.DropDownItems.Add(New ToolStripMenuItem("水平平铺", Nothing, AddressOf TileHorizontal))
        windowMenu.DropDownItems.Add(New ToolStripMenuItem("垂直平铺", Nothing, AddressOf TileVertical))
        windowMenu.DropDownItems.Add(New ToolStripSeparator())
        windowMenu.DropDownItems.Add(New ToolStripMenuItem("关闭所有窗口", Nothing, AddressOf CloseAllWindows))

        menuStrip.Items.AddRange({masterDataMenu, inventoryMenu, orderMenu, financeMenu, reportMenu, systemMenu, windowMenu})
        Me.MainMenuStrip = menuStrip
        Me.Controls.Add(menuStrip)
    End Sub

    Private Sub CreateToolStrip()
        toolStrip = New ToolStrip()
        toolStrip.ImageScalingSize = New Size(32, 32)

        ' 添加常用功能按钮
        Dim btnCustomer As New ToolStripButton("客户管理")
        btnCustomer.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
        AddHandler btnCustomer.Click, AddressOf OpenCustomerForm

        Dim btnMaterial As New ToolStripButton("物料管理")
        btnMaterial.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
        AddHandler btnMaterial.Click, AddressOf OpenMaterialForm

        Dim btnInventory As New ToolStripButton("库存查询")
        btnInventory.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
        AddHandler btnInventory.Click, AddressOf OpenInventoryForm

        Dim btnInbound As New ToolStripButton("入库管理")
        btnInbound.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
        AddHandler btnInbound.Click, AddressOf OpenInboundForm

        Dim btnOutbound As New ToolStripButton("出库管理")
        btnOutbound.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
        AddHandler btnOutbound.Click, AddressOf OpenOutboundForm

        toolStrip.Items.AddRange({btnCustomer, New ToolStripSeparator(), btnMaterial, New ToolStripSeparator(), 
                                 btnInventory, btnInbound, btnOutbound})
        Me.Controls.Add(toolStrip)
    End Sub

    Private Sub CreateStatusStrip()
        statusStrip = New StatusStrip()

        lblStatus = New ToolStripStatusLabel()
        lblStatus.Text = "就绪"
        lblStatus.Spring = True
        lblStatus.TextAlign = ContentAlignment.MiddleLeft

        lblTime = New ToolStripStatusLabel()
        lblTime.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")

        statusStrip.Items.AddRange({lblStatus, lblTime})
        Me.Controls.Add(statusStrip)
    End Sub

    Private Sub InitializeTimer()
        Dim timer As New Timer()
        timer.Interval = 1000 ' 1秒更新一次
        AddHandler timer.Tick, Sub() lblTime.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
        timer.Start()
    End Sub

    Private Sub SetMenuPermissions()
        ' 根据用户权限设置菜单项的可见性和可用性
        If menuStrip IsNot Nothing Then
            ' 基础数据管理权限
            SetMenuItemPermission("mnuCustomerManagement", PermissionManager.Permission.ViewCustomers, PermissionManager.Permission.ManageCustomers)
            SetMenuItemPermission("mnuSupplierManagement", PermissionManager.Permission.ViewSuppliers, PermissionManager.Permission.ManageSuppliers)
            SetMenuItemPermission("mnuMaterialManagement", PermissionManager.Permission.ViewMaterials, PermissionManager.Permission.ManageMaterials)
            SetMenuItemPermission("mnuLocationManagement", PermissionManager.Permission.ViewLocations, PermissionManager.Permission.ManageLocations)

            ' 库存管理权限
            SetMenuItemPermission("mnuInventoryQuery", PermissionManager.Permission.ViewInventory)
            SetMenuItemPermission("mnuInboundManagement", PermissionManager.Permission.ManageInbound)
            SetMenuItemPermission("mnuOutboundManagement", PermissionManager.Permission.ManageOutbound)
            SetMenuItemPermission("mnuStockCount", PermissionManager.Permission.ManageInventoryAdjustment)
            SetMenuItemPermission("mnuSafetyStockAlert", PermissionManager.Permission.ViewInventory)

            ' 订单管理权限
            SetMenuItemPermission("mnuOrderManagement", PermissionManager.Permission.ViewOrders, PermissionManager.Permission.ManageOrders)
            SetMenuItemPermission("mnuOrderAnalysis", PermissionManager.Permission.ViewOrders)

            ' 财务管理权限
            SetMenuItemPermission("mnuAccountsReceivable", PermissionManager.Permission.ViewFinancial)
            SetMenuItemPermission("mnuAccountsPayable", PermissionManager.Permission.ViewFinancial)
            SetMenuItemPermission("mnuPaymentRecords", PermissionManager.Permission.ViewFinancial)
            SetMenuItemPermission("mnuFinanceReport", PermissionManager.Permission.ViewFinancial)

            ' 系统管理权限
            SetMenuItemPermission("mnuUserManagement", PermissionManager.Permission.ViewUsers, PermissionManager.Permission.ManageUsers)
            SetMenuItemPermission("mnuSystemSettings", PermissionManager.Permission.SystemSettings)
            SetMenuItemPermission("mnuSystemLogs", PermissionManager.Permission.ViewLogs)

            ' 隐藏整个财务管理菜单（如果用户没有任何财务权限）
            Dim financeMenu As ToolStripMenuItem = FindMenuItemByName("mnuFinance")
            If financeMenu IsNot Nothing Then
                financeMenu.Visible = PermissionManager.HasPermission(PermissionManager.Permission.ViewFinancial)
            End If
        End If
    End Sub

    ''' <summary>
    ''' 设置单个菜单项的权限
    ''' </summary>
    Private Sub SetMenuItemPermission(menuName As String, viewPermission As PermissionManager.Permission, Optional managePermission As PermissionManager.Permission? = Nothing)
        Dim menuItem As ToolStripMenuItem = FindMenuItemByName(menuName)
        If menuItem IsNot Nothing Then
            menuItem.Visible = PermissionManager.HasPermission(viewPermission)
            If managePermission.HasValue Then
                menuItem.Enabled = PermissionManager.HasPermission(managePermission.Value)
            End If
        End If
    End Sub

    Private Function FindMenuItemByName(name As String) As ToolStripMenuItem
        For Each item As ToolStripMenuItem In menuStrip.Items
            Dim found As ToolStripMenuItem = FindMenuItemRecursive(item, name)
            If found IsNot Nothing Then
                Return found
            End If
        Next
        Return Nothing
    End Function

    Private Function FindMenuItemRecursive(item As ToolStripMenuItem, name As String) As ToolStripMenuItem
        If item.Name = name Then
            Return item
        End If

        For Each subItem As ToolStripItem In item.DropDownItems
            If TypeOf subItem Is ToolStripMenuItem Then
                Dim found As ToolStripMenuItem = FindMenuItemRecursive(DirectCast(subItem, ToolStripMenuItem), name)
                If found IsNot Nothing Then
                    Return found
                End If
            End If
        Next

        Return Nothing
    End Function

    Private Sub UpdateStatusBar()
        If lblStatus IsNot Nothing Then
            lblStatus.Text = $"欢迎，{UserSession.GetDisplayName()} ({PermissionManager.GetRoleDisplayName(UserSession.CurrentRole)})"
        End If
    End Sub

    ' 菜单事件处理
    Private Sub OpenCustomerForm(sender As Object, e As EventArgs)
        OpenChildForm(New CustomerForm(), "客户管理")
    End Sub

    Private Sub OpenSupplierForm(sender As Object, e As EventArgs)
        OpenChildForm(New SupplierForm(), "供应商管理")
    End Sub

    Private Sub OpenMaterialForm(sender As Object, e As EventArgs)
        OpenChildForm(New MaterialForm(), "物料管理")
    End Sub

    Private Sub OpenLocationForm(sender As Object, e As EventArgs)
        OpenChildForm(New LocationForm(), "库位管理")
    End Sub

    Private Sub OpenUserForm(sender As Object, e As EventArgs)
        ' 检查权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewUsers) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        OpenChildForm(New UserForm(), "用户管理")
    End Sub

    Private Sub OpenInventoryForm(sender As Object, e As EventArgs)
        OpenChildForm(New InventoryForm(), "库存查询")
    End Sub

    Private Sub OpenInboundForm(sender As Object, e As EventArgs)
        OpenChildForm(New InboundForm(), "入库管理")
    End Sub

    Private Sub OpenOutboundForm(sender As Object, e As EventArgs)
        OpenChildForm(New OutboundForm(), "出库管理")
    End Sub

    Private Sub OpenStockCountForm(sender As Object, e As EventArgs)
        ' 检查权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewInventory) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        OpenChildForm(New StockCountForm(), "库存盘点管理")
    End Sub

    Private Sub OpenSafetyStockForm(sender As Object, e As EventArgs)
        ' 检查权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewInventory) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        OpenChildForm(New SafetyStockAlertForm(), "安全库存预警管理")
    End Sub

    Private Sub OpenOrderForm(sender As Object, e As EventArgs)
        OpenChildForm(New OrderForm(), "订单管理")
    End Sub

    Private Sub OpenOrderAnalysisForm(sender As Object, e As EventArgs)
        ' 检查权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewOrders) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        OpenChildForm(New OrderAnalysisForm(), "订单分析")
    End Sub

    ' 财务管理菜单事件
    Private Sub OpenAccountsReceivableForm(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewFinancial) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        OpenChildForm(New AccountsReceivableForm(), "应收账款管理")
    End Sub

    Private Sub OpenAccountsPayableForm(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewFinancial) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        OpenChildForm(New AccountsPayableForm(), "应付账款管理")
    End Sub

    Private Sub OpenPaymentRecordsForm(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewFinancial) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        ' TODO: 实现收付款记录查询窗体
        MessageBox.Show("收付款记录功能正在开发中", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub OpenFinanceReport(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewFinancial) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        OpenChildForm(New FinancialReportForm(), "财务报表")
    End Sub

    Private Sub OpenInventoryReport(sender As Object, e As EventArgs)
        OpenChildForm(New InventoryReportForm(), "库存报表")
    End Sub

    Private Sub OpenInOutReport(sender As Object, e As EventArgs)
        OpenChildForm(New InOutReportForm(), "出入库报表")
    End Sub

    Private Sub OpenOrderReport(sender As Object, e As EventArgs)
        MessageBox.Show("订单报表功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub OpenSystemSettings(sender As Object, e As EventArgs)
        ' 检查权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.SystemSettings) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        OpenChildForm(New SystemSettingsForm(), "系统参数设置")
    End Sub

    Private Sub ShowAbout(sender As Object, e As EventArgs)
        MessageBox.Show("ERP库存管理系统 v1.0" & vbCrLf & "基于VB.NET和MySQL开发", "关于系统", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub ExitSystem(sender As Object, e As EventArgs)
        If MessageBox.Show("确定要退出系统吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Application.Exit()
        End If
    End Sub

    ' 窗口管理
    Private Sub CascadeWindows(sender As Object, e As EventArgs)
        Me.LayoutMdi(MdiLayout.Cascade)
    End Sub

    Private Sub TileHorizontal(sender As Object, e As EventArgs)
        Me.LayoutMdi(MdiLayout.TileHorizontal)
    End Sub

    Private Sub TileVertical(sender As Object, e As EventArgs)
        Me.LayoutMdi(MdiLayout.TileVertical)
    End Sub

    Private Sub CloseAllWindows(sender As Object, e As EventArgs)
        For Each childForm As Form In Me.MdiChildren
            childForm.Close()
        Next
    End Sub

    ''' <summary>
    ''' 打开子窗体的通用方法
    ''' </summary>
    Private Sub OpenChildForm(form As Form, title As String)
        ' 检查是否已经打开了相同类型的窗体
        For Each childForm As Form In Me.MdiChildren
            If childForm.GetType() = form.GetType() Then
                childForm.Activate()
                form.Dispose()
                Return
            End If
        Next

        ' 设置为MDI子窗体
        form.MdiParent = Me
        form.Text = title
        form.Show()
        lblStatus.Text = $"已打开: {title}"
    End Sub
End Class
