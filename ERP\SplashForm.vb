Imports System.Drawing
Imports System.Drawing.Drawing2D
Imports System.Windows.Forms
Imports FontAwesome.Sharp

''' <summary>
''' 现代化启动画面
''' </summary>
Public Class SplashForm
    Inherits Form

    Private WithEvents tmrProgress As Timer
    Private progressValue As Integer = 0
    Private lblTitle As Label
    Private lblVersion As Label
    Private lblStatus As Label
    Private pnlProgress As Panel
    Private pnlProgressBar As Panel
    Private iconLogo As IconPictureBox

    Public Sub New()
        InitializeComponent()
        SetupUI()
        StartProgress()
    End Sub

    Private Sub InitializeComponent()
        Me.SuspendLayout()
        Me.Size = New Size(600, 400)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.None
        Me.BackColor = UIThemeManager.SurfaceColor
        Me.ShowInTaskbar = False
        Me.TopMost = True
        
        ' 设置窗体圆角
        Me.Region = New Region(CreateRoundedRectangle(Me.ClientRectangle, 15))
        
        Me.ResumeLayout(False)
    End Sub

    Private Sub SetupUI()
        ' 背景渐变面板
        Dim pnlBackground As New Panel()
        pnlBackground.Dock = DockStyle.Fill
        pnlBackground.BackColor = Color.Transparent
        AddHandler pnlBackground.Paint, AddressOf PnlBackground_Paint
        Me.Controls.Add(pnlBackground)

        ' Logo图标
        iconLogo = New IconPictureBox()
        iconLogo.IconChar = IconChar.Warehouse
        iconLogo.IconColor = UIThemeManager.PrimaryColor
        iconLogo.IconSize = 80
        iconLogo.Size = New Size(80, 80)
        iconLogo.Location = New Point((Me.Width - 80) \ 2, 80)
        iconLogo.BackColor = Color.Transparent
        pnlBackground.Controls.Add(iconLogo)

        ' 标题
        lblTitle = New Label()
        lblTitle.Text = "ERP库存管理系统"
        lblTitle.Font = New Font("Microsoft YaHei", 24, FontStyle.Bold)
        lblTitle.ForeColor = UIThemeManager.TextPrimaryColor
        lblTitle.BackColor = Color.Transparent
        lblTitle.AutoSize = True
        lblTitle.Location = New Point((Me.Width - 300) \ 2, 180)
        pnlBackground.Controls.Add(lblTitle)

        ' 版本信息
        lblVersion = New Label()
        lblVersion.Text = "版本 1.0.0"
        lblVersion.Font = New Font("Microsoft YaHei", 10)
        lblVersion.ForeColor = UIThemeManager.TextSecondaryColor
        lblVersion.BackColor = Color.Transparent
        lblVersion.AutoSize = True
        lblVersion.Location = New Point((Me.Width - 80) \ 2, 220)
        pnlBackground.Controls.Add(lblVersion)

        ' 状态标签
        lblStatus = New Label()
        lblStatus.Text = "正在初始化..."
        lblStatus.Font = New Font("Microsoft YaHei", 9)
        lblStatus.ForeColor = UIThemeManager.TextSecondaryColor
        lblStatus.BackColor = Color.Transparent
        lblStatus.AutoSize = True
        lblStatus.Location = New Point(50, 320)
        pnlBackground.Controls.Add(lblStatus)

        ' 进度条容器
        pnlProgress = New Panel()
        pnlProgress.Size = New Size(500, 6)
        pnlProgress.Location = New Point(50, 350)
        pnlProgress.BackColor = UIThemeManager.LightColor
        pnlProgress.Region = New Region(CreateRoundedRectangle(pnlProgress.ClientRectangle, 3))
        pnlBackground.Controls.Add(pnlProgress)

        ' 进度条
        pnlProgressBar = New Panel()
        pnlProgressBar.Size = New Size(0, 6)
        pnlProgressBar.Location = New Point(0, 0)
        pnlProgressBar.BackColor = UIThemeManager.PrimaryColor
        pnlProgress.Controls.Add(pnlProgressBar)

        ' 版权信息
        Dim lblCopyright As New Label()
        lblCopyright.Text = "© 2025 您的公司名称. 保留所有权利."
        lblCopyright.Font = New Font("Microsoft YaHei", 8)
        lblCopyright.ForeColor = UIThemeManager.TextSecondaryColor
        lblCopyright.BackColor = Color.Transparent
        lblCopyright.AutoSize = True
        lblCopyright.Location = New Point((Me.Width - 200) \ 2, 370)
        pnlBackground.Controls.Add(lblCopyright)
    End Sub

    Private Sub PnlBackground_Paint(sender As Object, e As PaintEventArgs)
        ' 绘制渐变背景
        Dim rect As Rectangle = DirectCast(sender, Panel).ClientRectangle
        Using brush As New LinearGradientBrush(rect, Color.White, UIThemeManager.BackgroundColor, LinearGradientMode.Vertical)
            e.Graphics.FillRectangle(brush, rect)
        End Using

        ' 绘制边框
        Using pen As New Pen(UIThemeManager.BorderColor, 1)
            e.Graphics.DrawRectangle(pen, 0, 0, rect.Width - 1, rect.Height - 1)
        End Using
    End Sub

    Private Function CreateRoundedRectangle(rect As Rectangle, radius As Integer) As GraphicsPath
        Dim path As New GraphicsPath()
        path.AddArc(rect.X, rect.Y, radius, radius, 180, 90)
        path.AddArc(rect.Right - radius, rect.Y, radius, radius, 270, 90)
        path.AddArc(rect.Right - radius, rect.Bottom - radius, radius, radius, 0, 90)
        path.AddArc(rect.X, rect.Bottom - radius, radius, radius, 90, 90)
        path.CloseFigure()
        Return path
    End Function

    Private Sub StartProgress()
        tmrProgress = New Timer()
        tmrProgress.Interval = 50
        tmrProgress.Enabled = True
    End Sub

    Private Sub tmrProgress_Tick(sender As Object, e As EventArgs) Handles tmrProgress.Tick
        progressValue += 2
        
        ' 更新进度条
        Dim progressWidth As Integer = CInt((progressValue / 100.0) * pnlProgress.Width)
        pnlProgressBar.Width = progressWidth
        
        ' 更新进度条圆角
        If progressWidth > 0 Then
            pnlProgressBar.Region = New Region(CreateRoundedRectangle(New Rectangle(0, 0, progressWidth, 6), 3))
        End If

        ' 更新状态文本
        Select Case progressValue
            Case 10
                lblStatus.Text = "正在加载配置文件..."
            Case 25
                lblStatus.Text = "正在连接数据库..."
            Case 40
                lblStatus.Text = "正在验证数据库结构..."
            Case 55
                lblStatus.Text = "正在加载用户权限..."
            Case 70
                lblStatus.Text = "正在初始化界面组件..."
            Case 85
                lblStatus.Text = "正在应用主题样式..."
            Case 95
                lblStatus.Text = "启动完成..."
        End Select

        ' 完成后关闭
        If progressValue >= 100 Then
            tmrProgress.Enabled = False
            Me.DialogResult = DialogResult.OK
            Me.Close()
        End If
    End Sub

    Protected Overrides Sub OnPaint(e As PaintEventArgs)
        ' 绘制阴影效果
        Dim shadowRect As New Rectangle(5, 5, Me.Width - 5, Me.Height - 5)
        Using shadowBrush As New SolidBrush(Color.FromArgb(50, Color.Black))
            e.Graphics.FillRectangle(shadowBrush, shadowRect)
        End Using
        
        MyBase.OnPaint(e)
    End Sub

    ''' <summary>
    ''' 显示启动画面
    ''' </summary>
    Public Shared Sub ShowSplash()
        Dim splash As New SplashForm()
        splash.ShowDialog()
    End Sub

End Class
