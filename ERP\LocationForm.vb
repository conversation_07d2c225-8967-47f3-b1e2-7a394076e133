Imports System.Windows.Forms
Imports System.Data

''' <summary>
''' 库位管理窗体
''' </summary>
Public Class LocationForm
    Inherits Form

    Private dgvLocations As DataGridView
    Private txtLocationCode As TextBox
    Private txtLocationName As TextBox
    Private cmbLocationType As ComboBox
    Private txtDescription As TextBox
    Private chkIsActive As CheckBox

    Private btnNew As Button
    Private btnEdit As Button
    Private btnSave As Button
    Private btnCancel As Button
    Private btnDelete As Button
    Private btnRefresh As Button

    Private isEditMode As Boolean = False
    Private currentLocationId As Integer = 0

    Public Sub New()
        InitializeComponent()
        LoadLocations()
        ApplyPermissions()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "库位管理"
        Me.Size = New Size(900, 500)
        Me.StartPosition = FormStartPosition.CenterParent

        ' 创建数据网格
        dgvLocations = New DataGridView()
        dgvLocations.Location = New Point(10, 10)
        dgvLocations.Size = New Size(500, 350)
        dgvLocations.ReadOnly = True
        dgvLocations.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvLocations.MultiSelect = False
        dgvLocations.AllowUserToAddRows = False
        dgvLocations.AllowUserToDeleteRows = False
        AddHandler dgvLocations.SelectionChanged, AddressOf dgvLocations_SelectionChanged
        Me.Controls.Add(dgvLocations)

        ' 创建输入控件
        CreateInputControls()

        ' 创建按钮
        CreateButtons()

        ' 设置初始状态
        SetFormMode(False)
    End Sub

    Private Sub CreateInputControls()
        Dim startX As Integer = 530
        Dim startY As Integer = 30
        Dim labelWidth As Integer = 80
        Dim textWidth As Integer = 200
        Dim rowHeight As Integer = 35

        ' 库位编码
        Dim lblLocationCode As New Label()
        lblLocationCode.Text = "库位编码:"
        lblLocationCode.Location = New Point(startX, startY)
        lblLocationCode.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblLocationCode)

        txtLocationCode = New TextBox()
        txtLocationCode.Location = New Point(startX + labelWidth + 10, startY)
        txtLocationCode.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtLocationCode)

        ' 库位名称
        startY += rowHeight
        Dim lblLocationName As New Label()
        lblLocationName.Text = "库位名称:"
        lblLocationName.Location = New Point(startX, startY)
        lblLocationName.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblLocationName)

        txtLocationName = New TextBox()
        txtLocationName.Location = New Point(startX + labelWidth + 10, startY)
        txtLocationName.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtLocationName)

        ' 库位类型
        startY += rowHeight
        Dim lblLocationType As New Label()
        lblLocationType.Text = "库位类型:"
        lblLocationType.Location = New Point(startX, startY)
        lblLocationType.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblLocationType)

        cmbLocationType = New ComboBox()
        cmbLocationType.Location = New Point(startX + labelWidth + 10, startY)
        cmbLocationType.Size = New Size(textWidth, 25)
        cmbLocationType.DropDownStyle = ComboBoxStyle.DropDownList
        cmbLocationType.Items.AddRange({"storage", "wip", "qc", "shipping", "receiving"})
        cmbLocationType.SelectedIndex = 0
        Me.Controls.Add(cmbLocationType)

        ' 描述
        startY += rowHeight
        Dim lblDescription As New Label()
        lblDescription.Text = "描述:"
        lblDescription.Location = New Point(startX, startY)
        lblDescription.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblDescription)

        txtDescription = New TextBox()
        txtDescription.Location = New Point(startX + labelWidth + 10, startY)
        txtDescription.Size = New Size(textWidth, 80)
        txtDescription.Multiline = True
        Me.Controls.Add(txtDescription)

        ' 是否启用
        startY += 90
        chkIsActive = New CheckBox()
        chkIsActive.Text = "启用"
        chkIsActive.Location = New Point(startX + labelWidth + 10, startY)
        chkIsActive.Checked = True
        Me.Controls.Add(chkIsActive)
    End Sub

    Private Sub CreateButtons()
        Dim buttonY As Integer = 380
        Dim buttonWidth As Integer = 80
        Dim buttonHeight As Integer = 30
        Dim buttonSpacing As Integer = 90

        btnNew = New Button()
        btnNew.Text = "新增"
        btnNew.Location = New Point(10, buttonY)
        btnNew.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnNew.Click, AddressOf btnNew_Click
        Me.Controls.Add(btnNew)

        btnEdit = New Button()
        btnEdit.Text = "编辑"
        btnEdit.Location = New Point(10 + buttonSpacing, buttonY)
        btnEdit.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnEdit.Click, AddressOf btnEdit_Click
        Me.Controls.Add(btnEdit)

        btnSave = New Button()
        btnSave.Text = "保存"
        btnSave.Location = New Point(10 + buttonSpacing * 2, buttonY)
        btnSave.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnSave.Click, AddressOf btnSave_Click
        Me.Controls.Add(btnSave)

        btnCancel = New Button()
        btnCancel.Text = "取消"
        btnCancel.Location = New Point(10 + buttonSpacing * 3, buttonY)
        btnCancel.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnCancel.Click, AddressOf btnCancel_Click
        Me.Controls.Add(btnCancel)

        btnDelete = New Button()
        btnDelete.Text = "删除"
        btnDelete.Location = New Point(10 + buttonSpacing * 4, buttonY)
        btnDelete.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnDelete.Click, AddressOf btnDelete_Click
        Me.Controls.Add(btnDelete)

        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(10 + buttonSpacing * 5, buttonY)
        btnRefresh.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnRefresh.Click, AddressOf btnRefresh_Click
        Me.Controls.Add(btnRefresh)
    End Sub

    Private Sub LoadLocations()
        Try
            Dim sql = "SELECT id, location_code AS '库位编码', location_name AS '库位名称', " &
                     "location_type AS '库位类型', description AS '描述', " &
                     "CASE WHEN is_active THEN '是' ELSE '否' END AS '启用状态' " &
                     "FROM locations ORDER BY location_code"
            
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvLocations.DataSource = dt
            
            ' 隐藏ID列
            If dgvLocations.Columns.Contains("id") Then
                dgvLocations.Columns("id").Visible = False
            End If
            
            ' 设置列宽
            dgvLocations.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        Catch ex As Exception
            MessageBox.Show($"加载库位数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub dgvLocations_SelectionChanged(sender As Object, e As EventArgs)
        If dgvLocations.CurrentRow IsNot Nothing AndAlso Not isEditMode Then
            LoadLocationDetails()
        End If
    End Sub

    Private Sub LoadLocationDetails()
        Try
            If dgvLocations.CurrentRow Is Nothing Then Return
            
            currentLocationId = Convert.ToInt32(dgvLocations.CurrentRow.Cells("id").Value)
            
            Dim sql = "SELECT * FROM locations WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentLocationId}}
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql, parameters)
            
            If dt.Rows.Count > 0 Then
                Dim row = dt.Rows(0)
                txtLocationCode.Text = row("location_code").ToString()
                txtLocationName.Text = row("location_name").ToString()
                cmbLocationType.Text = row("location_type").ToString()
                txtDescription.Text = row("description").ToString()
                chkIsActive.Checked = Convert.ToBoolean(row("is_active"))
            End If
        Catch ex As Exception
            MessageBox.Show($"加载库位详情时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode

        ' 设置输入控件的启用状态
        txtLocationCode.Enabled = editMode
        txtLocationName.Enabled = editMode
        cmbLocationType.Enabled = editMode
        txtDescription.Enabled = editMode
        chkIsActive.Enabled = editMode

        ' 设置按钮的启用状态（结合权限控制）
        btnNew.Enabled = Not editMode AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageLocations)
        btnEdit.Enabled = Not editMode AndAlso currentLocationId > 0 AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageLocations)
        btnDelete.Enabled = Not editMode AndAlso currentLocationId > 0 AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageLocations)
        btnSave.Enabled = editMode
        btnCancel.Enabled = editMode
        btnRefresh.Enabled = Not editMode

        dgvLocations.Enabled = Not editMode
    End Sub

    ''' <summary>
    ''' 应用权限控制
    ''' </summary>
    Private Sub ApplyPermissions()
        ' 检查基本查看权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewLocations) Then
            MessageBox.Show("您没有权限访问库位管理功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 根据权限设置按钮状态
        SetFormMode(False)
    End Sub

    Private Sub ClearForm()
        txtLocationCode.Clear()
        txtLocationName.Clear()
        cmbLocationType.SelectedIndex = 0
        txtDescription.Clear()
        chkIsActive.Checked = True
        currentLocationId = 0
    End Sub

    ' 按钮事件处理
    Private Sub btnNew_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageLocations) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        ClearForm()
        SetFormMode(True)
        txtLocationCode.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageLocations) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        If currentLocationId <= 0 Then
            MessageBox.Show("请先选择要编辑的库位", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        SetFormMode(True)
        txtLocationName.Focus()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs)
        If Not ValidateForm() Then Return
        
        Try
            If currentLocationId = 0 Then
                ' 新增
                Dim sql = "INSERT INTO locations (location_code, location_name, location_type, description, is_active) " &
                         "VALUES (@location_code, @location_name, @location_type, @description, @is_active)"
                
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@location_code", txtLocationCode.Text.Trim()},
                    {"@location_name", txtLocationName.Text.Trim()},
                    {"@location_type", cmbLocationType.Text},
                    {"@description", txtDescription.Text.Trim()},
                    {"@is_active", chkIsActive.Checked}
                }
                
                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("库位信息保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' 更新
                Dim sql = "UPDATE locations SET location_name = @location_name, location_type = @location_type, " &
                         "description = @description, is_active = @is_active WHERE id = @id"
                
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@location_name", txtLocationName.Text.Trim()},
                    {"@location_type", cmbLocationType.Text},
                    {"@description", txtDescription.Text.Trim()},
                    {"@is_active", chkIsActive.Checked},
                    {"@id", currentLocationId}
                }
                
                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("库位信息更新成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            
            SetFormMode(False)
            LoadLocations()
        Catch ex As Exception
            MessageBox.Show($"保存库位信息时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        SetFormMode(False)
        If currentLocationId > 0 Then
            LoadLocationDetails()
        Else
            ClearForm()
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs)
        If currentLocationId <= 0 Then
            MessageBox.Show("请先选择要删除的库位", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        If MessageBox.Show("确定要删除选中的库位吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim sql = "DELETE FROM locations WHERE id = @id"
                Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentLocationId}}
                
                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("库位删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                
                ClearForm()
                LoadLocations()
            Catch ex As Exception
                MessageBox.Show($"删除库位时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs)
        LoadLocations()
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtLocationCode.Text) Then
            MessageBox.Show("请输入库位编码", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtLocationCode.Focus()
            Return False
        End If
        
        If String.IsNullOrWhiteSpace(txtLocationName.Text) Then
            MessageBox.Show("请输入库位名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtLocationName.Focus()
            Return False
        End If
        
        Return True
    End Function
End Class
