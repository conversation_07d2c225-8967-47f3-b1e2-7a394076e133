Imports System.Windows.Forms
Imports System.Data
Imports System.Drawing

''' <summary>
''' 系统参数设置窗体
''' </summary>
Public Class SystemSettingsForm
    Inherits Form

    Private tabControl As TabControl
    Private tabSystemParams As TabPage
    Private tabBusinessParams As TabPage
    Private tabDataDictionary As TabPage
    Private tabSystemInfo As TabPage

    ' 系统参数控件
    Private dgvSystemParams As DataGridView
    Private txtParamKey As TextBox
    Private txtParamValue As TextBox
    Private txtParamDescription As TextBox
    Private cmbParamType As ComboBox
    Private chkIsActive As CheckBox
    Private btnSaveParam As Button
    Private btnDeleteParam As Button
    Private btnRefreshParams As Button

    ' 业务参数控件
    Private dgvBusinessParams As DataGridView
    Private txtBusinessKey As TextBox
    Private txtBusinessValue As TextBox
    Private txtBusinessDescription As TextBox
    Private cmbBusinessCategory As ComboBox
    Private btnSaveBusinessParam As Button
    Private btnDeleteBusinessParam As Button
    Private btnRefreshBusinessParams As Button

    ' 数据字典控件
    Private dgvDataDictionary As DataGridView
    Private txtDictType As TextBox
    Private txtDictCode As TextBox
    Private txtDictName As TextBox
    Private txtDictValue As TextBox
    Private txtDictSort As TextBox
    Private chkDictActive As CheckBox
    Private btnSaveDictItem As Button
    Private btnDeleteDictItem As Button
    Private btnRefreshDict As Button

    ' 系统信息控件
    Private lblSystemVersion As Label
    Private lblDatabaseVersion As Label
    Private lblLastBackup As Label
    Private lblSystemUptime As Label
    Private lblTotalUsers As Label
    Private lblActiveUsers As Label
    Private btnBackupDatabase As Button
    Private btnClearLogs As Button
    Private btnSystemMaintenance As Button

    Private currentParamId As Integer = 0
    Private currentBusinessParamId As Integer = 0
    Private currentDictId As Integer = 0

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadData()
        ApplyPermissions()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "系统参数设置"
        Me.Size = New Size(1000, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
    End Sub

    Private Sub SetupUI()
        ' 创建选项卡控件
        tabControl = New TabControl()
        tabControl.Dock = DockStyle.Fill
        Me.Controls.Add(tabControl)

        ' 系统参数选项卡
        tabSystemParams = New TabPage("系统参数")
        tabControl.TabPages.Add(tabSystemParams)
        SetupSystemParamsTab()

        ' 业务参数选项卡
        tabBusinessParams = New TabPage("业务参数")
        tabControl.TabPages.Add(tabBusinessParams)
        SetupBusinessParamsTab()

        ' 数据字典选项卡
        tabDataDictionary = New TabPage("数据字典")
        tabControl.TabPages.Add(tabDataDictionary)
        SetupDataDictionaryTab()

        ' 系统信息选项卡
        tabSystemInfo = New TabPage("系统信息")
        tabControl.TabPages.Add(tabSystemInfo)
        SetupSystemInfoTab()
    End Sub

    Private Sub SetupSystemParamsTab()
        ' 参数列表
        dgvSystemParams = New DataGridView()
        dgvSystemParams.Location = New Point(12, 12)
        dgvSystemParams.Size = New Size(960, 300)
        dgvSystemParams.ReadOnly = True
        dgvSystemParams.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvSystemParams.AllowUserToAddRows = False
        dgvSystemParams.AllowUserToDeleteRows = False
        dgvSystemParams.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabSystemParams.Controls.Add(dgvSystemParams)

        ' 参数编辑区域
        Dim lblParamKey As New Label()
        lblParamKey.Text = "参数键:"
        lblParamKey.Location = New Point(12, 330)
        lblParamKey.Size = New Size(60, 20)
        tabSystemParams.Controls.Add(lblParamKey)

        txtParamKey = New TextBox()
        txtParamKey.Location = New Point(80, 328)
        txtParamKey.Size = New Size(200, 23)
        tabSystemParams.Controls.Add(txtParamKey)

        Dim lblParamValue As New Label()
        lblParamValue.Text = "参数值:"
        lblParamValue.Location = New Point(300, 330)
        lblParamValue.Size = New Size(60, 20)
        tabSystemParams.Controls.Add(lblParamValue)

        txtParamValue = New TextBox()
        txtParamValue.Location = New Point(370, 328)
        txtParamValue.Size = New Size(200, 23)
        tabSystemParams.Controls.Add(txtParamValue)

        Dim lblParamType As New Label()
        lblParamType.Text = "参数类型:"
        lblParamType.Location = New Point(590, 330)
        lblParamType.Size = New Size(70, 20)
        tabSystemParams.Controls.Add(lblParamType)

        cmbParamType = New ComboBox()
        cmbParamType.Location = New Point(670, 328)
        cmbParamType.Size = New Size(120, 23)
        cmbParamType.DropDownStyle = ComboBoxStyle.DropDownList
        cmbParamType.Items.AddRange({"字符串", "数字", "布尔值", "日期"})
        tabSystemParams.Controls.Add(cmbParamType)

        Dim lblParamDescription As New Label()
        lblParamDescription.Text = "参数描述:"
        lblParamDescription.Location = New Point(12, 370)
        lblParamDescription.Size = New Size(70, 20)
        tabSystemParams.Controls.Add(lblParamDescription)

        txtParamDescription = New TextBox()
        txtParamDescription.Location = New Point(90, 368)
        txtParamDescription.Size = New Size(400, 23)
        tabSystemParams.Controls.Add(txtParamDescription)

        chkIsActive = New CheckBox()
        chkIsActive.Text = "启用"
        chkIsActive.Location = New Point(510, 370)
        chkIsActive.Size = New Size(60, 20)
        chkIsActive.Checked = True
        tabSystemParams.Controls.Add(chkIsActive)

        ' 按钮
        btnSaveParam = New Button()
        btnSaveParam.Text = "保存"
        btnSaveParam.Location = New Point(12, 410)
        btnSaveParam.Size = New Size(80, 30)
        tabSystemParams.Controls.Add(btnSaveParam)

        btnDeleteParam = New Button()
        btnDeleteParam.Text = "删除"
        btnDeleteParam.Location = New Point(102, 410)
        btnDeleteParam.Size = New Size(80, 30)
        tabSystemParams.Controls.Add(btnDeleteParam)

        btnRefreshParams = New Button()
        btnRefreshParams.Text = "刷新"
        btnRefreshParams.Location = New Point(192, 410)
        btnRefreshParams.Size = New Size(80, 30)
        tabSystemParams.Controls.Add(btnRefreshParams)

        ' 事件处理
        AddHandler dgvSystemParams.SelectionChanged, AddressOf DgvSystemParams_SelectionChanged
        AddHandler btnSaveParam.Click, AddressOf BtnSaveParam_Click
        AddHandler btnDeleteParam.Click, AddressOf BtnDeleteParam_Click
        AddHandler btnRefreshParams.Click, AddressOf BtnRefreshParams_Click
    End Sub

    Private Sub SetupBusinessParamsTab()
        ' 业务参数列表
        dgvBusinessParams = New DataGridView()
        dgvBusinessParams.Location = New Point(12, 12)
        dgvBusinessParams.Size = New Size(960, 300)
        dgvBusinessParams.ReadOnly = True
        dgvBusinessParams.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvBusinessParams.AllowUserToAddRows = False
        dgvBusinessParams.AllowUserToDeleteRows = False
        dgvBusinessParams.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabBusinessParams.Controls.Add(dgvBusinessParams)

        ' 业务参数编辑区域
        Dim lblBusinessKey As New Label()
        lblBusinessKey.Text = "参数键:"
        lblBusinessKey.Location = New Point(12, 330)
        lblBusinessKey.Size = New Size(60, 20)
        tabBusinessParams.Controls.Add(lblBusinessKey)

        txtBusinessKey = New TextBox()
        txtBusinessKey.Location = New Point(80, 328)
        txtBusinessKey.Size = New Size(200, 23)
        tabBusinessParams.Controls.Add(txtBusinessKey)

        Dim lblBusinessValue As New Label()
        lblBusinessValue.Text = "参数值:"
        lblBusinessValue.Location = New Point(300, 330)
        lblBusinessValue.Size = New Size(60, 20)
        tabBusinessParams.Controls.Add(lblBusinessValue)

        txtBusinessValue = New TextBox()
        txtBusinessValue.Location = New Point(370, 328)
        txtBusinessValue.Size = New Size(200, 23)
        tabBusinessParams.Controls.Add(txtBusinessValue)

        Dim lblBusinessCategory As New Label()
        lblBusinessCategory.Text = "参数分类:"
        lblBusinessCategory.Location = New Point(590, 330)
        lblBusinessCategory.Size = New Size(70, 20)
        tabBusinessParams.Controls.Add(lblBusinessCategory)

        cmbBusinessCategory = New ComboBox()
        cmbBusinessCategory.Location = New Point(670, 328)
        cmbBusinessCategory.Size = New Size(120, 23)
        cmbBusinessCategory.DropDownStyle = ComboBoxStyle.DropDownList
        cmbBusinessCategory.Items.AddRange({"库存管理", "订单管理", "财务管理", "用户管理", "系统配置"})
        tabBusinessParams.Controls.Add(cmbBusinessCategory)

        Dim lblBusinessDescription As New Label()
        lblBusinessDescription.Text = "参数描述:"
        lblBusinessDescription.Location = New Point(12, 370)
        lblBusinessDescription.Size = New Size(70, 20)
        tabBusinessParams.Controls.Add(lblBusinessDescription)

        txtBusinessDescription = New TextBox()
        txtBusinessDescription.Location = New Point(90, 368)
        txtBusinessDescription.Size = New Size(500, 23)
        tabBusinessParams.Controls.Add(txtBusinessDescription)

        ' 按钮
        btnSaveBusinessParam = New Button()
        btnSaveBusinessParam.Text = "保存"
        btnSaveBusinessParam.Location = New Point(12, 410)
        btnSaveBusinessParam.Size = New Size(80, 30)
        tabBusinessParams.Controls.Add(btnSaveBusinessParam)

        btnDeleteBusinessParam = New Button()
        btnDeleteBusinessParam.Text = "删除"
        btnDeleteBusinessParam.Location = New Point(102, 410)
        btnDeleteBusinessParam.Size = New Size(80, 30)
        tabBusinessParams.Controls.Add(btnDeleteBusinessParam)

        btnRefreshBusinessParams = New Button()
        btnRefreshBusinessParams.Text = "刷新"
        btnRefreshBusinessParams.Location = New Point(192, 410)
        btnRefreshBusinessParams.Size = New Size(80, 30)
        tabBusinessParams.Controls.Add(btnRefreshBusinessParams)

        ' 事件处理
        AddHandler dgvBusinessParams.SelectionChanged, AddressOf DgvBusinessParams_SelectionChanged
        AddHandler btnSaveBusinessParam.Click, AddressOf BtnSaveBusinessParam_Click
        AddHandler btnDeleteBusinessParam.Click, AddressOf BtnDeleteBusinessParam_Click
        AddHandler btnRefreshBusinessParams.Click, AddressOf BtnRefreshBusinessParams_Click
    End Sub

    Private Sub SetupDataDictionaryTab()
        ' 数据字典列表
        dgvDataDictionary = New DataGridView()
        dgvDataDictionary.Location = New Point(12, 12)
        dgvDataDictionary.Size = New Size(960, 300)
        dgvDataDictionary.ReadOnly = True
        dgvDataDictionary.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvDataDictionary.AllowUserToAddRows = False
        dgvDataDictionary.AllowUserToDeleteRows = False
        dgvDataDictionary.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabDataDictionary.Controls.Add(dgvDataDictionary)

        ' 字典编辑区域
        Dim lblDictType As New Label()
        lblDictType.Text = "字典类型:"
        lblDictType.Location = New Point(12, 330)
        lblDictType.Size = New Size(70, 20)
        tabDataDictionary.Controls.Add(lblDictType)

        txtDictType = New TextBox()
        txtDictType.Location = New Point(90, 328)
        txtDictType.Size = New Size(150, 23)
        tabDataDictionary.Controls.Add(txtDictType)

        Dim lblDictCode As New Label()
        lblDictCode.Text = "字典编码:"
        lblDictCode.Location = New Point(260, 330)
        lblDictCode.Size = New Size(70, 20)
        tabDataDictionary.Controls.Add(lblDictCode)

        txtDictCode = New TextBox()
        txtDictCode.Location = New Point(340, 328)
        txtDictCode.Size = New Size(150, 23)
        tabDataDictionary.Controls.Add(txtDictCode)

        Dim lblDictName As New Label()
        lblDictName.Text = "字典名称:"
        lblDictName.Location = New Point(510, 330)
        lblDictName.Size = New Size(70, 20)
        tabDataDictionary.Controls.Add(lblDictName)

        txtDictName = New TextBox()
        txtDictName.Location = New Point(590, 328)
        txtDictName.Size = New Size(150, 23)
        tabDataDictionary.Controls.Add(txtDictName)

        Dim lblDictValue As New Label()
        lblDictValue.Text = "字典值:"
        lblDictValue.Location = New Point(12, 370)
        lblDictValue.Size = New Size(60, 20)
        tabDataDictionary.Controls.Add(lblDictValue)

        txtDictValue = New TextBox()
        txtDictValue.Location = New Point(80, 368)
        txtDictValue.Size = New Size(200, 23)
        tabDataDictionary.Controls.Add(txtDictValue)

        Dim lblDictSort As New Label()
        lblDictSort.Text = "排序:"
        lblDictSort.Location = New Point(300, 370)
        lblDictSort.Size = New Size(50, 20)
        tabDataDictionary.Controls.Add(lblDictSort)

        txtDictSort = New TextBox()
        txtDictSort.Location = New Point(360, 368)
        txtDictSort.Size = New Size(80, 23)
        txtDictSort.Text = "0"
        tabDataDictionary.Controls.Add(txtDictSort)

        chkDictActive = New CheckBox()
        chkDictActive.Text = "启用"
        chkDictActive.Location = New Point(460, 370)
        chkDictActive.Size = New Size(60, 20)
        chkDictActive.Checked = True
        tabDataDictionary.Controls.Add(chkDictActive)

        ' 按钮
        btnSaveDictItem = New Button()
        btnSaveDictItem.Text = "保存"
        btnSaveDictItem.Location = New Point(12, 410)
        btnSaveDictItem.Size = New Size(80, 30)
        tabDataDictionary.Controls.Add(btnSaveDictItem)

        btnDeleteDictItem = New Button()
        btnDeleteDictItem.Text = "删除"
        btnDeleteDictItem.Location = New Point(102, 410)
        btnDeleteDictItem.Size = New Size(80, 30)
        tabDataDictionary.Controls.Add(btnDeleteDictItem)

        btnRefreshDict = New Button()
        btnRefreshDict.Text = "刷新"
        btnRefreshDict.Location = New Point(192, 410)
        btnRefreshDict.Size = New Size(80, 30)
        tabDataDictionary.Controls.Add(btnRefreshDict)

        ' 事件处理
        AddHandler dgvDataDictionary.SelectionChanged, AddressOf DgvDataDictionary_SelectionChanged
        AddHandler btnSaveDictItem.Click, AddressOf BtnSaveDictItem_Click
        AddHandler btnDeleteDictItem.Click, AddressOf BtnDeleteDictItem_Click
        AddHandler btnRefreshDict.Click, AddressOf BtnRefreshDict_Click
    End Sub

    Private Sub SetupSystemInfoTab()
        ' 系统信息显示区域
        Dim pnlSystemInfo As New Panel()
        pnlSystemInfo.Location = New Point(12, 12)
        pnlSystemInfo.Size = New Size(960, 300)
        pnlSystemInfo.BorderStyle = BorderStyle.FixedSingle
        tabSystemInfo.Controls.Add(pnlSystemInfo)

        ' 系统版本信息
        lblSystemVersion = New Label()
        lblSystemVersion.Text = "系统版本: ERP库存管理系统 v1.0.0"
        lblSystemVersion.Location = New Point(20, 20)
        lblSystemVersion.Size = New Size(400, 20)
        lblSystemVersion.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        pnlSystemInfo.Controls.Add(lblSystemVersion)

        lblDatabaseVersion = New Label()
        lblDatabaseVersion.Text = "数据库版本: MySQL 8.0"
        lblDatabaseVersion.Location = New Point(20, 50)
        lblDatabaseVersion.Size = New Size(400, 20)
        pnlSystemInfo.Controls.Add(lblDatabaseVersion)

        lblLastBackup = New Label()
        lblLastBackup.Text = "最后备份时间: 未备份"
        lblLastBackup.Location = New Point(20, 80)
        lblLastBackup.Size = New Size(400, 20)
        pnlSystemInfo.Controls.Add(lblLastBackup)

        lblSystemUptime = New Label()
        lblSystemUptime.Text = "系统运行时间: 计算中..."
        lblSystemUptime.Location = New Point(20, 110)
        lblSystemUptime.Size = New Size(400, 20)
        pnlSystemInfo.Controls.Add(lblSystemUptime)

        lblTotalUsers = New Label()
        lblTotalUsers.Text = "用户总数: 0"
        lblTotalUsers.Location = New Point(20, 140)
        lblTotalUsers.Size = New Size(200, 20)
        pnlSystemInfo.Controls.Add(lblTotalUsers)

        lblActiveUsers = New Label()
        lblActiveUsers.Text = "活跃用户数: 0"
        lblActiveUsers.Location = New Point(240, 140)
        lblActiveUsers.Size = New Size(200, 20)
        pnlSystemInfo.Controls.Add(lblActiveUsers)

        ' 系统维护按钮
        btnBackupDatabase = New Button()
        btnBackupDatabase.Text = "备份数据库"
        btnBackupDatabase.Location = New Point(12, 330)
        btnBackupDatabase.Size = New Size(120, 30)
        tabSystemInfo.Controls.Add(btnBackupDatabase)

        btnClearLogs = New Button()
        btnClearLogs.Text = "清理日志"
        btnClearLogs.Location = New Point(142, 330)
        btnClearLogs.Size = New Size(100, 30)
        tabSystemInfo.Controls.Add(btnClearLogs)

        btnSystemMaintenance = New Button()
        btnSystemMaintenance.Text = "系统维护"
        btnSystemMaintenance.Location = New Point(252, 330)
        btnSystemMaintenance.Size = New Size(100, 30)
        tabSystemInfo.Controls.Add(btnSystemMaintenance)

        ' 事件处理
        AddHandler btnBackupDatabase.Click, AddressOf BtnBackupDatabase_Click
        AddHandler btnClearLogs.Click, AddressOf BtnClearLogs_Click
        AddHandler btnSystemMaintenance.Click, AddressOf BtnSystemMaintenance_Click
    End Sub

    Private Sub LoadData()
        LoadSystemParams()
        LoadBusinessParams()
        LoadDataDictionary()
        LoadSystemInfo()
    End Sub

    Private Sub LoadSystemParams()
        Try
            Dim query As String = "
                SELECT
                    id,
                    param_key AS '参数键',
                    param_value AS '参数值',
                    param_type AS '参数类型',
                    description AS '描述',
                    CASE WHEN is_active = 1 THEN '是' ELSE '否' END AS '启用状态',
                    created_at AS '创建时间',
                    updated_at AS '更新时间'
                FROM system_params
                ORDER BY param_key"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvSystemParams.DataSource = dt

            ' 隐藏ID列
            If dgvSystemParams.Columns.Contains("id") Then
                dgvSystemParams.Columns("id").Visible = False
            End If

        Catch ex As Exception
            MessageBox.Show($"加载系统参数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadBusinessParams()
        Try
            Dim query As String = "
                SELECT
                    id,
                    param_key AS '参数键',
                    param_value AS '参数值',
                    category AS '分类',
                    description AS '描述',
                    created_at AS '创建时间',
                    updated_at AS '更新时间'
                FROM business_params
                ORDER BY category, param_key"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvBusinessParams.DataSource = dt

            ' 隐藏ID列
            If dgvBusinessParams.Columns.Contains("id") Then
                dgvBusinessParams.Columns("id").Visible = False
            End If

        Catch ex As Exception
            MessageBox.Show($"加载业务参数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadDataDictionary()
        Try
            Dim query As String = "
                SELECT
                    id,
                    dict_type AS '字典类型',
                    dict_code AS '字典编码',
                    dict_name AS '字典名称',
                    dict_value AS '字典值',
                    sort_order AS '排序',
                    CASE WHEN is_active = 1 THEN '是' ELSE '否' END AS '启用状态',
                    created_at AS '创建时间'
                FROM data_dictionary
                ORDER BY dict_type, sort_order, dict_code"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvDataDictionary.DataSource = dt

            ' 隐藏ID列
            If dgvDataDictionary.Columns.Contains("id") Then
                dgvDataDictionary.Columns("id").Visible = False
            End If

        Catch ex As Exception
            MessageBox.Show($"加载数据字典失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadSystemInfo()
        Try
            ' 加载用户统计
            Dim userQuery As String = "SELECT COUNT(*) as total, SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active FROM users"
            Dim userDt As DataTable = DatabaseManager.Instance.ExecuteQuery(userQuery)

            If userDt.Rows.Count > 0 Then
                lblTotalUsers.Text = $"用户总数: {userDt.Rows(0)("total")}"
                lblActiveUsers.Text = $"活跃用户数: {userDt.Rows(0)("active")}"
            End If

            ' 更新系统运行时间
            lblSystemUptime.Text = $"系统运行时间: {Environment.TickCount \ 1000} 秒"

            ' 检查最后备份时间（这里可以从系统参数或日志表中获取）
            Try
                Dim backupQuery As String = "SELECT param_value FROM system_params WHERE param_key = 'last_backup_time'"
                Dim backupTime As Object = DatabaseManager.Instance.ExecuteScalar(backupQuery)
                If backupTime IsNot Nothing AndAlso Not IsDBNull(backupTime) Then
                    lblLastBackup.Text = $"最后备份时间: {backupTime}"
                Else
                    lblLastBackup.Text = "最后备份时间: 未备份"
                End If
            Catch
                lblLastBackup.Text = "最后备份时间: 未备份"
            End Try

        Catch ex As Exception
            MessageBox.Show($"加载系统信息失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 事件处理方法
    Private Sub DgvSystemParams_SelectionChanged(sender As Object, e As EventArgs)
        If dgvSystemParams.SelectedRows.Count > 0 Then
            Dim row As DataGridViewRow = dgvSystemParams.SelectedRows(0)
            currentParamId = Convert.ToInt32(row.Cells("id").Value)

            txtParamKey.Text = row.Cells("参数键").Value?.ToString()
            txtParamValue.Text = row.Cells("参数值").Value?.ToString()
            txtParamDescription.Text = row.Cells("描述").Value?.ToString()
            chkIsActive.Checked = row.Cells("启用状态").Value?.ToString() = "是"

            ' 设置参数类型
            Dim paramType As String = row.Cells("参数类型").Value?.ToString()
            Select Case paramType
                Case "string"
                    cmbParamType.SelectedIndex = 0
                Case "number"
                    cmbParamType.SelectedIndex = 1
                Case "boolean"
                    cmbParamType.SelectedIndex = 2
                Case "date"
                    cmbParamType.SelectedIndex = 3
                Case Else
                    cmbParamType.SelectedIndex = 0
            End Select
        End If
    End Sub

    Private Sub DgvBusinessParams_SelectionChanged(sender As Object, e As EventArgs)
        If dgvBusinessParams.SelectedRows.Count > 0 Then
            Dim row As DataGridViewRow = dgvBusinessParams.SelectedRows(0)
            currentBusinessParamId = Convert.ToInt32(row.Cells("id").Value)

            txtBusinessKey.Text = row.Cells("参数键").Value?.ToString()
            txtBusinessValue.Text = row.Cells("参数值").Value?.ToString()
            txtBusinessDescription.Text = row.Cells("描述").Value?.ToString()

            ' 设置分类
            Dim category As String = row.Cells("分类").Value?.ToString()
            For i As Integer = 0 To cmbBusinessCategory.Items.Count - 1
                If cmbBusinessCategory.Items(i).ToString() = category Then
                    cmbBusinessCategory.SelectedIndex = i
                    Exit For
                End If
            Next
        End If
    End Sub

    Private Sub DgvDataDictionary_SelectionChanged(sender As Object, e As EventArgs)
        If dgvDataDictionary.SelectedRows.Count > 0 Then
            Dim row As DataGridViewRow = dgvDataDictionary.SelectedRows(0)
            currentDictId = Convert.ToInt32(row.Cells("id").Value)

            txtDictType.Text = row.Cells("字典类型").Value?.ToString()
            txtDictCode.Text = row.Cells("字典编码").Value?.ToString()
            txtDictName.Text = row.Cells("字典名称").Value?.ToString()
            txtDictValue.Text = row.Cells("字典值").Value?.ToString()
            txtDictSort.Text = row.Cells("排序").Value?.ToString()
            chkDictActive.Checked = row.Cells("启用状态").Value?.ToString() = "是"
        End If
    End Sub

    Private Sub BtnSaveParam_Click(sender As Object, e As EventArgs)
        If Not ValidateSystemParam() Then Return

        Try
            Dim paramType As String = GetParamTypeValue(cmbParamType.SelectedIndex)

            If currentParamId = 0 Then
                ' 新增参数
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"param_key", txtParamKey.Text.Trim()},
                    {"param_value", txtParamValue.Text.Trim()},
                    {"param_type", paramType},
                    {"description", txtParamDescription.Text.Trim()},
                    {"is_active", chkIsActive.Checked}
                }

                Dim query As String = "
                    INSERT INTO system_params (param_key, param_value, param_type, description, is_active)
                    VALUES (@param_key, @param_value, @param_type, @description, @is_active)"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("系统参数添加成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' 修改参数
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"id", currentParamId},
                    {"param_key", txtParamKey.Text.Trim()},
                    {"param_value", txtParamValue.Text.Trim()},
                    {"param_type", paramType},
                    {"description", txtParamDescription.Text.Trim()},
                    {"is_active", chkIsActive.Checked}
                }

                Dim query As String = "
                    UPDATE system_params
                    SET param_key = @param_key, param_value = @param_value, param_type = @param_type,
                        description = @description, is_active = @is_active
                    WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("系统参数修改成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            LoadSystemParams()
            ClearSystemParamForm()

        Catch ex As Exception
            MessageBox.Show($"保存系统参数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnDeleteParam_Click(sender As Object, e As EventArgs)
        If currentParamId = 0 Then
            MessageBox.Show("请先选择要删除的参数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        If MessageBox.Show("确定要删除选中的系统参数吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim parameters As New Dictionary(Of String, Object) From {{"id", currentParamId}}
                Dim query As String = "DELETE FROM system_params WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("系统参数删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadSystemParams()
                ClearSystemParamForm()

            Catch ex As Exception
                MessageBox.Show($"删除系统参数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnRefreshParams_Click(sender As Object, e As EventArgs)
        LoadSystemParams()
        ClearSystemParamForm()
    End Sub

    Private Sub BtnSaveBusinessParam_Click(sender As Object, e As EventArgs)
        If Not ValidateBusinessParam() Then Return

        Try
            If currentBusinessParamId = 0 Then
                ' 新增业务参数
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"param_key", txtBusinessKey.Text.Trim()},
                    {"param_value", txtBusinessValue.Text.Trim()},
                    {"category", cmbBusinessCategory.SelectedItem.ToString()},
                    {"description", txtBusinessDescription.Text.Trim()}
                }

                Dim query As String = "
                    INSERT INTO business_params (param_key, param_value, category, description)
                    VALUES (@param_key, @param_value, @category, @description)"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("业务参数添加成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' 修改业务参数
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"id", currentBusinessParamId},
                    {"param_key", txtBusinessKey.Text.Trim()},
                    {"param_value", txtBusinessValue.Text.Trim()},
                    {"category", cmbBusinessCategory.SelectedItem.ToString()},
                    {"description", txtBusinessDescription.Text.Trim()}
                }

                Dim query As String = "
                    UPDATE business_params
                    SET param_key = @param_key, param_value = @param_value, category = @category,
                        description = @description
                    WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("业务参数修改成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            LoadBusinessParams()
            ClearBusinessParamForm()

        Catch ex As Exception
            MessageBox.Show($"保存业务参数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnDeleteBusinessParam_Click(sender As Object, e As EventArgs)
        If currentBusinessParamId = 0 Then
            MessageBox.Show("请先选择要删除的业务参数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        If MessageBox.Show("确定要删除选中的业务参数吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim parameters As New Dictionary(Of String, Object) From {{"id", currentBusinessParamId}}
                Dim query As String = "DELETE FROM business_params WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("业务参数删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadBusinessParams()
                ClearBusinessParamForm()

            Catch ex As Exception
                MessageBox.Show($"删除业务参数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnRefreshBusinessParams_Click(sender As Object, e As EventArgs)
        LoadBusinessParams()
        ClearBusinessParamForm()
    End Sub

    Private Sub BtnSaveDictItem_Click(sender As Object, e As EventArgs)
        If Not ValidateDataDict() Then Return

        Try
            If currentDictId = 0 Then
                ' 新增字典项
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"dict_type", txtDictType.Text.Trim()},
                    {"dict_code", txtDictCode.Text.Trim()},
                    {"dict_name", txtDictName.Text.Trim()},
                    {"dict_value", txtDictValue.Text.Trim()},
                    {"sort_order", Convert.ToInt32(txtDictSort.Text)},
                    {"is_active", chkDictActive.Checked}
                }

                Dim query As String = "
                    INSERT INTO data_dictionary (dict_type, dict_code, dict_name, dict_value, sort_order, is_active)
                    VALUES (@dict_type, @dict_code, @dict_name, @dict_value, @sort_order, @is_active)"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("数据字典项添加成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' 修改字典项
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"id", currentDictId},
                    {"dict_type", txtDictType.Text.Trim()},
                    {"dict_code", txtDictCode.Text.Trim()},
                    {"dict_name", txtDictName.Text.Trim()},
                    {"dict_value", txtDictValue.Text.Trim()},
                    {"sort_order", Convert.ToInt32(txtDictSort.Text)},
                    {"is_active", chkDictActive.Checked}
                }

                Dim query As String = "
                    UPDATE data_dictionary
                    SET dict_type = @dict_type, dict_code = @dict_code, dict_name = @dict_name,
                        dict_value = @dict_value, sort_order = @sort_order, is_active = @is_active
                    WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("数据字典项修改成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            LoadDataDictionary()
            ClearDataDictForm()

        Catch ex As Exception
            MessageBox.Show($"保存数据字典项失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnDeleteDictItem_Click(sender As Object, e As EventArgs)
        If currentDictId = 0 Then
            MessageBox.Show("请先选择要删除的字典项！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        If MessageBox.Show("确定要删除选中的数据字典项吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim parameters As New Dictionary(Of String, Object) From {{"id", currentDictId}}
                Dim query As String = "DELETE FROM data_dictionary WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("数据字典项删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadDataDictionary()
                ClearDataDictForm()

            Catch ex As Exception
                MessageBox.Show($"删除数据字典项失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnRefreshDict_Click(sender As Object, e As EventArgs)
        LoadDataDictionary()
        ClearDataDictForm()
    End Sub

    Private Sub BtnBackupDatabase_Click(sender As Object, e As EventArgs)
        If MessageBox.Show("确定要备份数据库吗？这可能需要一些时间。", "确认备份", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                ' 这里可以实现数据库备份逻辑
                ' 更新最后备份时间
                Dim backupTime As String = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"param_key", "last_backup_time"},
                    {"param_value", backupTime}
                }

                Dim query As String = "
                    INSERT INTO system_params (param_key, param_value, param_type, description, is_active)
                    VALUES (@param_key, @param_value, 'string', '最后备份时间', 1)
                    ON DUPLICATE KEY UPDATE param_value = @param_value"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("数据库备份完成！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadSystemInfo()

            Catch ex As Exception
                MessageBox.Show($"数据库备份失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnClearLogs_Click(sender As Object, e As EventArgs)
        If MessageBox.Show("确定要清理系统日志吗？", "确认清理", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                ' 这里可以实现日志清理逻辑
                MessageBox.Show("系统日志清理完成！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Catch ex As Exception
                MessageBox.Show($"清理日志失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnSystemMaintenance_Click(sender As Object, e As EventArgs)
        MessageBox.Show("系统维护功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    ' 辅助方法
    Private Function GetParamTypeValue(index As Integer) As String
        Select Case index
            Case 0
                Return "string"
            Case 1
                Return "number"
            Case 2
                Return "boolean"
            Case 3
                Return "date"
            Case Else
                Return "string"
        End Select
    End Function

    Private Function ValidateSystemParam() As Boolean
        If String.IsNullOrWhiteSpace(txtParamKey.Text) Then
            MessageBox.Show("请输入参数键！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtParamKey.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtParamValue.Text) Then
            MessageBox.Show("请输入参数值！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtParamValue.Focus()
            Return False
        End If

        If cmbParamType.SelectedIndex = -1 Then
            MessageBox.Show("请选择参数类型！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbParamType.Focus()
            Return False
        End If

        Return True
    End Function

    Private Function ValidateBusinessParam() As Boolean
        If String.IsNullOrWhiteSpace(txtBusinessKey.Text) Then
            MessageBox.Show("请输入参数键！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtBusinessKey.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtBusinessValue.Text) Then
            MessageBox.Show("请输入参数值！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtBusinessValue.Focus()
            Return False
        End If

        If cmbBusinessCategory.SelectedIndex = -1 Then
            MessageBox.Show("请选择参数分类！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbBusinessCategory.Focus()
            Return False
        End If

        Return True
    End Function

    Private Function ValidateDataDict() As Boolean
        If String.IsNullOrWhiteSpace(txtDictType.Text) Then
            MessageBox.Show("请输入字典类型！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtDictType.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtDictCode.Text) Then
            MessageBox.Show("请输入字典编码！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtDictCode.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtDictName.Text) Then
            MessageBox.Show("请输入字典名称！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtDictName.Focus()
            Return False
        End If

        Dim sortOrder As Integer
        If Not Integer.TryParse(txtDictSort.Text, sortOrder) Then
            MessageBox.Show("排序必须是数字！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtDictSort.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub ClearSystemParamForm()
        txtParamKey.Clear()
        txtParamValue.Clear()
        txtParamDescription.Clear()
        cmbParamType.SelectedIndex = -1
        chkIsActive.Checked = True
        currentParamId = 0
    End Sub

    Private Sub ClearBusinessParamForm()
        txtBusinessKey.Clear()
        txtBusinessValue.Clear()
        txtBusinessDescription.Clear()
        cmbBusinessCategory.SelectedIndex = -1
        currentBusinessParamId = 0
    End Sub

    Private Sub ClearDataDictForm()
        txtDictType.Clear()
        txtDictCode.Clear()
        txtDictName.Clear()
        txtDictValue.Clear()
        txtDictSort.Text = "0"
        chkDictActive.Checked = True
        currentDictId = 0
    End Sub

    Private Sub ApplyPermissions()
        ' 检查系统设置权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.SystemSettings) Then
            MessageBox.Show("您没有权限访问系统设置功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 系统设置权限用户可以访问所有功能
        ' 这里可以根据需要进一步细化权限控制
    End Sub
End Class
