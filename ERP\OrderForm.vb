Imports System.Windows.Forms
Imports MySql.Data.MySqlClient

Public Class OrderForm
    Inherits Form

    Private dgvOrders As DataGridView
    Private txtOrderNumber As TextBox
    Private cmbCustomer As ComboBox
    Private dtpOrderDate As DateTimePicker
    Private dtpDeliveryDate As DateTimePicker
    Private cmbOrderStatus As ComboBox
    Private txtTotalAmount As TextBox
    Private txtTaxRate As TextBox
    Private txtRemarks As TextBox

    Private btnAdd As Button
    Private btnEdit As Button
    Private btnDelete As Button
    Private btnRefresh As Button
    Private btnSearch As Button
    Private btnClear As Button
    Private btnViewDetails As Button

    Private currentOrderId As Integer = 0

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadData()
        LoadCustomers()
        SetupOrderStatus()
    End Sub

    Private Sub SetupUI()
        ' 数据表格
        dgvOrders = New DataGridView()
        dgvOrders.Location = New Point(12, 12)
        dgvOrders.Size = New Size(1160, 350)
        dgvOrders.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        dgvOrders.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvOrders.MultiSelect = False
        dgvOrders.ReadOnly = True
        dgvOrders.AllowUserToAddRows = False
        dgvOrders.AllowUserToDeleteRows = False
        Me.Controls.Add(dgvOrders)

        ' 订单编号
        Dim lblOrderNumber As New Label()
        lblOrderNumber.Text = "订单编号:"
        lblOrderNumber.Location = New Point(12, 380)
        lblOrderNumber.Size = New Size(80, 23)
        Me.Controls.Add(lblOrderNumber)

        txtOrderNumber = New TextBox()
        txtOrderNumber.Location = New Point(100, 378)
        txtOrderNumber.Size = New Size(150, 23)
        txtOrderNumber.ReadOnly = True
        Me.Controls.Add(txtOrderNumber)

        ' 客户
        Dim lblCustomer As New Label()
        lblCustomer.Text = "客户:"
        lblCustomer.Location = New Point(270, 380)
        lblCustomer.Size = New Size(50, 23)
        Me.Controls.Add(lblCustomer)

        cmbCustomer = New ComboBox()
        cmbCustomer.Location = New Point(330, 378)
        cmbCustomer.Size = New Size(200, 23)
        cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbCustomer)

        ' 订单日期
        Dim lblOrderDate As New Label()
        lblOrderDate.Text = "订单日期:"
        lblOrderDate.Location = New Point(550, 380)
        lblOrderDate.Size = New Size(70, 23)
        Me.Controls.Add(lblOrderDate)

        dtpOrderDate = New DateTimePicker()
        dtpOrderDate.Location = New Point(630, 378)
        dtpOrderDate.Size = New Size(120, 23)
        dtpOrderDate.Format = DateTimePickerFormat.Short
        Me.Controls.Add(dtpOrderDate)

        ' 交货日期
        Dim lblDeliveryDate As New Label()
        lblDeliveryDate.Text = "交货日期:"
        lblDeliveryDate.Location = New Point(770, 380)
        lblDeliveryDate.Size = New Size(70, 23)
        Me.Controls.Add(lblDeliveryDate)

        dtpDeliveryDate = New DateTimePicker()
        dtpDeliveryDate.Location = New Point(850, 378)
        dtpDeliveryDate.Size = New Size(120, 23)
        dtpDeliveryDate.Format = DateTimePickerFormat.Short
        Me.Controls.Add(dtpDeliveryDate)

        ' 订单状态
        Dim lblOrderStatus As New Label()
        lblOrderStatus.Text = "订单状态:"
        lblOrderStatus.Location = New Point(12, 420)
        lblOrderStatus.Size = New Size(80, 23)
        Me.Controls.Add(lblOrderStatus)

        cmbOrderStatus = New ComboBox()
        cmbOrderStatus.Location = New Point(100, 418)
        cmbOrderStatus.Size = New Size(150, 23)
        cmbOrderStatus.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbOrderStatus)

        ' 总金额
        Dim lblTotalAmount As New Label()
        lblTotalAmount.Text = "总金额:"
        lblTotalAmount.Location = New Point(270, 420)
        lblTotalAmount.Size = New Size(50, 23)
        Me.Controls.Add(lblTotalAmount)

        txtTotalAmount = New TextBox()
        txtTotalAmount.Location = New Point(330, 418)
        txtTotalAmount.Size = New Size(120, 23)
        txtTotalAmount.ReadOnly = True
        Me.Controls.Add(txtTotalAmount)

        ' 税率
        Dim lblTaxRate As New Label()
        lblTaxRate.Text = "税率(%):"
        lblTaxRate.Location = New Point(470, 420)
        lblTaxRate.Size = New Size(60, 23)
        Me.Controls.Add(lblTaxRate)

        txtTaxRate = New TextBox()
        txtTaxRate.Location = New Point(540, 418)
        txtTaxRate.Size = New Size(80, 23)
        txtTaxRate.Text = "13.00"
        Me.Controls.Add(txtTaxRate)

        ' 备注
        Dim lblRemarks As New Label()
        lblRemarks.Text = "备注:"
        lblRemarks.Location = New Point(12, 460)
        lblRemarks.Size = New Size(50, 23)
        Me.Controls.Add(lblRemarks)

        txtRemarks = New TextBox()
        txtRemarks.Location = New Point(70, 458)
        txtRemarks.Size = New Size(500, 60)
        txtRemarks.Multiline = True
        txtRemarks.ScrollBars = ScrollBars.Vertical
        Me.Controls.Add(txtRemarks)

        ' 按钮
        btnAdd = New Button()
        btnAdd.Text = "新增"
        btnAdd.Location = New Point(12, 540)
        btnAdd.Size = New Size(80, 30)
        Me.Controls.Add(btnAdd)

        btnEdit = New Button()
        btnEdit.Text = "修改"
        btnEdit.Location = New Point(102, 540)
        btnEdit.Size = New Size(80, 30)
        Me.Controls.Add(btnEdit)

        btnDelete = New Button()
        btnDelete.Text = "删除"
        btnDelete.Location = New Point(192, 540)
        btnDelete.Size = New Size(80, 30)
        Me.Controls.Add(btnDelete)

        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(282, 540)
        btnRefresh.Size = New Size(80, 30)
        Me.Controls.Add(btnRefresh)

        btnSearch = New Button()
        btnSearch.Text = "搜索"
        btnSearch.Location = New Point(372, 540)
        btnSearch.Size = New Size(80, 30)
        Me.Controls.Add(btnSearch)

        btnClear = New Button()
        btnClear.Text = "清空"
        btnClear.Location = New Point(462, 540)
        btnClear.Size = New Size(80, 30)
        Me.Controls.Add(btnClear)

        btnViewDetails = New Button()
        btnViewDetails.Text = "查看明细"
        btnViewDetails.Location = New Point(552, 540)
        btnViewDetails.Size = New Size(80, 30)
        Me.Controls.Add(btnViewDetails)

        ' 事件处理
        AddHandler dgvOrders.SelectionChanged, AddressOf DgvOrders_SelectionChanged
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        AddHandler btnSearch.Click, AddressOf BtnSearch_Click
        AddHandler btnClear.Click, AddressOf BtnClear_Click
        AddHandler btnViewDetails.Click, AddressOf BtnViewDetails_Click
    End Sub



    Private Sub LoadData()
        Try
            Dim query As String = "
                SELECT 
                    o.id,
                    o.order_number AS '订单编号',
                    c.customer_name AS '客户名称',
                    o.order_date AS '订单日期',
                    o.delivery_date AS '交货日期',
                    CASE o.order_status
                        WHEN 'pending' THEN '待处理'
                        WHEN 'confirmed' THEN '已确认'
                        WHEN 'in_production' THEN '生产中'
                        WHEN 'ready' THEN '待发货'
                        WHEN 'shipped' THEN '已发货'
                        WHEN 'delivered' THEN '已交货'
                        WHEN 'completed' THEN '已完成'
                        WHEN 'cancelled' THEN '已取消'
                        ELSE o.order_status
                    END AS '订单状态',
                    o.total_amount AS '总金额',
                    o.tax_rate AS '税率',
                    o.remarks AS '备注'
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                ORDER BY o.order_date DESC, o.order_number DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvOrders.DataSource = dt

            ' 隐藏ID列
            If dgvOrders.Columns.Contains("id") Then
                dgvOrders.Columns("id").Visible = False
            End If

            ' 设置列宽
            If dgvOrders.Columns.Count > 0 Then
                dgvOrders.Columns("订单编号").Width = 120
                dgvOrders.Columns("客户名称").Width = 150
                dgvOrders.Columns("订单日期").Width = 100
                dgvOrders.Columns("交货日期").Width = 100
                dgvOrders.Columns("订单状态").Width = 80
                dgvOrders.Columns("总金额").Width = 100
                dgvOrders.Columns("税率").Width = 80
                dgvOrders.Columns("备注").Width = 200
            End If

        Catch ex As Exception
            MessageBox.Show($"加载数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCustomers()
        Try
            Dim query As String = "SELECT id, customer_name FROM customers WHERE is_active = TRUE ORDER BY customer_name"
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)

            cmbCustomer.DisplayMember = "customer_name"
            cmbCustomer.ValueMember = "id"
            cmbCustomer.DataSource = dt

        Catch ex As Exception
            MessageBox.Show($"加载客户数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetupOrderStatus()
        Dim statusItems As New List(Of KeyValuePair(Of String, String)) From {
            New KeyValuePair(Of String, String)("pending", "待处理"),
            New KeyValuePair(Of String, String)("confirmed", "已确认"),
            New KeyValuePair(Of String, String)("in_production", "生产中"),
            New KeyValuePair(Of String, String)("ready", "待发货"),
            New KeyValuePair(Of String, String)("shipped", "已发货"),
            New KeyValuePair(Of String, String)("delivered", "已交货"),
            New KeyValuePair(Of String, String)("completed", "已完成"),
            New KeyValuePair(Of String, String)("cancelled", "已取消")
        }

        cmbOrderStatus.DisplayMember = "Value"
        cmbOrderStatus.ValueMember = "Key"
        cmbOrderStatus.DataSource = statusItems
    End Sub

    Private Sub DgvOrders_SelectionChanged(sender As Object, e As EventArgs)
        If dgvOrders.SelectedRows.Count > 0 Then
            Dim row As DataGridViewRow = dgvOrders.SelectedRows(0)
            currentOrderId = Convert.ToInt32(row.Cells("id").Value)

            txtOrderNumber.Text = row.Cells("订单编号").Value?.ToString()

            ' 设置客户
            Dim customerName As String = row.Cells("客户名称").Value?.ToString()
            For i As Integer = 0 To cmbCustomer.Items.Count - 1
                Dim item As DataRowView = CType(cmbCustomer.Items(i), DataRowView)
                If item("customer_name").ToString() = customerName Then
                    cmbCustomer.SelectedIndex = i
                    Exit For
                End If
            Next

            ' 设置日期
            If row.Cells("订单日期").Value IsNot Nothing Then
                dtpOrderDate.Value = Convert.ToDateTime(row.Cells("订单日期").Value)
            End If

            If row.Cells("交货日期").Value IsNot Nothing AndAlso Not IsDBNull(row.Cells("交货日期").Value) Then
                dtpDeliveryDate.Value = Convert.ToDateTime(row.Cells("交货日期").Value)
            End If

            ' 设置订单状态
            Dim statusText As String = row.Cells("订单状态").Value?.ToString()
            For i As Integer = 0 To cmbOrderStatus.Items.Count - 1
                Dim item As KeyValuePair(Of String, String) = CType(cmbOrderStatus.Items(i), KeyValuePair(Of String, String))
                If item.Value = statusText Then
                    cmbOrderStatus.SelectedIndex = i
                    Exit For
                End If
            Next

            txtTotalAmount.Text = row.Cells("总金额").Value?.ToString()

            If row.Cells("税率").Value IsNot Nothing Then
                txtTaxRate.Text = (Convert.ToDecimal(row.Cells("税率").Value) * 100).ToString("F2")
            End If

            txtRemarks.Text = row.Cells("备注").Value?.ToString()
        End If
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        If ValidateInput() Then
            Try
                ' 生成订单编号
                Dim orderNumber As String = GenerateOrderNumber()

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"order_number", orderNumber},
                    {"customer_id", cmbCustomer.SelectedValue},
                    {"order_date", dtpOrderDate.Value.Date},
                    {"delivery_date", dtpDeliveryDate.Value.Date},
                    {"order_status", cmbOrderStatus.SelectedValue},
                    {"tax_rate", Convert.ToDecimal(txtTaxRate.Text) / 100},
                    {"remarks", txtRemarks.Text.Trim()}
                }

                Dim query As String = "
                    INSERT INTO orders (order_number, customer_id, order_date, delivery_date, order_status, tax_rate, remarks)
                    VALUES (@order_number, @customer_id, @order_date, @delivery_date, @order_status, @tax_rate, @remarks)"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("订单添加成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadData()
                ClearForm()

            Catch ex As Exception
                MessageBox.Show($"添加订单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If currentOrderId = 0 Then
            MessageBox.Show("请先选择要修改的订单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If ValidateInput() Then
            Try
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"id", currentOrderId},
                    {"customer_id", cmbCustomer.SelectedValue},
                    {"order_date", dtpOrderDate.Value.Date},
                    {"delivery_date", dtpDeliveryDate.Value.Date},
                    {"order_status", cmbOrderStatus.SelectedValue},
                    {"tax_rate", Convert.ToDecimal(txtTaxRate.Text) / 100},
                    {"remarks", txtRemarks.Text.Trim()}
                }

                Dim query As String = "
                    UPDATE orders
                    SET customer_id = @customer_id, order_date = @order_date, delivery_date = @delivery_date,
                        order_status = @order_status, tax_rate = @tax_rate, remarks = @remarks
                    WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("订单修改成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadData()

            Catch ex As Exception
                MessageBox.Show($"修改订单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If currentOrderId = 0 Then
            MessageBox.Show("请先选择要删除的订单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If MessageBox.Show("确定要删除选中的订单吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                ' 检查是否有订单明细
                Dim checkQuery As String = "SELECT COUNT(*) FROM order_details WHERE order_id = @id"
                Dim checkParams As New Dictionary(Of String, Object) From {{"id", currentOrderId}}
                Dim detailCount As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(checkQuery, checkParams))

                If detailCount > 0 Then
                    MessageBox.Show("该订单存在明细记录，无法删除！请先删除相关明细记录。", "删除失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If

                Dim parameters As New Dictionary(Of String, Object) From {{"id", currentOrderId}}
                Dim query As String = "DELETE FROM orders WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("订单删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadData()
                ClearForm()

            Catch ex As Exception
                MessageBox.Show($"删除订单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadData()
        ClearForm()
    End Sub

    Private Sub BtnSearch_Click(sender As Object, e As EventArgs)
        Dim searchForm As New OrderSearchForm()
        If searchForm.ShowDialog() = DialogResult.OK Then
            LoadFilteredData(searchForm.SearchCriteria)
        End If
    End Sub

    Private Sub BtnClear_Click(sender As Object, e As EventArgs)
        ClearForm()
    End Sub

    Private Sub BtnViewDetails_Click(sender As Object, e As EventArgs)
        If currentOrderId = 0 Then
            MessageBox.Show("请先选择要查看明细的订单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim detailForm As New OrderDetailForm(currentOrderId)
        detailForm.ShowDialog()

        ' 刷新订单总金额
        LoadData()
    End Sub

    Private Function ValidateInput() As Boolean
        If cmbCustomer.SelectedValue Is Nothing Then
            MessageBox.Show("请选择客户！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbCustomer.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtTaxRate.Text) Then
            MessageBox.Show("请输入税率！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtTaxRate.Focus()
            Return False
        End If

        Dim taxRate As Decimal
        If Not Decimal.TryParse(txtTaxRate.Text, taxRate) OrElse taxRate < 0 OrElse taxRate > 100 Then
            MessageBox.Show("税率必须是0-100之间的数字！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtTaxRate.Focus()
            Return False
        End If

        If dtpDeliveryDate.Value < dtpOrderDate.Value Then
            MessageBox.Show("交货日期不能早于订单日期！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            dtpDeliveryDate.Focus()
            Return False
        End If

        Return True
    End Function

    Private Function GenerateOrderNumber() As String
        Dim today As String = DateTime.Now.ToString("yyyyMMdd")
        Dim query As String = "SELECT COUNT(*) FROM orders WHERE order_number LIKE @pattern"
        Dim parameters As New Dictionary(Of String, Object) From {{"pattern", $"SO{today}%"}}

        Dim count As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(query, parameters))
        Return $"SO{today}{(count + 1).ToString("D3")}"
    End Function

    Private Sub ClearForm()
        currentOrderId = 0
        txtOrderNumber.Clear()
        If cmbCustomer.Items.Count > 0 Then cmbCustomer.SelectedIndex = 0
        dtpOrderDate.Value = DateTime.Now
        dtpDeliveryDate.Value = DateTime.Now.AddDays(7)
        If cmbOrderStatus.Items.Count > 0 Then cmbOrderStatus.SelectedIndex = 0
        txtTotalAmount.Clear()
        txtTaxRate.Text = "13.00"
        txtRemarks.Clear()
    End Sub

    Private Sub LoadFilteredData(criteria As Dictionary(Of String, Object))
        Try
            Dim whereClause As New List(Of String)
            Dim parameters As New Dictionary(Of String, Object)

            If criteria.ContainsKey("order_number") AndAlso Not String.IsNullOrWhiteSpace(criteria("order_number").ToString()) Then
                whereClause.Add("o.order_number LIKE @order_number")
                parameters("order_number") = $"%{criteria("order_number")}%"
            End If

            If criteria.ContainsKey("customer_id") AndAlso criteria("customer_id") IsNot Nothing Then
                whereClause.Add("o.customer_id = @customer_id")
                parameters("customer_id") = criteria("customer_id")
            End If

            If criteria.ContainsKey("order_status") AndAlso Not String.IsNullOrWhiteSpace(criteria("order_status").ToString()) Then
                whereClause.Add("o.order_status = @order_status")
                parameters("order_status") = criteria("order_status")
            End If

            If criteria.ContainsKey("date_from") AndAlso criteria("date_from") IsNot Nothing Then
                whereClause.Add("o.order_date >= @date_from")
                parameters("date_from") = criteria("date_from")
            End If

            If criteria.ContainsKey("date_to") AndAlso criteria("date_to") IsNot Nothing Then
                whereClause.Add("o.order_date <= @date_to")
                parameters("date_to") = criteria("date_to")
            End If

            Dim query As String = "
                SELECT
                    o.id,
                    o.order_number AS '订单编号',
                    c.customer_name AS '客户名称',
                    o.order_date AS '订单日期',
                    o.delivery_date AS '交货日期',
                    CASE o.order_status
                        WHEN 'pending' THEN '待处理'
                        WHEN 'confirmed' THEN '已确认'
                        WHEN 'in_production' THEN '生产中'
                        WHEN 'ready' THEN '待发货'
                        WHEN 'shipped' THEN '已发货'
                        WHEN 'delivered' THEN '已交货'
                        WHEN 'completed' THEN '已完成'
                        WHEN 'cancelled' THEN '已取消'
                        ELSE o.order_status
                    END AS '订单状态',
                    o.total_amount AS '总金额',
                    o.tax_rate AS '税率',
                    o.remarks AS '备注'
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id"

            If whereClause.Count > 0 Then
                query += " WHERE " + String.Join(" AND ", whereClause)
            End If

            query += " ORDER BY o.order_date DESC, o.order_number DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvOrders.DataSource = dt

            ' 隐藏ID列
            If dgvOrders.Columns.Contains("id") Then
                dgvOrders.Columns("id").Visible = False
            End If

        Catch ex As Exception
            MessageBox.Show($"搜索数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
