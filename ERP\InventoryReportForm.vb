Imports System.Data
Imports System.Windows.Forms

Public Class InventoryReportForm
    Inherits Form

    Private dgvInventory As DataGridView
    Private btnQuery As Button
    Private btnExport As Button

    Public Sub New()
        Me.Text = "库存报表"
        Me.Size = New Size(900, 600)
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        dgvInventory = New DataGridView() With {
            .Dock = DockStyle.Fill,
            .ReadOnly = True,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        }
        btnQuery = New Button() With {.Text = "查询", .Width = 80}
        btnExport = New Button() With {.Text = "导出Excel", .Width = 100}

        AddHandler btnQuery.Click, AddressOf QueryInventory
        AddHandler btnExport.Click, AddressOf ExportToExcel

        Dim panel As New FlowLayoutPanel() With {.Dock = DockStyle.Top, .Height = 40}
        panel.Controls.Add(btnQuery)
        panel.Controls.Add(btnExport)

        Me.Controls.Add(dgvInventory)
        Me.Controls.Add(panel)
    End Sub

    Private Sub QueryInventory(sender As Object, e As EventArgs)
        ' TODO: 从数据库查询库存数据
        Dim dt As DataTable = GetInventoryData()
        dgvInventory.DataSource = dt
    End Sub

    Private Function GetInventoryData() As DataTable
        ' TODO: 实际应从数据库查询
        Dim dt As New DataTable()
        dt.Columns.Add("物料编号")
        dt.Columns.Add("物料名称")
        dt.Columns.Add("规格")
        dt.Columns.Add("库存数量")
        dt.Columns.Add("库位")
        ' 示例数据
        dt.Rows.Add("1001", "螺丝", "M6*20", 500, "A01")
        dt.Rows.Add("1002", "螺母", "M6", 300, "A02")
        Return dt
    End Function

    Private Sub ExportToExcel(sender As Object, e As EventArgs)
        ' TODO: 实现导出功能
        MessageBox.Show("导出功能开发中...", "提示")
    End Sub
End Class 