Imports System.Drawing
Imports System.Windows.Forms

''' <summary>
''' 响应式布局管理器 - 提供自动缩放和响应式布局功能
''' </summary>
Public Class ResponsiveLayoutManager

    Private Shared ReadOnly BreakPoints As New Dictionary(Of String, Integer) From {
        {"xs", 576},    ' 超小屏幕
        {"sm", 768},    ' 小屏幕
        {"md", 992},    ' 中等屏幕
        {"lg", 1200},   ' 大屏幕
        {"xl", 1400}    ' 超大屏幕
    }

    ''' <summary>
    ''' 应用响应式布局到窗体
    ''' </summary>
    Public Shared Sub ApplyResponsiveLayout(form As Form)
        ' 设置窗体自动缩放
        form.AutoScaleMode = AutoScaleMode.Dpi
        form.AutoScaleDimensions = New SizeF(96.0F, 96.0F)
        
        ' 添加窗体大小改变事件
        AddHandler form.Resize, Sub(s, e) OnFormResize(form)
        AddHandler form.Load, Sub(s, e) OnFormResize(form)
        
        ' 递归应用到所有控件
        ApplyResponsiveToControls(form.Controls, form.Size)
    End Sub

    ''' <summary>
    ''' 窗体大小改变时的处理
    ''' </summary>
    Private Shared Sub OnFormResize(form As Form)
        Dim currentBreakpoint As String = GetCurrentBreakpoint(form.Width)
        ApplyBreakpointStyles(form, currentBreakpoint)
        AdjustControlSizes(form.Controls, form.Size)
    End Sub

    ''' <summary>
    ''' 获取当前断点
    ''' </summary>
    Private Shared Function GetCurrentBreakpoint(width As Integer) As String
        If width < BreakPoints("xs") Then
            Return "xs"
        ElseIf width < BreakPoints("sm") Then
            Return "sm"
        ElseIf width < BreakPoints("md") Then
            Return "md"
        ElseIf width < BreakPoints("lg") Then
            Return "lg"
        Else
            Return "xl"
        End If
    End Function

    ''' <summary>
    ''' 应用断点样式
    ''' </summary>
    Private Shared Sub ApplyBreakpointStyles(form As Form, breakpoint As String)
        Select Case breakpoint
            Case "xs", "sm"
                ' 小屏幕：隐藏一些非必要元素，调整布局
                ApplyMobileLayout(form)
            Case "md"
                ' 中等屏幕：标准布局
                ApplyTabletLayout(form)
            Case "lg", "xl"
                ' 大屏幕：完整布局
                ApplyDesktopLayout(form)
        End Select
    End Sub

    ''' <summary>
    ''' 应用移动端布局
    ''' </summary>
    Private Shared Sub ApplyMobileLayout(form As Form)
        ' 调整字体大小
        AdjustFontSizes(form.Controls, 0.9F)
        
        ' 调整控件间距
        AdjustControlSpacing(form.Controls, 0.8F)
    End Sub

    ''' <summary>
    ''' 应用平板布局
    ''' </summary>
    Private Shared Sub ApplyTabletLayout(form As Form)
        ' 标准字体大小
        AdjustFontSizes(form.Controls, 1.0F)
        
        ' 标准控件间距
        AdjustControlSpacing(form.Controls, 1.0F)
    End Sub

    ''' <summary>
    ''' 应用桌面布局
    ''' </summary>
    Private Shared Sub ApplyDesktopLayout(form As Form)
        ' 稍大的字体
        AdjustFontSizes(form.Controls, 1.1F)
        
        ' 更大的控件间距
        AdjustControlSpacing(form.Controls, 1.2F)
    End Sub

    ''' <summary>
    ''' 递归应用响应式到控件集合
    ''' </summary>
    Private Shared Sub ApplyResponsiveToControls(controls As Control.ControlCollection, formSize As Size)
        For Each control As Control In controls
            ApplyResponsiveToControl(control, formSize)
            If control.HasChildren Then
                ApplyResponsiveToControls(control.Controls, formSize)
            End If
        Next
    End Sub

    ''' <summary>
    ''' 应用响应式到单个控件
    ''' </summary>
    Private Shared Sub ApplyResponsiveToControl(control As Control, formSize As Size)
        ' 设置控件的响应式属性
        control.Tag = If(control.Tag, New ResponsiveInfo())
        
        ' 根据控件类型应用不同的响应式规则
        Select Case control.GetType()
            Case GetType(DataGridView)
                ApplyResponsiveToDataGridView(DirectCast(control, DataGridView), formSize)
            Case GetType(Panel)
                ApplyResponsiveToPanel(DirectCast(control, Panel), formSize)
            Case GetType(Button)
                ApplyResponsiveToButton(DirectCast(control, Button), formSize)
            Case GetType(TabControl)
                ApplyResponsiveToTabControl(DirectCast(control, TabControl), formSize)
        End Select
    End Sub

    ''' <summary>
    ''' 应用响应式到DataGridView
    ''' </summary>
    Private Shared Sub ApplyResponsiveToDataGridView(dgv As DataGridView, formSize As Size)
        ' 根据窗体大小调整列宽模式
        If formSize.Width < BreakPoints("md") Then
            dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells
        Else
            dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        End If
        
        ' 调整行高
        Dim scaleFactor As Single = Math.Min(formSize.Width / 1200.0F, 1.2F)
        dgv.RowTemplate.Height = CInt(25 * scaleFactor)
    End Sub

    ''' <summary>
    ''' 应用响应式到面板
    ''' </summary>
    Private Shared Sub ApplyResponsiveToPanel(panel As Panel, formSize As Size)
        ' 根据屏幕大小调整面板布局
        If formSize.Width < BreakPoints("md") Then
            ' 小屏幕：垂直布局
            For Each control As Control In panel.Controls
                If TypeOf control Is Button Then
                    control.Dock = DockStyle.Top
                End If
            Next
        End If
    End Sub

    ''' <summary>
    ''' 应用响应式到按钮
    ''' </summary>
    Private Shared Sub ApplyResponsiveToButton(button As Button, formSize As Size)
        ' 根据屏幕大小调整按钮尺寸
        Dim scaleFactor As Single = Math.Min(formSize.Width / 1200.0F, 1.2F)
        Dim baseHeight As Integer = 35
        button.Height = CInt(baseHeight * scaleFactor)
    End Sub

    ''' <summary>
    ''' 应用响应式到选项卡控件
    ''' </summary>
    Private Shared Sub ApplyResponsiveToTabControl(tabControl As TabControl, formSize As Size)
        ' 小屏幕时调整选项卡显示
        If formSize.Width < BreakPoints("sm") Then
            tabControl.Multiline = True
        Else
            tabControl.Multiline = False
        End If
    End Sub

    ''' <summary>
    ''' 调整控件大小
    ''' </summary>
    Private Shared Sub AdjustControlSizes(controls As Control.ControlCollection, formSize As Size)
        Dim scaleFactor As Single = Math.Min(formSize.Width / 1200.0F, 1.2F)
        
        For Each control As Control In controls
            ' 保存原始大小信息
            If control.Tag Is Nothing Then
                control.Tag = New ResponsiveInfo() With {
                    .OriginalSize = control.Size,
                    .OriginalLocation = control.Location
                }
            End If
            
            Dim info As ResponsiveInfo = DirectCast(control.Tag, ResponsiveInfo)
            
            ' 应用缩放
            If Not (TypeOf control Is Form) Then
                control.Size = New Size(
                    CInt(info.OriginalSize.Width * scaleFactor),
                    CInt(info.OriginalSize.Height * scaleFactor)
                )
            End If
            
            ' 递归处理子控件
            If control.HasChildren Then
                AdjustControlSizes(control.Controls, formSize)
            End If
        Next
    End Sub

    ''' <summary>
    ''' 调整字体大小
    ''' </summary>
    Private Shared Sub AdjustFontSizes(controls As Control.ControlCollection, scaleFactor As Single)
        For Each control As Control In controls
            If control.Font IsNot Nothing Then
                Dim newSize As Single = control.Font.Size * scaleFactor
                If newSize >= 6 AndAlso newSize <= 24 Then ' 限制字体大小范围
                    control.Font = New Font(control.Font.FontFamily, newSize, control.Font.Style)
                End If
            End If
            
            If control.HasChildren Then
                AdjustFontSizes(control.Controls, scaleFactor)
            End If
        Next
    End Sub

    ''' <summary>
    ''' 调整控件间距
    ''' </summary>
    Private Shared Sub AdjustControlSpacing(controls As Control.ControlCollection, scaleFactor As Single)
        For Each control As Control In controls
            If TypeOf control Is Panel Then
                Dim panel As Panel = DirectCast(control, Panel)
                Dim newPadding As Integer = CInt(panel.Padding.All * scaleFactor)
                panel.Padding = New Padding(newPadding)
            End If
            
            If control.HasChildren Then
                AdjustControlSpacing(control.Controls, scaleFactor)
            End If
        Next
    End Sub

    ''' <summary>
    ''' 创建响应式网格布局
    ''' </summary>
    Public Shared Function CreateResponsiveGrid(parent As Control, columns As Integer) As TableLayoutPanel
        Dim grid As New TableLayoutPanel()
        grid.Dock = DockStyle.Fill
        grid.ColumnCount = columns
        grid.AutoSize = True
        grid.AutoSizeMode = AutoSizeMode.GrowAndShrink
        
        ' 设置列样式
        For i As Integer = 0 To columns - 1
            grid.ColumnStyles.Add(New ColumnStyle(SizeType.Percent, 100.0F / columns))
        Next
        
        parent.Controls.Add(grid)
        Return grid
    End Function

    ''' <summary>
    ''' 创建响应式卡片
    ''' </summary>
    Public Shared Function CreateResponsiveCard(title As String, content As Control) As Panel
        Dim card As New Panel()
        card.BackColor = UIThemeManager.SurfaceColor
        card.Padding = New Padding(15)
        card.Margin = New Padding(10)
        
        ' 标题
        Dim lblTitle As New Label()
        lblTitle.Text = title
        lblTitle.Font = UIThemeManager.HeaderFont
        lblTitle.ForeColor = UIThemeManager.TextPrimaryColor
        lblTitle.Dock = DockStyle.Top
        lblTitle.Height = 30
        card.Controls.Add(lblTitle)
        
        ' 内容
        content.Dock = DockStyle.Fill
        card.Controls.Add(content)
        
        ' 添加阴影效果
        AddHandler card.Paint, Sub(s, e)
                                   Dim rect As Rectangle = card.ClientRectangle
                                   Using pen As New Pen(UIThemeManager.BorderColor, 1)
                                       e.Graphics.DrawRectangle(pen, 0, 0, rect.Width - 1, rect.Height - 1)
                                   End Using
                               End Sub
        
        Return card
    End Function

End Class

''' <summary>
''' 响应式信息类
''' </summary>
Public Class ResponsiveInfo
    Public Property OriginalSize As Size
    Public Property OriginalLocation As Point
    Public Property OriginalFont As Font
End Class
