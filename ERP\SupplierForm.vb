Imports System.Windows.Forms
Imports System.Data

''' <summary>
''' 供应商管理窗体
''' </summary>
Public Class SupplierForm
    Inherits Form

    Private dgvSuppliers As DataGridView
    Private txtSupplierCode As TextBox
    Private txtSupplierName As TextBox
    Private txtContactPerson As TextBox
    Private txtPhone As TextBox
    Private txtEmail As TextBox
    Private txtAddress As TextBox
    Private txtTaxNumber As TextBox
    Private txtPaymentTerms As TextBox
    Private chkIsActive As CheckBox

    Private btnNew As Button
    Private btnEdit As Button
    Private btnSave As Button
    Private btnCancel As Button
    Private btnDelete As Button
    Private btnRefresh As Button

    Private isEditMode As Boolean = False
    Private currentSupplierId As Integer = 0

    Public Sub New()
        InitializeComponent()
        LoadSuppliers()
        ApplyPermissions()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "供应商管理"
        Me.Size = New Size(1000, 600)
        Me.StartPosition = FormStartPosition.CenterParent

        ' 创建数据网格
        dgvSuppliers = New DataGridView()
        dgvSuppliers.Location = New Point(10, 10)
        dgvSuppliers.Size = New Size(600, 400)
        dgvSuppliers.ReadOnly = True
        dgvSuppliers.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvSuppliers.MultiSelect = False
        dgvSuppliers.AllowUserToAddRows = False
        dgvSuppliers.AllowUserToDeleteRows = False
        AddHandler dgvSuppliers.SelectionChanged, AddressOf dgvSuppliers_SelectionChanged
        Me.Controls.Add(dgvSuppliers)

        ' 创建输入控件
        CreateInputControls()

        ' 创建按钮
        CreateButtons()

        ' 设置初始状态
        SetFormMode(False)
    End Sub

    Private Sub CreateInputControls()
        Dim startX As Integer = 630
        Dim startY As Integer = 30
        Dim labelWidth As Integer = 80
        Dim textWidth As Integer = 200
        Dim rowHeight As Integer = 35

        ' 供应商编码
        Dim lblSupplierCode As New Label()
        lblSupplierCode.Text = "供应商编码:"
        lblSupplierCode.Location = New Point(startX, startY)
        lblSupplierCode.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblSupplierCode)

        txtSupplierCode = New TextBox()
        txtSupplierCode.Location = New Point(startX + labelWidth + 10, startY)
        txtSupplierCode.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtSupplierCode)

        ' 供应商名称
        startY += rowHeight
        Dim lblSupplierName As New Label()
        lblSupplierName.Text = "供应商名称:"
        lblSupplierName.Location = New Point(startX, startY)
        lblSupplierName.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblSupplierName)

        txtSupplierName = New TextBox()
        txtSupplierName.Location = New Point(startX + labelWidth + 10, startY)
        txtSupplierName.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtSupplierName)

        ' 联系人
        startY += rowHeight
        Dim lblContactPerson As New Label()
        lblContactPerson.Text = "联系人:"
        lblContactPerson.Location = New Point(startX, startY)
        lblContactPerson.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblContactPerson)

        txtContactPerson = New TextBox()
        txtContactPerson.Location = New Point(startX + labelWidth + 10, startY)
        txtContactPerson.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtContactPerson)

        ' 电话
        startY += rowHeight
        Dim lblPhone As New Label()
        lblPhone.Text = "电话:"
        lblPhone.Location = New Point(startX, startY)
        lblPhone.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblPhone)

        txtPhone = New TextBox()
        txtPhone.Location = New Point(startX + labelWidth + 10, startY)
        txtPhone.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtPhone)

        ' 邮箱
        startY += rowHeight
        Dim lblEmail As New Label()
        lblEmail.Text = "邮箱:"
        lblEmail.Location = New Point(startX, startY)
        lblEmail.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblEmail)

        txtEmail = New TextBox()
        txtEmail.Location = New Point(startX + labelWidth + 10, startY)
        txtEmail.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtEmail)

        ' 地址
        startY += rowHeight
        Dim lblAddress As New Label()
        lblAddress.Text = "地址:"
        lblAddress.Location = New Point(startX, startY)
        lblAddress.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblAddress)

        txtAddress = New TextBox()
        txtAddress.Location = New Point(startX + labelWidth + 10, startY)
        txtAddress.Size = New Size(textWidth, 50)
        txtAddress.Multiline = True
        Me.Controls.Add(txtAddress)

        ' 税号
        startY += 60
        Dim lblTaxNumber As New Label()
        lblTaxNumber.Text = "税号:"
        lblTaxNumber.Location = New Point(startX, startY)
        lblTaxNumber.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblTaxNumber)

        txtTaxNumber = New TextBox()
        txtTaxNumber.Location = New Point(startX + labelWidth + 10, startY)
        txtTaxNumber.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtTaxNumber)

        ' 付款条件
        startY += rowHeight
        Dim lblPaymentTerms As New Label()
        lblPaymentTerms.Text = "付款条件:"
        lblPaymentTerms.Location = New Point(startX, startY)
        lblPaymentTerms.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblPaymentTerms)

        txtPaymentTerms = New TextBox()
        txtPaymentTerms.Location = New Point(startX + labelWidth + 10, startY)
        txtPaymentTerms.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtPaymentTerms)

        ' 是否启用
        startY += rowHeight
        chkIsActive = New CheckBox()
        chkIsActive.Text = "启用"
        chkIsActive.Location = New Point(startX + labelWidth + 10, startY)
        chkIsActive.Checked = True
        Me.Controls.Add(chkIsActive)
    End Sub

    Private Sub CreateButtons()
        Dim buttonY As Integer = 450
        Dim buttonWidth As Integer = 80
        Dim buttonHeight As Integer = 30
        Dim buttonSpacing As Integer = 90

        btnNew = New Button()
        btnNew.Text = "新增"
        btnNew.Location = New Point(10, buttonY)
        btnNew.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnNew.Click, AddressOf btnNew_Click
        Me.Controls.Add(btnNew)

        btnEdit = New Button()
        btnEdit.Text = "编辑"
        btnEdit.Location = New Point(10 + buttonSpacing, buttonY)
        btnEdit.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnEdit.Click, AddressOf btnEdit_Click
        Me.Controls.Add(btnEdit)

        btnSave = New Button()
        btnSave.Text = "保存"
        btnSave.Location = New Point(10 + buttonSpacing * 2, buttonY)
        btnSave.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnSave.Click, AddressOf btnSave_Click
        Me.Controls.Add(btnSave)

        btnCancel = New Button()
        btnCancel.Text = "取消"
        btnCancel.Location = New Point(10 + buttonSpacing * 3, buttonY)
        btnCancel.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnCancel.Click, AddressOf btnCancel_Click
        Me.Controls.Add(btnCancel)

        btnDelete = New Button()
        btnDelete.Text = "删除"
        btnDelete.Location = New Point(10 + buttonSpacing * 4, buttonY)
        btnDelete.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnDelete.Click, AddressOf btnDelete_Click
        Me.Controls.Add(btnDelete)

        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(10 + buttonSpacing * 5, buttonY)
        btnRefresh.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnRefresh.Click, AddressOf btnRefresh_Click
        Me.Controls.Add(btnRefresh)
    End Sub

    Private Sub LoadSuppliers()
        Try
            Dim sql = "SELECT id, supplier_code AS '供应商编码', supplier_name AS '供应商名称', " &
                     "contact_person AS '联系人', phone AS '电话', email AS '邮箱', " &
                     "CASE WHEN is_active THEN '是' ELSE '否' END AS '启用状态' " &
                     "FROM suppliers ORDER BY supplier_code"

            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvSuppliers.DataSource = dt

            ' 隐藏ID列
            If dgvSuppliers.Columns.Contains("id") Then
                dgvSuppliers.Columns("id").Visible = False
            End If

            ' 设置列宽
            dgvSuppliers.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        Catch ex As Exception
            MessageBox.Show($"加载供应商数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub dgvSuppliers_SelectionChanged(sender As Object, e As EventArgs)
        If dgvSuppliers.CurrentRow IsNot Nothing AndAlso Not isEditMode Then
            LoadSupplierDetails()
        End If
    End Sub

    Private Sub LoadSupplierDetails()
        Try
            If dgvSuppliers.CurrentRow Is Nothing Then Return

            currentSupplierId = Convert.ToInt32(dgvSuppliers.CurrentRow.Cells("id").Value)

            Dim sql = "SELECT * FROM suppliers WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentSupplierId}}
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql, parameters)

            If dt.Rows.Count > 0 Then
                Dim row = dt.Rows(0)
                txtSupplierCode.Text = row("supplier_code").ToString()
                txtSupplierName.Text = row("supplier_name").ToString()
                txtContactPerson.Text = row("contact_person").ToString()
                txtPhone.Text = row("phone").ToString()
                txtEmail.Text = row("email").ToString()
                txtAddress.Text = row("address").ToString()
                txtTaxNumber.Text = row("tax_number").ToString()
                txtPaymentTerms.Text = row("payment_terms").ToString()
                chkIsActive.Checked = Convert.ToBoolean(row("is_active"))
            End If
        Catch ex As Exception
            MessageBox.Show($"加载供应商详情时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode

        ' 设置输入控件的启用状态
        txtSupplierCode.Enabled = editMode
        txtSupplierName.Enabled = editMode
        txtContactPerson.Enabled = editMode
        txtPhone.Enabled = editMode
        txtEmail.Enabled = editMode
        txtAddress.Enabled = editMode
        txtTaxNumber.Enabled = editMode
        txtPaymentTerms.Enabled = editMode
        chkIsActive.Enabled = editMode

        ' 设置按钮的启用状态（结合权限控制）
        btnNew.Enabled = Not editMode AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageSuppliers)
        btnEdit.Enabled = Not editMode AndAlso currentSupplierId > 0 AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageSuppliers)
        btnDelete.Enabled = Not editMode AndAlso currentSupplierId > 0 AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageSuppliers)
        btnSave.Enabled = editMode
        btnCancel.Enabled = editMode
        btnRefresh.Enabled = Not editMode

        dgvSuppliers.Enabled = Not editMode
    End Sub

    ''' <summary>
    ''' 应用权限控制
    ''' </summary>
    Private Sub ApplyPermissions()
        ' 检查基本查看权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewSuppliers) Then
            MessageBox.Show("您没有权限访问供应商管理功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 根据权限设置按钮状态
        SetFormMode(False)
    End Sub

    Private Sub ClearForm()
        txtSupplierCode.Clear()
        txtSupplierName.Clear()
        txtContactPerson.Clear()
        txtPhone.Clear()
        txtEmail.Clear()
        txtAddress.Clear()
        txtTaxNumber.Clear()
        txtPaymentTerms.Clear()
        chkIsActive.Checked = True
        currentSupplierId = 0
    End Sub

    ' 按钮事件处理
    Private Sub btnNew_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageSuppliers) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        ClearForm()
        SetFormMode(True)
        txtSupplierCode.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageSuppliers) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        If currentSupplierId <= 0 Then
            MessageBox.Show("请先选择要编辑的供应商", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        SetFormMode(True)
        txtSupplierName.Focus()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs)
        If Not ValidateForm() Then Return

        Try
            If currentSupplierId = 0 Then
                ' 新增
                Dim sql = "INSERT INTO suppliers (supplier_code, supplier_name, contact_person, phone, email, address, tax_number, payment_terms, is_active) " &
                         "VALUES (@supplier_code, @supplier_name, @contact_person, @phone, @email, @address, @tax_number, @payment_terms, @is_active)"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@supplier_code", txtSupplierCode.Text.Trim()},
                    {"@supplier_name", txtSupplierName.Text.Trim()},
                    {"@contact_person", txtContactPerson.Text.Trim()},
                    {"@phone", txtPhone.Text.Trim()},
                    {"@email", txtEmail.Text.Trim()},
                    {"@address", txtAddress.Text.Trim()},
                    {"@tax_number", txtTaxNumber.Text.Trim()},
                    {"@payment_terms", txtPaymentTerms.Text.Trim()},
                    {"@is_active", chkIsActive.Checked}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("供应商信息保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' 更新
                Dim sql = "UPDATE suppliers SET supplier_name = @supplier_name, contact_person = @contact_person, phone = @phone, " &
                         "email = @email, address = @address, tax_number = @tax_number, payment_terms = @payment_terms, " &
                         "is_active = @is_active WHERE id = @id"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@supplier_name", txtSupplierName.Text.Trim()},
                    {"@contact_person", txtContactPerson.Text.Trim()},
                    {"@phone", txtPhone.Text.Trim()},
                    {"@email", txtEmail.Text.Trim()},
                    {"@address", txtAddress.Text.Trim()},
                    {"@tax_number", txtTaxNumber.Text.Trim()},
                    {"@payment_terms", txtPaymentTerms.Text.Trim()},
                    {"@is_active", chkIsActive.Checked},
                    {"@id", currentSupplierId}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("供应商信息更新成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            SetFormMode(False)
            LoadSuppliers()
        Catch ex As Exception
            MessageBox.Show($"保存供应商信息时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        SetFormMode(False)
        If currentSupplierId > 0 Then
            LoadSupplierDetails()
        Else
            ClearForm()
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs)
        If currentSupplierId <= 0 Then
            MessageBox.Show("请先选择要删除的供应商", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If MessageBox.Show("确定要删除选中的供应商吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim sql = "DELETE FROM suppliers WHERE id = @id"
                Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentSupplierId}}

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("供应商删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ClearForm()
                LoadSuppliers()
            Catch ex As Exception
                MessageBox.Show($"删除供应商时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs)
        LoadSuppliers()
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtSupplierCode.Text) Then
            MessageBox.Show("请输入供应商编码", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtSupplierCode.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtSupplierName.Text) Then
            MessageBox.Show("请输入供应商名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtSupplierName.Focus()
            Return False
        End If

        Return True
    End Function
End Class
