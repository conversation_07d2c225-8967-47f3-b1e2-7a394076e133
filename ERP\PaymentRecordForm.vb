Imports System.Windows.Forms
Imports System.Drawing

Public Class PaymentRecordForm
    Inherits Form

    ' 控件声明
    Private txtPaymentNumber As TextBox
    Private dtpPaymentDate As DateTimePicker
    Private txtPaymentAmount As TextBox
    Private cmbPaymentMethod As ComboBox
    Private txtBankAccount As TextBox
    Private txtReferenceNumber As TextBox
    Private txtDescription As TextBox
    Private lblReferenceInfo As Label

    ' 按钮控件
    Private btnSave As Button
    Private btnCancel As Button

    Private referenceId As Integer
    Private paymentType As String
    Private originalAmount As Decimal
    Private paidAmount As Decimal
    Private outstandingAmount As Decimal

    Public Sub New(refId As Integer, pType As String)
        referenceId = refId
        paymentType = pType
        InitializeComponent()
        LoadReferenceInfo()
        GeneratePaymentNumber()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = If(paymentType = "receivable", "应收款收款", "应付款付款")
        Me.Size = New Size(500, 450)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False

        Dim startY As Integer = 30
        Dim labelWidth As Integer = 100
        Dim controlWidth As Integer = 250
        Dim rowHeight As Integer = 35

        ' 参考信息标签
        lblReferenceInfo = New Label()
        lblReferenceInfo.Location = New Point(20, startY)
        lblReferenceInfo.Size = New Size(450, 60)
        lblReferenceInfo.BorderStyle = BorderStyle.FixedSingle
        lblReferenceInfo.BackColor = Color.LightYellow
        Me.Controls.Add(lblReferenceInfo)

        startY += 80

        ' 收付款单号
        Dim lblPaymentNumber As New Label()
        lblPaymentNumber.Text = If(paymentType = "receivable", "收款单号:", "付款单号:")
        lblPaymentNumber.Location = New Point(20, startY)
        lblPaymentNumber.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblPaymentNumber)

        txtPaymentNumber = New TextBox()
        txtPaymentNumber.Location = New Point(20 + labelWidth, startY)
        txtPaymentNumber.Size = New Size(controlWidth, 20)
        txtPaymentNumber.ReadOnly = True
        Me.Controls.Add(txtPaymentNumber)

        ' 收付款日期
        startY += rowHeight
        Dim lblPaymentDate As New Label()
        lblPaymentDate.Text = If(paymentType = "receivable", "收款日期:", "付款日期:")
        lblPaymentDate.Location = New Point(20, startY)
        lblPaymentDate.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblPaymentDate)

        dtpPaymentDate = New DateTimePicker()
        dtpPaymentDate.Location = New Point(20 + labelWidth, startY)
        dtpPaymentDate.Size = New Size(controlWidth, 20)
        dtpPaymentDate.Format = DateTimePickerFormat.Short
        dtpPaymentDate.Value = DateTime.Now
        Me.Controls.Add(dtpPaymentDate)

        ' 收付款金额
        startY += rowHeight
        Dim lblPaymentAmount As New Label()
        lblPaymentAmount.Text = If(paymentType = "receivable", "收款金额:", "付款金额:")
        lblPaymentAmount.Location = New Point(20, startY)
        lblPaymentAmount.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblPaymentAmount)

        txtPaymentAmount = New TextBox()
        txtPaymentAmount.Location = New Point(20 + labelWidth, startY)
        txtPaymentAmount.Size = New Size(controlWidth, 20)
        Me.Controls.Add(txtPaymentAmount)

        ' 收付款方式
        startY += rowHeight
        Dim lblPaymentMethod As New Label()
        lblPaymentMethod.Text = If(paymentType = "receivable", "收款方式:", "付款方式:")
        lblPaymentMethod.Location = New Point(20, startY)
        lblPaymentMethod.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblPaymentMethod)

        cmbPaymentMethod = New ComboBox()
        cmbPaymentMethod.Location = New Point(20 + labelWidth, startY)
        cmbPaymentMethod.Size = New Size(controlWidth, 20)
        cmbPaymentMethod.Items.AddRange({"现金", "银行转账", "支票", "承兑汇票", "信用卡", "其他"})
        cmbPaymentMethod.SelectedIndex = 1
        Me.Controls.Add(cmbPaymentMethod)

        ' 银行账户
        startY += rowHeight
        Dim lblBankAccount As New Label()
        lblBankAccount.Text = "银行账户:"
        lblBankAccount.Location = New Point(20, startY)
        lblBankAccount.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblBankAccount)

        txtBankAccount = New TextBox()
        txtBankAccount.Location = New Point(20 + labelWidth, startY)
        txtBankAccount.Size = New Size(controlWidth, 20)
        Me.Controls.Add(txtBankAccount)

        ' 参考号码
        startY += rowHeight
        Dim lblReferenceNumber As New Label()
        lblReferenceNumber.Text = "参考号码:"
        lblReferenceNumber.Location = New Point(20, startY)
        lblReferenceNumber.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblReferenceNumber)

        txtReferenceNumber = New TextBox()
        txtReferenceNumber.Location = New Point(20 + labelWidth, startY)
        txtReferenceNumber.Size = New Size(controlWidth, 20)
        Me.Controls.Add(txtReferenceNumber)

        ' 备注说明
        startY += rowHeight
        Dim lblDescription As New Label()
        lblDescription.Text = "备注说明:"
        lblDescription.Location = New Point(20, startY)
        lblDescription.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblDescription)

        txtDescription = New TextBox()
        txtDescription.Location = New Point(20 + labelWidth, startY)
        txtDescription.Size = New Size(controlWidth, 60)
        txtDescription.Multiline = True
        Me.Controls.Add(txtDescription)

        ' 按钮
        startY += 80
        btnSave = New Button()
        btnSave.Text = "保存"
        btnSave.Location = New Point(200, startY)
        btnSave.Size = New Size(80, 30)
        AddHandler btnSave.Click, AddressOf btnSave_Click
        Me.Controls.Add(btnSave)

        btnCancel = New Button()
        btnCancel.Text = "取消"
        btnCancel.Location = New Point(290, startY)
        btnCancel.Size = New Size(80, 30)
        AddHandler btnCancel.Click, AddressOf btnCancel_Click
        Me.Controls.Add(btnCancel)
    End Sub

    Private Sub LoadReferenceInfo()
        Try
            Dim sql As String
            If paymentType = "receivable" Then
                sql = "SELECT ar.ar_number, c.customer_name, ar.original_amount, ar.paid_amount, ar.outstanding_amount " &
                     "FROM accounts_receivable ar " &
                     "LEFT JOIN customers c ON ar.customer_id = c.id " &
                     "WHERE ar.id = @id"
            Else
                sql = "SELECT ap.ap_number, s.supplier_name, ap.original_amount, ap.paid_amount, ap.outstanding_amount " &
                     "FROM accounts_payable ap " &
                     "LEFT JOIN suppliers s ON ap.supplier_id = s.id " &
                     "WHERE ap.id = @id"
            End If

            Dim parameters As New Dictionary(Of String, Object) From {{"@id", referenceId}}
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql, parameters)

            If dt.Rows.Count > 0 Then
                Dim row = dt.Rows(0)
                originalAmount = Convert.ToDecimal(row(2))
                paidAmount = Convert.ToDecimal(row(3))
                outstandingAmount = Convert.ToDecimal(row(4))

                If paymentType = "receivable" Then
                    lblReferenceInfo.Text = $"应收单号: {row(0)}" & vbCrLf &
                                          $"客户: {row(1)}" & vbCrLf &
                                          $"原始金额: {originalAmount:F2}  已收金额: {paidAmount:F2}  未收金额: {outstandingAmount:F2}"
                Else
                    lblReferenceInfo.Text = $"应付单号: {row(0)}" & vbCrLf &
                                          $"供应商: {row(1)}" & vbCrLf &
                                          $"原始金额: {originalAmount:F2}  已付金额: {paidAmount:F2}  未付金额: {outstandingAmount:F2}"
                End If

                ' 默认设置收付款金额为未付金额
                txtPaymentAmount.Text = outstandingAmount.ToString("F2")
            End If
        Catch ex As Exception
            MessageBox.Show($"加载参考信息时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub GeneratePaymentNumber()
        Try
            Dim prefix = If(paymentType = "receivable", "REC", "PAY")
            Dim today = DateTime.Now.ToString("yyyyMMdd")
            Dim sql = "SELECT COUNT(*) FROM payment_records WHERE payment_number LIKE @pattern"
            Dim parameters As New Dictionary(Of String, Object) From {{"@pattern", $"{prefix}{today}%"}}
            Dim count = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(sql, parameters))
            txtPaymentNumber.Text = $"{prefix}{today}{(count + 1).ToString("D3")}"
        Catch ex As Exception
            Dim prefix = If(paymentType = "receivable", "REC", "PAY")
            txtPaymentNumber.Text = $"{prefix}{DateTime.Now:yyyyMMdd}001"
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs)
        If Not ValidateForm() Then Return

        Try
            Dim paymentAmount = Convert.ToDecimal(txtPaymentAmount.Text)
            Dim newPaidAmount = paidAmount + paymentAmount
            Dim newOutstandingAmount = originalAmount - newPaidAmount

            ' 插入收付款记录
            Dim sql = "INSERT INTO payment_records (payment_number, payment_type, reference_id, payment_date, payment_amount, " &
                     "payment_method, bank_account, reference_number, description, created_by) " &
                     "VALUES (@payment_number, @payment_type, @reference_id, @payment_date, @payment_amount, " &
                     "@payment_method, @bank_account, @reference_number, @description, @created_by)"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"@payment_number", txtPaymentNumber.Text.Trim()},
                {"@payment_type", paymentType},
                {"@reference_id", referenceId},
                {"@payment_date", dtpPaymentDate.Value.Date},
                {"@payment_amount", paymentAmount},
                {"@payment_method", cmbPaymentMethod.Text},
                {"@bank_account", If(String.IsNullOrEmpty(txtBankAccount.Text.Trim()), DBNull.Value, txtBankAccount.Text.Trim())},
                {"@reference_number", If(String.IsNullOrEmpty(txtReferenceNumber.Text.Trim()), DBNull.Value, txtReferenceNumber.Text.Trim())},
                {"@description", If(String.IsNullOrEmpty(txtDescription.Text.Trim()), DBNull.Value, txtDescription.Text.Trim())},
                {"@created_by", UserSession.CurrentUserId}
            }

            DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)

            ' 更新应收/应付账款
            Dim updateSql As String
            Dim newStatus As String = "pending"
            If newOutstandingAmount <= 0 Then
                newStatus = "paid"
            ElseIf newPaidAmount > 0 Then
                newStatus = "partial"
            End If

            If paymentType = "receivable" Then
                updateSql = "UPDATE accounts_receivable SET paid_amount = @paid_amount, outstanding_amount = @outstanding_amount, status = @status WHERE id = @id"
            Else
                updateSql = "UPDATE accounts_payable SET paid_amount = @paid_amount, outstanding_amount = @outstanding_amount, status = @status WHERE id = @id"
            End If

            Dim updateParameters As New Dictionary(Of String, Object) From {
                {"@paid_amount", newPaidAmount},
                {"@outstanding_amount", newOutstandingAmount},
                {"@status", newStatus},
                {"@id", referenceId}
            }

            DatabaseManager.Instance.ExecuteNonQuery(updateSql, updateParameters)

            MessageBox.Show(If(paymentType = "receivable", "收款记录保存成功", "付款记录保存成功"), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.DialogResult = DialogResult.OK
            Me.Close()
        Catch ex As Exception
            MessageBox.Show($"保存收付款记录时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrEmpty(txtPaymentAmount.Text.Trim()) Then
            MessageBox.Show(If(paymentType = "receivable", "请输入收款金额", "请输入付款金额"), "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPaymentAmount.Focus()
            Return False
        End If

        Dim amount As Decimal
        If Not Decimal.TryParse(txtPaymentAmount.Text, amount) OrElse amount <= 0 Then
            MessageBox.Show(If(paymentType = "receivable", "请输入有效的收款金额", "请输入有效的付款金额"), "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPaymentAmount.Focus()
            Return False
        End If

        If amount > outstandingAmount Then
            MessageBox.Show($"收付款金额不能超过未付金额 {outstandingAmount:F2}", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPaymentAmount.Focus()
            Return False
        End If

        If String.IsNullOrEmpty(cmbPaymentMethod.Text.Trim()) Then
            MessageBox.Show(If(paymentType = "receivable", "请选择收款方式", "请选择付款方式"), "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbPaymentMethod.Focus()
            Return False
        End If

        Return True
    End Function
End Class
