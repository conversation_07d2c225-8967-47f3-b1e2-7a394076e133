Imports System.Windows.Forms
Imports System.Data

''' <summary>
''' 物料管理窗体
''' </summary>
Public Class MaterialForm
    Inherits Form

    Private dgvMaterials As DataGridView
    Private txtMaterialCode As TextBox
    Private txtMaterialName As TextBox
    Private txtDrawingNumber As TextBox
    Private txtVersion As TextBox
    Private cmbUnit As ComboBox
    Private txtCategory As TextBox
    Private txtSpecification As TextBox
    Private txtSafetyStock As TextBox
    Private txtMinOrderQty As TextBox
    Private txtStandardPrice As TextBox
    Private chkIsActive As CheckBox

    Private btnNew As Button
    Private btnEdit As Button
    Private btnSave As Button
    Private btnCancel As Button
    Private btnDelete As Button
    Private btnRefresh As Button

    Private isEditMode As Boolean = False
    Private currentMaterialId As Integer = 0

    Public Sub New()
        InitializeComponent()
        LoadMaterials()
        ApplyPermissions()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "物料管理"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterParent

        ' 创建数据网格
        dgvMaterials = New DataGridView()
        dgvMaterials.Location = New Point(10, 10)
        dgvMaterials.Size = New Size(800, 400)
        dgvMaterials.ReadOnly = True
        dgvMaterials.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvMaterials.MultiSelect = False
        dgvMaterials.AllowUserToAddRows = False
        dgvMaterials.AllowUserToDeleteRows = False
        AddHandler dgvMaterials.SelectionChanged, AddressOf dgvMaterials_SelectionChanged
        Me.Controls.Add(dgvMaterials)

        ' 创建输入控件
        CreateInputControls()

        ' 创建按钮
        CreateButtons()

        ' 设置初始状态
        SetFormMode(False)
    End Sub

    Private Sub CreateInputControls()
        Dim startX As Integer = 830
        Dim startY As Integer = 30
        Dim labelWidth As Integer = 80
        Dim textWidth As Integer = 200
        Dim rowHeight As Integer = 35

        ' 物料编码
        Dim lblMaterialCode As New Label()
        lblMaterialCode.Text = "物料编码:"
        lblMaterialCode.Location = New Point(startX, startY)
        lblMaterialCode.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblMaterialCode)

        txtMaterialCode = New TextBox()
        txtMaterialCode.Location = New Point(startX + labelWidth + 10, startY)
        txtMaterialCode.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtMaterialCode)

        ' 物料名称
        startY += rowHeight
        Dim lblMaterialName As New Label()
        lblMaterialName.Text = "物料名称:"
        lblMaterialName.Location = New Point(startX, startY)
        lblMaterialName.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblMaterialName)

        txtMaterialName = New TextBox()
        txtMaterialName.Location = New Point(startX + labelWidth + 10, startY)
        txtMaterialName.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtMaterialName)

        ' 图号
        startY += rowHeight
        Dim lblDrawingNumber As New Label()
        lblDrawingNumber.Text = "图号:"
        lblDrawingNumber.Location = New Point(startX, startY)
        lblDrawingNumber.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblDrawingNumber)

        txtDrawingNumber = New TextBox()
        txtDrawingNumber.Location = New Point(startX + labelWidth + 10, startY)
        txtDrawingNumber.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtDrawingNumber)

        ' 版本
        startY += rowHeight
        Dim lblVersion As New Label()
        lblVersion.Text = "版本:"
        lblVersion.Location = New Point(startX, startY)
        lblVersion.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblVersion)

        txtVersion = New TextBox()
        txtVersion.Location = New Point(startX + labelWidth + 10, startY)
        txtVersion.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtVersion)

        ' 单位
        startY += rowHeight
        Dim lblUnit As New Label()
        lblUnit.Text = "单位:"
        lblUnit.Location = New Point(startX, startY)
        lblUnit.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblUnit)

        cmbUnit = New ComboBox()
        cmbUnit.Location = New Point(startX + labelWidth + 10, startY)
        cmbUnit.Size = New Size(textWidth, 25)
        cmbUnit.DropDownStyle = ComboBoxStyle.DropDownList
        cmbUnit.Items.AddRange({"PCS", "KG", "M", "M2", "M3", "L", "SET", "BOX"})
        cmbUnit.SelectedIndex = 0
        Me.Controls.Add(cmbUnit)

        ' 类别
        startY += rowHeight
        Dim lblCategory As New Label()
        lblCategory.Text = "类别:"
        lblCategory.Location = New Point(startX, startY)
        lblCategory.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblCategory)

        txtCategory = New TextBox()
        txtCategory.Location = New Point(startX + labelWidth + 10, startY)
        txtCategory.Size = New Size(textWidth, 25)
        Me.Controls.Add(txtCategory)

        ' 规格
        startY += rowHeight
        Dim lblSpecification As New Label()
        lblSpecification.Text = "规格:"
        lblSpecification.Location = New Point(startX, startY)
        lblSpecification.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblSpecification)

        txtSpecification = New TextBox()
        txtSpecification.Location = New Point(startX + labelWidth + 10, startY)
        txtSpecification.Size = New Size(textWidth, 50)
        txtSpecification.Multiline = True
        Me.Controls.Add(txtSpecification)

        ' 安全库存
        startY += 60
        Dim lblSafetyStock As New Label()
        lblSafetyStock.Text = "安全库存:"
        lblSafetyStock.Location = New Point(startX, startY)
        lblSafetyStock.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblSafetyStock)

        txtSafetyStock = New TextBox()
        txtSafetyStock.Location = New Point(startX + labelWidth + 10, startY)
        txtSafetyStock.Size = New Size(textWidth, 25)
        txtSafetyStock.Text = "0"
        Me.Controls.Add(txtSafetyStock)

        ' 最小订购量
        startY += rowHeight
        Dim lblMinOrderQty As New Label()
        lblMinOrderQty.Text = "最小订购量:"
        lblMinOrderQty.Location = New Point(startX, startY)
        lblMinOrderQty.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblMinOrderQty)

        txtMinOrderQty = New TextBox()
        txtMinOrderQty.Location = New Point(startX + labelWidth + 10, startY)
        txtMinOrderQty.Size = New Size(textWidth, 25)
        txtMinOrderQty.Text = "1"
        Me.Controls.Add(txtMinOrderQty)

        ' 标准价格
        startY += rowHeight
        Dim lblStandardPrice As New Label()
        lblStandardPrice.Text = "标准价格:"
        lblStandardPrice.Location = New Point(startX, startY)
        lblStandardPrice.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblStandardPrice)

        txtStandardPrice = New TextBox()
        txtStandardPrice.Location = New Point(startX + labelWidth + 10, startY)
        txtStandardPrice.Size = New Size(textWidth, 25)
        txtStandardPrice.Text = "0"
        Me.Controls.Add(txtStandardPrice)

        ' 是否启用
        startY += rowHeight
        chkIsActive = New CheckBox()
        chkIsActive.Text = "启用"
        chkIsActive.Location = New Point(startX + labelWidth + 10, startY)
        chkIsActive.Checked = True
        Me.Controls.Add(chkIsActive)
    End Sub

    Private Sub CreateButtons()
        Dim buttonY As Integer = 450
        Dim buttonWidth As Integer = 80
        Dim buttonHeight As Integer = 30
        Dim buttonSpacing As Integer = 90

        btnNew = New Button()
        btnNew.Text = "新增"
        btnNew.Location = New Point(10, buttonY)
        btnNew.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnNew.Click, AddressOf btnNew_Click
        Me.Controls.Add(btnNew)

        btnEdit = New Button()
        btnEdit.Text = "编辑"
        btnEdit.Location = New Point(10 + buttonSpacing, buttonY)
        btnEdit.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnEdit.Click, AddressOf btnEdit_Click
        Me.Controls.Add(btnEdit)

        btnSave = New Button()
        btnSave.Text = "保存"
        btnSave.Location = New Point(10 + buttonSpacing * 2, buttonY)
        btnSave.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnSave.Click, AddressOf btnSave_Click
        Me.Controls.Add(btnSave)

        btnCancel = New Button()
        btnCancel.Text = "取消"
        btnCancel.Location = New Point(10 + buttonSpacing * 3, buttonY)
        btnCancel.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnCancel.Click, AddressOf btnCancel_Click
        Me.Controls.Add(btnCancel)

        btnDelete = New Button()
        btnDelete.Text = "删除"
        btnDelete.Location = New Point(10 + buttonSpacing * 4, buttonY)
        btnDelete.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnDelete.Click, AddressOf btnDelete_Click
        Me.Controls.Add(btnDelete)

        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(10 + buttonSpacing * 5, buttonY)
        btnRefresh.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnRefresh.Click, AddressOf btnRefresh_Click
        Me.Controls.Add(btnRefresh)
    End Sub

    Private Sub LoadMaterials()
        Try
            Dim sql = "SELECT id, material_code AS '物料编码', material_name AS '物料名称', " &
                     "drawing_number AS '图号', version AS '版本', unit AS '单位', " &
                     "category AS '类别', standard_price AS '标准价格', " &
                     "CASE WHEN is_active THEN '是' ELSE '否' END AS '启用状态' " &
                     "FROM materials ORDER BY material_code"

            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvMaterials.DataSource = dt

            ' 隐藏ID列
            If dgvMaterials.Columns.Contains("id") Then
                dgvMaterials.Columns("id").Visible = False
            End If

            ' 设置列宽
            dgvMaterials.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        Catch ex As Exception
            MessageBox.Show($"加载物料数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub dgvMaterials_SelectionChanged(sender As Object, e As EventArgs)
        If dgvMaterials.CurrentRow IsNot Nothing AndAlso Not isEditMode Then
            LoadMaterialDetails()
        End If
    End Sub

    Private Sub LoadMaterialDetails()
        Try
            If dgvMaterials.CurrentRow Is Nothing Then Return

            currentMaterialId = Convert.ToInt32(dgvMaterials.CurrentRow.Cells("id").Value)

            Dim sql = "SELECT * FROM materials WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentMaterialId}}
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql, parameters)

            If dt.Rows.Count > 0 Then
                Dim row = dt.Rows(0)
                txtMaterialCode.Text = row("material_code").ToString()
                txtMaterialName.Text = row("material_name").ToString()
                txtDrawingNumber.Text = row("drawing_number").ToString()
                txtVersion.Text = row("version").ToString()
                cmbUnit.Text = row("unit").ToString()
                txtCategory.Text = row("category").ToString()
                txtSpecification.Text = row("specification").ToString()
                txtSafetyStock.Text = row("safety_stock").ToString()
                txtMinOrderQty.Text = row("min_order_qty").ToString()
                txtStandardPrice.Text = row("standard_price").ToString()
                chkIsActive.Checked = Convert.ToBoolean(row("is_active"))
            End If
        Catch ex As Exception
            MessageBox.Show($"加载物料详情时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode

        ' 设置输入控件的启用状态
        txtMaterialCode.Enabled = editMode
        txtMaterialName.Enabled = editMode
        txtDrawingNumber.Enabled = editMode
        txtVersion.Enabled = editMode
        cmbUnit.Enabled = editMode
        txtCategory.Enabled = editMode
        txtSpecification.Enabled = editMode
        txtSafetyStock.Enabled = editMode
        txtMinOrderQty.Enabled = editMode
        txtStandardPrice.Enabled = editMode
        chkIsActive.Enabled = editMode

        ' 设置按钮的启用状态（结合权限控制）
        btnNew.Enabled = Not editMode AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageMaterials)
        btnEdit.Enabled = Not editMode AndAlso currentMaterialId > 0 AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageMaterials)
        btnDelete.Enabled = Not editMode AndAlso currentMaterialId > 0 AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageMaterials)
        btnSave.Enabled = editMode
        btnCancel.Enabled = editMode
        btnRefresh.Enabled = Not editMode

        dgvMaterials.Enabled = Not editMode
    End Sub

    ''' <summary>
    ''' 应用权限控制
    ''' </summary>
    Private Sub ApplyPermissions()
        ' 检查基本查看权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewMaterials) Then
            MessageBox.Show("您没有权限访问物料管理功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 根据权限设置按钮状态
        SetFormMode(False)
    End Sub

    Private Sub ClearForm()
        txtMaterialCode.Clear()
        txtMaterialName.Clear()
        txtDrawingNumber.Clear()
        txtVersion.Clear()
        cmbUnit.SelectedIndex = 0
        txtCategory.Clear()
        txtSpecification.Clear()
        txtSafetyStock.Text = "0"
        txtMinOrderQty.Text = "1"
        txtStandardPrice.Text = "0"
        chkIsActive.Checked = True
        currentMaterialId = 0
    End Sub

    ' 按钮事件处理
    Private Sub btnNew_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageMaterials) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        ClearForm()
        SetFormMode(True)
        txtMaterialCode.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageMaterials) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        If currentMaterialId <= 0 Then
            MessageBox.Show("请先选择要编辑的物料", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        SetFormMode(True)
        txtMaterialName.Focus()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs)
        If Not ValidateForm() Then Return

        Try
            If currentMaterialId = 0 Then
                ' 新增
                Dim sql = "INSERT INTO materials (material_code, material_name, drawing_number, version, unit, category, specification, safety_stock, min_order_qty, standard_price, is_active) " &
                         "VALUES (@material_code, @material_name, @drawing_number, @version, @unit, @category, @specification, @safety_stock, @min_order_qty, @standard_price, @is_active)"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@material_code", txtMaterialCode.Text.Trim()},
                    {"@material_name", txtMaterialName.Text.Trim()},
                    {"@drawing_number", txtDrawingNumber.Text.Trim()},
                    {"@version", txtVersion.Text.Trim()},
                    {"@unit", cmbUnit.Text},
                    {"@category", txtCategory.Text.Trim()},
                    {"@specification", txtSpecification.Text.Trim()},
                    {"@safety_stock", Convert.ToDecimal(txtSafetyStock.Text)},
                    {"@min_order_qty", Convert.ToDecimal(txtMinOrderQty.Text)},
                    {"@standard_price", Convert.ToDecimal(txtStandardPrice.Text)},
                    {"@is_active", chkIsActive.Checked}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("物料信息保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' 更新
                Dim sql = "UPDATE materials SET material_name = @material_name, drawing_number = @drawing_number, version = @version, " &
                         "unit = @unit, category = @category, specification = @specification, safety_stock = @safety_stock, " &
                         "min_order_qty = @min_order_qty, standard_price = @standard_price, is_active = @is_active WHERE id = @id"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@material_name", txtMaterialName.Text.Trim()},
                    {"@drawing_number", txtDrawingNumber.Text.Trim()},
                    {"@version", txtVersion.Text.Trim()},
                    {"@unit", cmbUnit.Text},
                    {"@category", txtCategory.Text.Trim()},
                    {"@specification", txtSpecification.Text.Trim()},
                    {"@safety_stock", Convert.ToDecimal(txtSafetyStock.Text)},
                    {"@min_order_qty", Convert.ToDecimal(txtMinOrderQty.Text)},
                    {"@standard_price", Convert.ToDecimal(txtStandardPrice.Text)},
                    {"@is_active", chkIsActive.Checked},
                    {"@id", currentMaterialId}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("物料信息更新成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            SetFormMode(False)
            LoadMaterials()
        Catch ex As Exception
            MessageBox.Show($"保存物料信息时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        SetFormMode(False)
        If currentMaterialId > 0 Then
            LoadMaterialDetails()
        Else
            ClearForm()
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs)
        If currentMaterialId <= 0 Then
            MessageBox.Show("请先选择要删除的物料", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If MessageBox.Show("确定要删除选中的物料吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim sql = "DELETE FROM materials WHERE id = @id"
                Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentMaterialId}}

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("物料删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ClearForm()
                LoadMaterials()
            Catch ex As Exception
                MessageBox.Show($"删除物料时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs)
        LoadMaterials()
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtMaterialCode.Text) Then
            MessageBox.Show("请输入物料编码", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtMaterialCode.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtMaterialName.Text) Then
            MessageBox.Show("请输入物料名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtMaterialName.Focus()
            Return False
        End If

        ' 验证数字字段
        Dim safetyStock As Decimal
        If Not Decimal.TryParse(txtSafetyStock.Text, safetyStock) Then
            MessageBox.Show("安全库存必须是有效的数字", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtSafetyStock.Focus()
            Return False
        End If

        Dim minOrderQty As Decimal
        If Not Decimal.TryParse(txtMinOrderQty.Text, minOrderQty) Then
            MessageBox.Show("最小订购量必须是有效的数字", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtMinOrderQty.Focus()
            Return False
        End If

        Dim standardPrice As Decimal
        If Not Decimal.TryParse(txtStandardPrice.Text, standardPrice) Then
            MessageBox.Show("标准价格必须是有效的数字", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtStandardPrice.Focus()
            Return False
        End If

        Return True
    End Function
End Class
