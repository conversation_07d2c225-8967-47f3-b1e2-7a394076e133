Imports MySql.Data.MySqlClient

Public Class DataVerifier
    Private Shared ReadOnly connectionString As String = "Server=127.0.0.1;Port=3306;Database=ERPSystem;Uid=root;Pwd=******;CharSet=utf8mb4;"

    Public Shared Sub VerifyImportedData()
        Try
            Console.WriteLine("=== 验证导入的数据 ===")
            Console.WriteLine()
            
            Using connection As New MySqlConnection(connectionString)
                connection.Open()
                Console.WriteLine("数据库连接成功")
                Console.WriteLine()
                
                ' 验证客户数据
                VerifyCustomers(connection)
                
                ' 验证供应商数据
                VerifySuppliers(connection)
                
                ' 验证物料数据
                VerifyMaterials(connection)
                
                ' 验证库位数据
                VerifyLocations(connection)
                
                ' 验证库存数据
                VerifyInventory(connection)
                
                Console.WriteLine("数据验证完成！")
            End Using
            
        Catch ex As Exception
            Console.WriteLine($"验证数据时发生错误: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub VerifyCustomers(connection As MySqlConnection)
        Try
            Console.WriteLine("--- 客户数据验证 ---")
            
            Using cmd As New MySqlCommand("SELECT COUNT(*) FROM customers WHERE is_active = TRUE", connection)
                Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                Console.WriteLine($"活跃客户数量: {count}")
            End Using
            
            Using cmd As New MySqlCommand("SELECT customer_code, customer_name FROM customers LIMIT 3", connection)
                Using reader As MySqlDataReader = cmd.ExecuteReader()
                    Console.WriteLine("前3个客户:")
                    While reader.Read()
                        Console.WriteLine($"  {reader("customer_code")} - {reader("customer_name")}")
                    End While
                End Using
            End Using
            Console.WriteLine()
            
        Catch ex As Exception
            Console.WriteLine($"验证客户数据失败: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub VerifySuppliers(connection As MySqlConnection)
        Try
            Console.WriteLine("--- 供应商数据验证 ---")
            
            Using cmd As New MySqlCommand("SELECT COUNT(*) FROM suppliers WHERE is_active = TRUE", connection)
                Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                Console.WriteLine($"活跃供应商数量: {count}")
            End Using
            
            Using cmd As New MySqlCommand("SELECT supplier_code, supplier_name FROM suppliers LIMIT 3", connection)
                Using reader As MySqlDataReader = cmd.ExecuteReader()
                    Console.WriteLine("前3个供应商:")
                    While reader.Read()
                        Console.WriteLine($"  {reader("supplier_code")} - {reader("supplier_name")}")
                    End While
                End Using
            End Using
            Console.WriteLine()
            
        Catch ex As Exception
            Console.WriteLine($"验证供应商数据失败: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub VerifyMaterials(connection As MySqlConnection)
        Try
            Console.WriteLine("--- 物料数据验证 ---")
            
            Using cmd As New MySqlCommand("SELECT COUNT(*) FROM materials WHERE is_active = TRUE", connection)
                Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                Console.WriteLine($"活跃物料数量: {count}")
            End Using
            
            ' 按类别统计
            Using cmd As New MySqlCommand("SELECT category, COUNT(*) as count FROM materials WHERE is_active = TRUE GROUP BY category", connection)
                Using reader As MySqlDataReader = cmd.ExecuteReader()
                    Console.WriteLine("按类别统计:")
                    While reader.Read()
                        Console.WriteLine($"  {reader("category")}: {reader("count")}个")
                    End While
                End Using
            End Using
            
            ' T670系列物料
            Using cmd As New MySqlCommand("SELECT material_code, material_name FROM materials WHERE material_code LIKE 'TW2054%' LIMIT 5", connection)
                Using reader As MySqlDataReader = cmd.ExecuteReader()
                    Console.WriteLine("T670系列物料:")
                    While reader.Read()
                        Console.WriteLine($"  {reader("material_code")} - {reader("material_name")}")
                    End While
                End Using
            End Using
            Console.WriteLine()
            
        Catch ex As Exception
            Console.WriteLine($"验证物料数据失败: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub VerifyLocations(connection As MySqlConnection)
        Try
            Console.WriteLine("--- 库位数据验证 ---")
            
            Using cmd As New MySqlCommand("SELECT COUNT(*) FROM locations WHERE is_active = TRUE", connection)
                Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                Console.WriteLine($"活跃库位数量: {count}")
            End Using
            
            ' 按类型统计
            Using cmd As New MySqlCommand("SELECT location_type, COUNT(*) as count FROM locations WHERE is_active = TRUE GROUP BY location_type", connection)
                Using reader As MySqlDataReader = cmd.ExecuteReader()
                    Console.WriteLine("按类型统计:")
                    While reader.Read()
                        Console.WriteLine($"  {reader("location_type")}: {reader("count")}个")
                    End While
                End Using
            End Using
            Console.WriteLine()
            
        Catch ex As Exception
            Console.WriteLine($"验证库位数据失败: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub VerifyInventory(connection As MySqlConnection)
        Try
            Console.WriteLine("--- 库存数据验证 ---")
            
            Using cmd As New MySqlCommand("SELECT COUNT(*) FROM inventory", connection)
                Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                Console.WriteLine($"库存记录数量: {count}")
            End Using
            
            ' 总库存价值
            Using cmd As New MySqlCommand("SELECT SUM(i.current_stock * m.standard_price) as total_value FROM inventory i JOIN materials m ON i.material_id = m.id", connection)
                Dim totalValue As Object = cmd.ExecuteScalar()
                If totalValue IsNot Nothing AndAlso Not IsDBNull(totalValue) Then
                    Console.WriteLine($"总库存价值: ¥{Convert.ToDecimal(totalValue):F2}")
                End If
            End Using
            
            ' 库存详情示例
            Using cmd As New MySqlCommand("SELECT m.material_code, m.material_name, l.location_code, i.current_stock, i.available_stock FROM inventory i JOIN materials m ON i.material_id = m.id JOIN locations l ON i.location_id = l.id LIMIT 5", connection)
                Using reader As MySqlDataReader = cmd.ExecuteReader()
                    Console.WriteLine("库存详情示例:")
                    While reader.Read()
                        Console.WriteLine($"  {reader("material_code")} ({reader("material_name")}) @ {reader("location_code")}: 当前库存{reader("current_stock")}, 可用库存{reader("available_stock")}")
                    End While
                End Using
            End Using
            Console.WriteLine()
            
        Catch ex As Exception
            Console.WriteLine($"验证库存数据失败: {ex.Message}")
        End Try
    End Sub
End Class
