Imports System.Windows.Forms
Imports MySql.Data.MySqlClient

Public Class InOutReportForm
    Inherits Form

    ' 控件声明
    Private tabControl As TabControl
    Private tabInOutSummary As TabPage
    Private tabInboundDetail As TabPage
    Private tabOutboundDetail As TabPage
    Private tabMaterialFlow As TabPage
    Private tabLocationFlow As TabPage

    ' 汇总报表控件
    Private dtpSummaryStart As DateTimePicker
    Private dtpSummaryEnd As DateTimePicker
    Private btnSummaryQuery As Button
    Private dgvSummary As DataGridView
    Private lblSummaryStats As Label

    ' 入库明细控件
    Private dtpInboundStart As DateTimePicker
    Private dtpInboundEnd As DateTimePicker
    Private cmbInboundSupplier As ComboBox
    Private cmbInboundType As ComboBox
    Private cmbInboundStatus As ComboBox
    Private btnInboundQuery As Button
    Private dgvInboundDetail As DataGridView

    ' 出库明细控件
    Private dtpOutboundStart As DateTimePicker
    Private dtpOutboundEnd As DateTimePicker
    Private cmbOutboundCustomer As ComboBox
    Private cmbOutboundType As ComboBox
    Private cmbOutboundStatus As ComboBox
    Private btnOutboundQuery As Button
    Private dgvOutboundDetail As DataGridView

    ' 物料流水控件
    Private cmbMaterial As ComboBox
    Private dtpMaterialStart As DateTimePicker
    Private dtpMaterialEnd As DateTimePicker
    Private btnMaterialQuery As Button
    Private dgvMaterialFlow As DataGridView

    ' 库位流水控件
    Private cmbLocation As ComboBox
    Private dtpLocationStart As DateTimePicker
    Private dtpLocationEnd As DateTimePicker
    Private btnLocationQuery As Button
    Private dgvLocationFlow As DataGridView

    ' 工具栏
    Private toolStrip As ToolStrip
    Private btnRefresh As ToolStripButton
    Private btnExport As ToolStripButton
    Private btnPrint As ToolStripButton

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadComboBoxData()
        SetDefaultDates()
    End Sub

    Private Sub InitializeComponent()
        Me.SuspendLayout()
        Me.AutoScaleDimensions = New SizeF(7.0!, 17.0!)
        Me.AutoScaleMode = AutoScaleMode.Font
        Me.ClientSize = New Size(1400, 900)
        Me.Name = "InOutReportForm"
        Me.Text = "出入库报表"
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
        Me.ResumeLayout(False)
    End Sub

    Private Sub SetupUI()
        ' 工具栏
        SetupToolStrip()

        ' 主选项卡控件
        tabControl = New TabControl()
        tabControl.Dock = DockStyle.Fill
        tabControl.Font = New Font("Microsoft YaHei", 9)
        Me.Controls.Add(tabControl)

        ' 创建各个选项卡页面
        CreateSummaryTab()
        CreateInboundDetailTab()
        CreateOutboundDetailTab()
        CreateMaterialFlowTab()
        CreateLocationFlowTab()
    End Sub

    Private Sub SetupToolStrip()
        toolStrip = New ToolStrip()
        toolStrip.Font = New Font("Microsoft YaHei", 9)

        btnRefresh = New ToolStripButton("刷新数据")
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click

        btnExport = New ToolStripButton("导出报表")
        AddHandler btnExport.Click, AddressOf BtnExport_Click

        btnPrint = New ToolStripButton("打印报表")
        AddHandler btnPrint.Click, AddressOf BtnPrint_Click

        toolStrip.Items.AddRange({btnRefresh, New ToolStripSeparator(), btnExport, btnPrint})
        Me.Controls.Add(toolStrip)
    End Sub

    Private Sub CreateSummaryTab()
        tabInOutSummary = New TabPage("出入库汇总")
        tabControl.TabPages.Add(tabInOutSummary)

        ' 查询条件面板
        Dim pnlSummaryQuery As New Panel()
        pnlSummaryQuery.Dock = DockStyle.Top
        pnlSummaryQuery.Height = 60
        pnlSummaryQuery.BackColor = Color.FromArgb(248, 249, 250)
        tabInOutSummary.Controls.Add(pnlSummaryQuery)

        ' 时间范围
        Dim lblSummaryDateRange As New Label()
        lblSummaryDateRange.Text = "统计时间:"
        lblSummaryDateRange.Location = New Point(20, 20)
        lblSummaryDateRange.Size = New Size(80, 20)
        pnlSummaryQuery.Controls.Add(lblSummaryDateRange)

        dtpSummaryStart = New DateTimePicker()
        dtpSummaryStart.Location = New Point(100, 18)
        dtpSummaryStart.Size = New Size(120, 25)
        pnlSummaryQuery.Controls.Add(dtpSummaryStart)

        Dim lblSummaryTo As New Label()
        lblSummaryTo.Text = "至"
        lblSummaryTo.Location = New Point(230, 20)
        lblSummaryTo.Size = New Size(20, 20)
        pnlSummaryQuery.Controls.Add(lblSummaryTo)

        dtpSummaryEnd = New DateTimePicker()
        dtpSummaryEnd.Location = New Point(250, 18)
        dtpSummaryEnd.Size = New Size(120, 25)
        pnlSummaryQuery.Controls.Add(dtpSummaryEnd)

        btnSummaryQuery = New Button()
        btnSummaryQuery.Text = "查询"
        btnSummaryQuery.Location = New Point(390, 17)
        btnSummaryQuery.Size = New Size(80, 27)
        AddHandler btnSummaryQuery.Click, AddressOf BtnSummaryQuery_Click
        pnlSummaryQuery.Controls.Add(btnSummaryQuery)

        ' 统计信息标签
        lblSummaryStats = New Label()
        lblSummaryStats.Text = "统计信息将在查询后显示"
        lblSummaryStats.Location = New Point(500, 20)
        lblSummaryStats.Size = New Size(400, 20)
        lblSummaryStats.Font = New Font("Microsoft YaHei", 9, FontStyle.Bold)
        lblSummaryStats.ForeColor = Color.Blue
        pnlSummaryQuery.Controls.Add(lblSummaryStats)

        ' 汇总数据表格
        dgvSummary = New DataGridView()
        dgvSummary.Dock = DockStyle.Fill
        dgvSummary.ReadOnly = True
        dgvSummary.AllowUserToAddRows = False
        dgvSummary.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvSummary.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabInOutSummary.Controls.Add(dgvSummary)
    End Sub

    Private Sub CreateInboundDetailTab()
        tabInboundDetail = New TabPage("入库明细")
        tabControl.TabPages.Add(tabInboundDetail)

        ' 查询条件面板
        Dim pnlInboundQuery As New Panel()
        pnlInboundQuery.Dock = DockStyle.Top
        pnlInboundQuery.Height = 80
        pnlInboundQuery.BackColor = Color.FromArgb(248, 249, 250)
        tabInboundDetail.Controls.Add(pnlInboundQuery)

        ' 第一行：时间范围
        Dim lblInboundDate As New Label()
        lblInboundDate.Text = "入库时间:"
        lblInboundDate.Location = New Point(20, 15)
        lblInboundDate.Size = New Size(80, 20)
        pnlInboundQuery.Controls.Add(lblInboundDate)

        dtpInboundStart = New DateTimePicker()
        dtpInboundStart.Location = New Point(100, 13)
        dtpInboundStart.Size = New Size(120, 25)
        pnlInboundQuery.Controls.Add(dtpInboundStart)

        Dim lblInboundTo As New Label()
        lblInboundTo.Text = "至"
        lblInboundTo.Location = New Point(230, 15)
        lblInboundTo.Size = New Size(20, 20)
        pnlInboundQuery.Controls.Add(lblInboundTo)

        dtpInboundEnd = New DateTimePicker()
        dtpInboundEnd.Location = New Point(250, 13)
        dtpInboundEnd.Size = New Size(120, 25)
        pnlInboundQuery.Controls.Add(dtpInboundEnd)

        ' 第二行：筛选条件
        Dim lblInboundSupplier As New Label()
        lblInboundSupplier.Text = "供应商:"
        lblInboundSupplier.Location = New Point(20, 45)
        lblInboundSupplier.Size = New Size(60, 20)
        pnlInboundQuery.Controls.Add(lblInboundSupplier)

        cmbInboundSupplier = New ComboBox()
        cmbInboundSupplier.Location = New Point(80, 43)
        cmbInboundSupplier.Size = New Size(120, 25)
        cmbInboundSupplier.DropDownStyle = ComboBoxStyle.DropDownList
        pnlInboundQuery.Controls.Add(cmbInboundSupplier)

        Dim lblInboundType As New Label()
        lblInboundType.Text = "入库类型:"
        lblInboundType.Location = New Point(220, 45)
        lblInboundType.Size = New Size(70, 20)
        pnlInboundQuery.Controls.Add(lblInboundType)

        cmbInboundType = New ComboBox()
        cmbInboundType.Location = New Point(290, 43)
        cmbInboundType.Size = New Size(100, 25)
        cmbInboundType.DropDownStyle = ComboBoxStyle.DropDownList
        cmbInboundType.Items.AddRange({"全部", "purchase", "return", "transfer", "adjustment"})
        cmbInboundType.SelectedIndex = 0
        pnlInboundQuery.Controls.Add(cmbInboundType)

        Dim lblInboundStatus As New Label()
        lblInboundStatus.Text = "状态:"
        lblInboundStatus.Location = New Point(410, 45)
        lblInboundStatus.Size = New Size(50, 20)
        pnlInboundQuery.Controls.Add(lblInboundStatus)

        cmbInboundStatus = New ComboBox()
        cmbInboundStatus.Location = New Point(460, 43)
        cmbInboundStatus.Size = New Size(100, 25)
        cmbInboundStatus.DropDownStyle = ComboBoxStyle.DropDownList
        cmbInboundStatus.Items.AddRange({"全部", "pending", "confirmed", "completed", "cancelled"})
        cmbInboundStatus.SelectedIndex = 0
        pnlInboundQuery.Controls.Add(cmbInboundStatus)

        btnInboundQuery = New Button()
        btnInboundQuery.Text = "查询"
        btnInboundQuery.Location = New Point(580, 42)
        btnInboundQuery.Size = New Size(80, 27)
        AddHandler btnInboundQuery.Click, AddressOf BtnInboundQuery_Click
        pnlInboundQuery.Controls.Add(btnInboundQuery)

        ' 入库明细数据表格
        dgvInboundDetail = New DataGridView()
        dgvInboundDetail.Dock = DockStyle.Fill
        dgvInboundDetail.ReadOnly = True
        dgvInboundDetail.AllowUserToAddRows = False
        dgvInboundDetail.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvInboundDetail.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabInboundDetail.Controls.Add(dgvInboundDetail)
    End Sub

    Private Sub CreateOutboundDetailTab()
        tabOutboundDetail = New TabPage("出库明细")
        tabControl.TabPages.Add(tabOutboundDetail)

        ' 查询条件面板
        Dim pnlOutboundQuery As New Panel()
        pnlOutboundQuery.Dock = DockStyle.Top
        pnlOutboundQuery.Height = 80
        pnlOutboundQuery.BackColor = Color.FromArgb(248, 249, 250)
        tabOutboundDetail.Controls.Add(pnlOutboundQuery)

        ' 第一行：时间范围
        Dim lblOutboundDate As New Label()
        lblOutboundDate.Text = "出库时间:"
        lblOutboundDate.Location = New Point(20, 15)
        lblOutboundDate.Size = New Size(80, 20)
        pnlOutboundQuery.Controls.Add(lblOutboundDate)

        dtpOutboundStart = New DateTimePicker()
        dtpOutboundStart.Location = New Point(100, 13)
        dtpOutboundStart.Size = New Size(120, 25)
        pnlOutboundQuery.Controls.Add(dtpOutboundStart)

        Dim lblOutboundTo As New Label()
        lblOutboundTo.Text = "至"
        lblOutboundTo.Location = New Point(230, 15)
        lblOutboundTo.Size = New Size(20, 20)
        pnlOutboundQuery.Controls.Add(lblOutboundTo)

        dtpOutboundEnd = New DateTimePicker()
        dtpOutboundEnd.Location = New Point(250, 13)
        dtpOutboundEnd.Size = New Size(120, 25)
        pnlOutboundQuery.Controls.Add(dtpOutboundEnd)

        ' 第二行：筛选条件
        Dim lblOutboundCustomer As New Label()
        lblOutboundCustomer.Text = "客户:"
        lblOutboundCustomer.Location = New Point(20, 45)
        lblOutboundCustomer.Size = New Size(50, 20)
        pnlOutboundQuery.Controls.Add(lblOutboundCustomer)

        cmbOutboundCustomer = New ComboBox()
        cmbOutboundCustomer.Location = New Point(70, 43)
        cmbOutboundCustomer.Size = New Size(120, 25)
        cmbOutboundCustomer.DropDownStyle = ComboBoxStyle.DropDownList
        pnlOutboundQuery.Controls.Add(cmbOutboundCustomer)

        Dim lblOutboundType As New Label()
        lblOutboundType.Text = "出库类型:"
        lblOutboundType.Location = New Point(210, 45)
        lblOutboundType.Size = New Size(70, 20)
        pnlOutboundQuery.Controls.Add(lblOutboundType)

        cmbOutboundType = New ComboBox()
        cmbOutboundType.Location = New Point(280, 43)
        cmbOutboundType.Size = New Size(100, 25)
        cmbOutboundType.DropDownStyle = ComboBoxStyle.DropDownList
        cmbOutboundType.Items.AddRange({"全部", "sales", "return", "transfer", "adjustment"})
        cmbOutboundType.SelectedIndex = 0
        pnlOutboundQuery.Controls.Add(cmbOutboundType)

        Dim lblOutboundStatus As New Label()
        lblOutboundStatus.Text = "状态:"
        lblOutboundStatus.Location = New Point(400, 45)
        lblOutboundStatus.Size = New Size(50, 20)
        pnlOutboundQuery.Controls.Add(lblOutboundStatus)

        cmbOutboundStatus = New ComboBox()
        cmbOutboundStatus.Location = New Point(450, 43)
        cmbOutboundStatus.Size = New Size(100, 25)
        cmbOutboundStatus.DropDownStyle = ComboBoxStyle.DropDownList
        cmbOutboundStatus.Items.AddRange({"全部", "pending", "confirmed", "completed", "cancelled"})
        cmbOutboundStatus.SelectedIndex = 0
        pnlOutboundQuery.Controls.Add(cmbOutboundStatus)

        btnOutboundQuery = New Button()
        btnOutboundQuery.Text = "查询"
        btnOutboundQuery.Location = New Point(570, 42)
        btnOutboundQuery.Size = New Size(80, 27)
        AddHandler btnOutboundQuery.Click, AddressOf BtnOutboundQuery_Click
        pnlOutboundQuery.Controls.Add(btnOutboundQuery)

        ' 出库明细数据表格
        dgvOutboundDetail = New DataGridView()
        dgvOutboundDetail.Dock = DockStyle.Fill
        dgvOutboundDetail.ReadOnly = True
        dgvOutboundDetail.AllowUserToAddRows = False
        dgvOutboundDetail.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvOutboundDetail.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabOutboundDetail.Controls.Add(dgvOutboundDetail)
    End Sub

    Private Sub CreateMaterialFlowTab()
        tabMaterialFlow = New TabPage("物料流水")
        tabControl.TabPages.Add(tabMaterialFlow)

        ' 查询条件面板
        Dim pnlMaterialQuery As New Panel()
        pnlMaterialQuery.Dock = DockStyle.Top
        pnlMaterialQuery.Height = 60
        pnlMaterialQuery.BackColor = Color.FromArgb(248, 249, 250)
        tabMaterialFlow.Controls.Add(pnlMaterialQuery)

        ' 物料选择
        Dim lblMaterial As New Label()
        lblMaterial.Text = "物料:"
        lblMaterial.Location = New Point(20, 20)
        lblMaterial.Size = New Size(50, 20)
        pnlMaterialQuery.Controls.Add(lblMaterial)

        cmbMaterial = New ComboBox()
        cmbMaterial.Location = New Point(70, 18)
        cmbMaterial.Size = New Size(200, 25)
        cmbMaterial.DropDownStyle = ComboBoxStyle.DropDownList
        pnlMaterialQuery.Controls.Add(cmbMaterial)

        ' 时间范围
        Dim lblMaterialDate As New Label()
        lblMaterialDate.Text = "时间:"
        lblMaterialDate.Location = New Point(290, 20)
        lblMaterialDate.Size = New Size(50, 20)
        pnlMaterialQuery.Controls.Add(lblMaterialDate)

        dtpMaterialStart = New DateTimePicker()
        dtpMaterialStart.Location = New Point(340, 18)
        dtpMaterialStart.Size = New Size(120, 25)
        pnlMaterialQuery.Controls.Add(dtpMaterialStart)

        Dim lblMaterialTo As New Label()
        lblMaterialTo.Text = "至"
        lblMaterialTo.Location = New Point(470, 20)
        lblMaterialTo.Size = New Size(20, 20)
        pnlMaterialQuery.Controls.Add(lblMaterialTo)

        dtpMaterialEnd = New DateTimePicker()
        dtpMaterialEnd.Location = New Point(490, 18)
        dtpMaterialEnd.Size = New Size(120, 25)
        pnlMaterialQuery.Controls.Add(dtpMaterialEnd)

        btnMaterialQuery = New Button()
        btnMaterialQuery.Text = "查询"
        btnMaterialQuery.Location = New Point(630, 17)
        btnMaterialQuery.Size = New Size(80, 27)
        AddHandler btnMaterialQuery.Click, AddressOf BtnMaterialQuery_Click
        pnlMaterialQuery.Controls.Add(btnMaterialQuery)

        ' 物料流水数据表格
        dgvMaterialFlow = New DataGridView()
        dgvMaterialFlow.Dock = DockStyle.Fill
        dgvMaterialFlow.ReadOnly = True
        dgvMaterialFlow.AllowUserToAddRows = False
        dgvMaterialFlow.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvMaterialFlow.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabMaterialFlow.Controls.Add(dgvMaterialFlow)
    End Sub

    Private Sub CreateLocationFlowTab()
        tabLocationFlow = New TabPage("库位流水")
        tabControl.TabPages.Add(tabLocationFlow)

        ' 查询条件面板
        Dim pnlLocationQuery As New Panel()
        pnlLocationQuery.Dock = DockStyle.Top
        pnlLocationQuery.Height = 60
        pnlLocationQuery.BackColor = Color.FromArgb(248, 249, 250)
        tabLocationFlow.Controls.Add(pnlLocationQuery)

        ' 库位选择
        Dim lblLocation As New Label()
        lblLocation.Text = "库位:"
        lblLocation.Location = New Point(20, 20)
        lblLocation.Size = New Size(50, 20)
        pnlLocationQuery.Controls.Add(lblLocation)

        cmbLocation = New ComboBox()
        cmbLocation.Location = New Point(70, 18)
        cmbLocation.Size = New Size(200, 25)
        cmbLocation.DropDownStyle = ComboBoxStyle.DropDownList
        pnlLocationQuery.Controls.Add(cmbLocation)

        ' 时间范围
        Dim lblLocationDate As New Label()
        lblLocationDate.Text = "时间:"
        lblLocationDate.Location = New Point(290, 20)
        lblLocationDate.Size = New Size(50, 20)
        pnlLocationQuery.Controls.Add(lblLocationDate)

        dtpLocationStart = New DateTimePicker()
        dtpLocationStart.Location = New Point(340, 18)
        dtpLocationStart.Size = New Size(120, 25)
        pnlLocationQuery.Controls.Add(dtpLocationStart)

        Dim lblLocationTo As New Label()
        lblLocationTo.Text = "至"
        lblLocationTo.Location = New Point(470, 20)
        lblLocationTo.Size = New Size(20, 20)
        pnlLocationQuery.Controls.Add(lblLocationTo)

        dtpLocationEnd = New DateTimePicker()
        dtpLocationEnd.Location = New Point(490, 18)
        dtpLocationEnd.Size = New Size(120, 25)
        pnlLocationQuery.Controls.Add(dtpLocationEnd)

        btnLocationQuery = New Button()
        btnLocationQuery.Text = "查询"
        btnLocationQuery.Location = New Point(630, 17)
        btnLocationQuery.Size = New Size(80, 27)
        AddHandler btnLocationQuery.Click, AddressOf BtnLocationQuery_Click
        pnlLocationQuery.Controls.Add(btnLocationQuery)

        ' 库位流水数据表格
        dgvLocationFlow = New DataGridView()
        dgvLocationFlow.Dock = DockStyle.Fill
        dgvLocationFlow.ReadOnly = True
        dgvLocationFlow.AllowUserToAddRows = False
        dgvLocationFlow.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvLocationFlow.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabLocationFlow.Controls.Add(dgvLocationFlow)
    End Sub

    Private Sub LoadComboBoxData()
        Try
            ' 加载供应商数据
            Dim supplierQuery As String = "SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name"
            Dim supplierDt As DataTable = DatabaseManager.Instance.ExecuteQuery(supplierQuery)

            Dim supplierRow As DataRow = supplierDt.NewRow()
            supplierRow("id") = DBNull.Value
            supplierRow("supplier_name") = "全部"
            supplierDt.Rows.InsertAt(supplierRow, 0)

            cmbInboundSupplier.DisplayMember = "supplier_name"
            cmbInboundSupplier.ValueMember = "id"
            cmbInboundSupplier.DataSource = supplierDt
            cmbInboundSupplier.SelectedIndex = 0

            ' 加载客户数据
            Dim customerQuery As String = "SELECT id, customer_name FROM customers WHERE is_active = 1 ORDER BY customer_name"
            Dim customerDt As DataTable = DatabaseManager.Instance.ExecuteQuery(customerQuery)

            Dim customerRow As DataRow = customerDt.NewRow()
            customerRow("id") = DBNull.Value
            customerRow("customer_name") = "全部"
            customerDt.Rows.InsertAt(customerRow, 0)

            cmbOutboundCustomer.DisplayMember = "customer_name"
            cmbOutboundCustomer.ValueMember = "id"
            cmbOutboundCustomer.DataSource = customerDt
            cmbOutboundCustomer.SelectedIndex = 0

            ' 加载物料数据
            Dim materialQuery As String = "SELECT id, CONCAT(material_code, ' - ', material_name) as display_name FROM materials WHERE is_active = 1 ORDER BY material_code"
            Dim materialDt As DataTable = DatabaseManager.Instance.ExecuteQuery(materialQuery)

            Dim materialRow As DataRow = materialDt.NewRow()
            materialRow("id") = DBNull.Value
            materialRow("display_name") = "全部"
            materialDt.Rows.InsertAt(materialRow, 0)

            cmbMaterial.DisplayMember = "display_name"
            cmbMaterial.ValueMember = "id"
            cmbMaterial.DataSource = materialDt
            cmbMaterial.SelectedIndex = 0

            ' 加载库位数据
            Dim locationQuery As String = "SELECT id, CONCAT(location_code, ' - ', location_name) as display_name FROM locations WHERE is_active = 1 ORDER BY location_code"
            Dim locationDt As DataTable = DatabaseManager.Instance.ExecuteQuery(locationQuery)

            Dim locationRow As DataRow = locationDt.NewRow()
            locationRow("id") = DBNull.Value
            locationRow("display_name") = "全部"
            locationDt.Rows.InsertAt(locationRow, 0)

            cmbLocation.DisplayMember = "display_name"
            cmbLocation.ValueMember = "id"
            cmbLocation.DataSource = locationDt
            cmbLocation.SelectedIndex = 0

        Catch ex As Exception
            MessageBox.Show($"加载下拉框数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetDefaultDates()
        ' 设置默认时间范围为当前月份
        Dim now As DateTime = DateTime.Now
        Dim startOfMonth As DateTime = New DateTime(now.Year, now.Month, 1)
        Dim endOfMonth As DateTime = startOfMonth.AddMonths(1).AddDays(-1)

        dtpSummaryStart.Value = startOfMonth
        dtpSummaryEnd.Value = endOfMonth
        dtpInboundStart.Value = startOfMonth
        dtpInboundEnd.Value = endOfMonth
        dtpOutboundStart.Value = startOfMonth
        dtpOutboundEnd.Value = endOfMonth
        dtpMaterialStart.Value = startOfMonth
        dtpMaterialEnd.Value = endOfMonth
        dtpLocationStart.Value = startOfMonth
        dtpLocationEnd.Value = endOfMonth
    End Sub

    ' 查询方法
    Private Sub BtnSummaryQuery_Click(sender As Object, e As EventArgs)
        Try
            Dim startDate As DateTime = dtpSummaryStart.Value.Date
            Dim endDate As DateTime = dtpSummaryEnd.Value.Date

            ' 出入库汇总查询
            Dim summaryQuery As String = "
                SELECT
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    m.unit AS '单位',
                    COALESCE(inbound.total_in, 0) AS '入库数量',
                    COALESCE(inbound.total_in_amount, 0) AS '入库金额',
                    COALESCE(outbound.total_out, 0) AS '出库数量',
                    COALESCE(outbound.total_out_amount, 0) AS '出库金额',
                    (COALESCE(inbound.total_in, 0) - COALESCE(outbound.total_out, 0)) AS '净变动量',
                    (COALESCE(inbound.total_in_amount, 0) - COALESCE(outbound.total_out_amount, 0)) AS '净变动金额'
                FROM materials m
                LEFT JOIN (
                    SELECT
                        id.material_id,
                        SUM(id.actual_quantity) as total_in,
                        SUM(id.line_total) as total_in_amount
                    FROM inbound_details id
                    INNER JOIN inbound_orders io ON id.inbound_id = io.id
                    WHERE io.inbound_date BETWEEN @start_date AND @end_date
                    AND io.status = 'completed'
                    GROUP BY id.material_id
                ) inbound ON m.id = inbound.material_id
                LEFT JOIN (
                    SELECT
                        od.material_id,
                        SUM(od.actual_quantity) as total_out,
                        SUM(od.line_total) as total_out_amount
                    FROM outbound_details od
                    INNER JOIN outbound_orders oo ON od.outbound_id = oo.id
                    WHERE oo.outbound_date BETWEEN @start_date AND @end_date
                    AND oo.status = 'completed'
                    GROUP BY od.material_id
                ) outbound ON m.id = outbound.material_id
                WHERE m.is_active = 1
                AND (inbound.material_id IS NOT NULL OR outbound.material_id IS NOT NULL)
                ORDER BY m.material_code"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"@start_date", startDate},
                {"@end_date", endDate}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(summaryQuery, parameters)
            dgvSummary.DataSource = dt

            ' 计算统计信息
            Dim totalInQuantity As Decimal = 0
            Dim totalInAmount As Decimal = 0
            Dim totalOutQuantity As Decimal = 0
            Dim totalOutAmount As Decimal = 0

            For Each row As DataRow In dt.Rows
                totalInQuantity += Convert.ToDecimal(row("入库数量"))
                totalInAmount += Convert.ToDecimal(row("入库金额"))
                totalOutQuantity += Convert.ToDecimal(row("出库数量"))
                totalOutAmount += Convert.ToDecimal(row("出库金额"))
            Next

            lblSummaryStats.Text = $"统计期间：{startDate:yyyy-MM-dd} 至 {endDate:yyyy-MM-dd} | " &
                                  $"入库总量：{totalInQuantity:N2} | 入库总额：¥{totalInAmount:N2} | " &
                                  $"出库总量：{totalOutQuantity:N2} | 出库总额：¥{totalOutAmount:N2}"

            ' 设置数字列格式
            SetNumericColumnFormat(dgvSummary)

        Catch ex As Exception
            MessageBox.Show($"查询汇总数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnInboundQuery_Click(sender As Object, e As EventArgs)
        Try
            Dim startDate As DateTime = dtpInboundStart.Value.Date
            Dim endDate As DateTime = dtpInboundEnd.Value.Date

            Dim inboundQuery As String = "
                SELECT
                    io.inbound_number AS '入库单号',
                    io.inbound_date AS '入库日期',
                    s.supplier_name AS '供应商',
                    io.inbound_type AS '入库类型',
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    l.location_code AS '库位编码',
                    id.planned_quantity AS '计划数量',
                    id.actual_quantity AS '实际数量',
                    m.unit AS '单位',
                    id.unit_price AS '单价',
                    id.line_total AS '行总额',
                    id.batch_number AS '批次号',
                    io.status AS '状态',
                    io.remarks AS '备注'
                FROM inbound_details id
                INNER JOIN inbound_orders io ON id.inbound_id = io.id
                INNER JOIN materials m ON id.material_id = m.id
                INNER JOIN locations l ON id.location_id = l.id
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                WHERE io.inbound_date BETWEEN @start_date AND @end_date"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"@start_date", startDate},
                {"@end_date", endDate}
            }

            ' 添加供应商筛选条件
            If cmbInboundSupplier.SelectedValue IsNot Nothing AndAlso Not IsDBNull(cmbInboundSupplier.SelectedValue) Then
                inboundQuery += " AND io.supplier_id = @supplier_id"
                parameters.Add("@supplier_id", cmbInboundSupplier.SelectedValue)
            End If

            ' 添加入库类型筛选条件
            If cmbInboundType.SelectedIndex > 0 Then
                inboundQuery += " AND io.inbound_type = @inbound_type"
                parameters.Add("@inbound_type", cmbInboundType.Text)
            End If

            ' 添加状态筛选条件
            If cmbInboundStatus.SelectedIndex > 0 Then
                inboundQuery += " AND io.status = @status"
                parameters.Add("@status", cmbInboundStatus.Text)
            End If

            inboundQuery += " ORDER BY io.inbound_date DESC, io.inbound_number DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(inboundQuery, parameters)
            dgvInboundDetail.DataSource = dt

            ' 设置数字列格式
            SetNumericColumnFormat(dgvInboundDetail)

        Catch ex As Exception
            MessageBox.Show($"查询入库明细失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnOutboundQuery_Click(sender As Object, e As EventArgs)
        Try
            Dim startDate As DateTime = dtpOutboundStart.Value.Date
            Dim endDate As DateTime = dtpOutboundEnd.Value.Date

            Dim outboundQuery As String = "
                SELECT
                    oo.outbound_number AS '出库单号',
                    oo.outbound_date AS '出库日期',
                    c.customer_name AS '客户',
                    oo.outbound_type AS '出库类型',
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    l.location_code AS '库位编码',
                    od.planned_quantity AS '计划数量',
                    od.actual_quantity AS '实际数量',
                    m.unit AS '单位',
                    od.unit_price AS '单价',
                    od.line_total AS '行总额',
                    od.batch_number AS '批次号',
                    oo.status AS '状态',
                    oo.remarks AS '备注'
                FROM outbound_details od
                INNER JOIN outbound_orders oo ON od.outbound_id = oo.id
                INNER JOIN materials m ON od.material_id = m.id
                INNER JOIN locations l ON od.location_id = l.id
                LEFT JOIN customers c ON oo.customer_id = c.id
                WHERE oo.outbound_date BETWEEN @start_date AND @end_date"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"@start_date", startDate},
                {"@end_date", endDate}
            }

            ' 添加客户筛选条件
            If cmbOutboundCustomer.SelectedValue IsNot Nothing AndAlso Not IsDBNull(cmbOutboundCustomer.SelectedValue) Then
                outboundQuery += " AND oo.customer_id = @customer_id"
                parameters.Add("@customer_id", cmbOutboundCustomer.SelectedValue)
            End If

            ' 添加出库类型筛选条件
            If cmbOutboundType.SelectedIndex > 0 Then
                outboundQuery += " AND oo.outbound_type = @outbound_type"
                parameters.Add("@outbound_type", cmbOutboundType.Text)
            End If

            ' 添加状态筛选条件
            If cmbOutboundStatus.SelectedIndex > 0 Then
                outboundQuery += " AND oo.status = @status"
                parameters.Add("@status", cmbOutboundStatus.Text)
            End If

            outboundQuery += " ORDER BY oo.outbound_date DESC, oo.outbound_number DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(outboundQuery, parameters)
            dgvOutboundDetail.DataSource = dt

            ' 设置数字列格式
            SetNumericColumnFormat(dgvOutboundDetail)

        Catch ex As Exception
            MessageBox.Show($"查询出库明细失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnMaterialQuery_Click(sender As Object, e As EventArgs)
        Try
            If cmbMaterial.SelectedValue Is Nothing OrElse IsDBNull(cmbMaterial.SelectedValue) Then
                MessageBox.Show("请选择要查询的物料", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            Dim materialId As Integer = Convert.ToInt32(cmbMaterial.SelectedValue)
            Dim startDate As DateTime = dtpMaterialStart.Value.Date
            Dim endDate As DateTime = dtpMaterialEnd.Value.Date

            Dim materialFlowQuery As String = "
                SELECT
                    '入库' AS '业务类型',
                    io.inbound_number AS '单据号',
                    io.inbound_date AS '业务日期',
                    s.supplier_name AS '业务对象',
                    l.location_code AS '库位编码',
                    id.actual_quantity AS '数量',
                    '+' AS '方向',
                    id.unit_price AS '单价',
                    id.line_total AS '金额',
                    id.batch_number AS '批次号',
                    io.remarks AS '备注'
                FROM inbound_details id
                INNER JOIN inbound_orders io ON id.inbound_id = io.id
                INNER JOIN locations l ON id.location_id = l.id
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                WHERE id.material_id = @material_id
                AND io.inbound_date BETWEEN @start_date AND @end_date
                AND io.status = 'completed'

                UNION ALL

                SELECT
                    '出库' AS '业务类型',
                    oo.outbound_number AS '单据号',
                    oo.outbound_date AS '业务日期',
                    c.customer_name AS '业务对象',
                    l.location_code AS '库位编码',
                    od.actual_quantity AS '数量',
                    '-' AS '方向',
                    od.unit_price AS '单价',
                    od.line_total AS '金额',
                    od.batch_number AS '批次号',
                    oo.remarks AS '备注'
                FROM outbound_details od
                INNER JOIN outbound_orders oo ON od.outbound_id = oo.id
                INNER JOIN locations l ON od.location_id = l.id
                LEFT JOIN customers c ON oo.customer_id = c.id
                WHERE od.material_id = @material_id
                AND oo.outbound_date BETWEEN @start_date AND @end_date
                AND oo.status = 'completed'

                ORDER BY 业务日期 DESC, 单据号 DESC"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"@material_id", materialId},
                {"@start_date", startDate},
                {"@end_date", endDate}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(materialFlowQuery, parameters)
            dgvMaterialFlow.DataSource = dt

            ' 设置数字列格式
            SetNumericColumnFormat(dgvMaterialFlow)

        Catch ex As Exception
            MessageBox.Show($"查询物料流水失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnLocationQuery_Click(sender As Object, e As EventArgs)
        Try
            If cmbLocation.SelectedValue Is Nothing OrElse IsDBNull(cmbLocation.SelectedValue) Then
                MessageBox.Show("请选择要查询的库位", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            Dim locationId As Integer = Convert.ToInt32(cmbLocation.SelectedValue)
            Dim startDate As DateTime = dtpLocationStart.Value.Date
            Dim endDate As DateTime = dtpLocationEnd.Value.Date

            Dim locationFlowQuery As String = "
                SELECT
                    '入库' AS '业务类型',
                    io.inbound_number AS '单据号',
                    io.inbound_date AS '业务日期',
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    s.supplier_name AS '业务对象',
                    id.actual_quantity AS '数量',
                    '+' AS '方向',
                    m.unit AS '单位',
                    id.unit_price AS '单价',
                    id.line_total AS '金额',
                    id.batch_number AS '批次号'
                FROM inbound_details id
                INNER JOIN inbound_orders io ON id.inbound_id = io.id
                INNER JOIN materials m ON id.material_id = m.id
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                WHERE id.location_id = @location_id
                AND io.inbound_date BETWEEN @start_date AND @end_date
                AND io.status = 'completed'

                UNION ALL

                SELECT
                    '出库' AS '业务类型',
                    oo.outbound_number AS '单据号',
                    oo.outbound_date AS '业务日期',
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    c.customer_name AS '业务对象',
                    od.actual_quantity AS '数量',
                    '-' AS '方向',
                    m.unit AS '单位',
                    od.unit_price AS '单价',
                    od.line_total AS '金额',
                    od.batch_number AS '批次号'
                FROM outbound_details od
                INNER JOIN outbound_orders oo ON od.outbound_id = oo.id
                INNER JOIN materials m ON od.material_id = m.id
                LEFT JOIN customers c ON oo.customer_id = c.id
                WHERE od.location_id = @location_id
                AND oo.outbound_date BETWEEN @start_date AND @end_date
                AND oo.status = 'completed'

                ORDER BY 业务日期 DESC, 单据号 DESC"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"@location_id", locationId},
                {"@start_date", startDate},
                {"@end_date", endDate}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(locationFlowQuery, parameters)
            dgvLocationFlow.DataSource = dt

            ' 设置数字列格式
            SetNumericColumnFormat(dgvLocationFlow)

        Catch ex As Exception
            MessageBox.Show($"查询库位流水失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 辅助方法
    Private Sub SetNumericColumnFormat(dgv As DataGridView)
        For Each column As DataGridViewColumn In dgv.Columns
            If column.HeaderText.Contains("数量") OrElse column.HeaderText.Contains("金额") OrElse
               column.HeaderText.Contains("单价") OrElse column.HeaderText.Contains("总额") Then
                column.DefaultCellStyle.Format = "N2"
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
            End If
        Next
    End Sub

    ' 事件处理程序
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadComboBoxData()
        MessageBox.Show("数据已刷新", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BtnExport_Click(sender As Object, e As EventArgs)
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "文本文件|*.txt|CSV文件|*.csv"
            saveDialog.Title = "导出出入库报表"
            saveDialog.FileName = $"出入库报表_{DateTime.Now:yyyyMMdd_HHmmss}"

            If saveDialog.ShowDialog() = DialogResult.OK Then
                ' 根据当前选中的选项卡导出相应数据
                Select Case tabControl.SelectedIndex
                    Case 0 ' 汇总
                        ExportDataGridView(dgvSummary, saveDialog.FileName, "出入库汇总")
                    Case 1 ' 入库明细
                        ExportDataGridView(dgvInboundDetail, saveDialog.FileName, "入库明细")
                    Case 2 ' 出库明细
                        ExportDataGridView(dgvOutboundDetail, saveDialog.FileName, "出库明细")
                    Case 3 ' 物料流水
                        ExportDataGridView(dgvMaterialFlow, saveDialog.FileName, "物料流水")
                    Case 4 ' 库位流水
                        ExportDataGridView(dgvLocationFlow, saveDialog.FileName, "库位流水")
                End Select

                MessageBox.Show("导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnPrint_Click(sender As Object, e As EventArgs)
        MessageBox.Show("打印功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub ExportDataGridView(dgv As DataGridView, fileName As String, sheetName As String)
        Dim sb As New System.Text.StringBuilder()
        sb.AppendLine($"{sheetName}报表")
        sb.AppendLine($"生成时间: {DateTime.Now}")
        sb.AppendLine()

        ' 添加表头
        For Each column As DataGridViewColumn In dgv.Columns
            If column.Visible Then
                sb.Append(column.HeaderText + vbTab)
            End If
        Next
        sb.AppendLine()

        ' 添加数据行
        For Each row As DataGridViewRow In dgv.Rows
            If Not row.IsNewRow Then
                For Each cell As DataGridViewCell In row.Cells
                    If dgv.Columns(cell.ColumnIndex).Visible Then
                        sb.Append(cell.Value?.ToString() + vbTab)
                    End If
                Next
                sb.AppendLine()
            End If
        Next

        System.IO.File.WriteAllText(fileName, sb.ToString(), System.Text.Encoding.UTF8)
    End Sub
End Class
