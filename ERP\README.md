# ERP库存管理系统

## 项目概述
这是一个基于VB.NET Windows Forms的ERP库存管理系统，使用MySQL数据库，支持完整的库存管理功能。

## 技术栈
- **开发框架**: .NET 6.0-windows
- **编程语言**: VB.NET
- **UI框架**: Windows Forms
- **数据库**: MySQL 8.0
- **数据库连接**: MySql.Data v8.2.0

## 数据库配置
- **服务器**: 127.0.0.1:3306
- **数据库名**: ERPSystem
- **用户名**: root
- **密码**: 521223
- **字符集**: UTF8MB4

## 已实现功能

### 1. 基础数据管理
- ✅ **客户管理** (CustomerForm.vb)
  - 客户信息的增删改查
  - 包含客户编码、名称、联系人、电话、邮箱、地址、税号、信用额度、付款条件等字段
  
- ✅ **物料管理** (MaterialForm.vb)
  - 物料信息的增删改查
  - 包含物料编码、名称、图号、版本、单位、类别、规格、安全库存、最小订购量、标准价格等字段
  
- ✅ **供应商管理** (SupplierForm.vb)
  - 供应商信息的增删改查
  - 包含供应商编码、名称、联系人、电话、邮箱、地址、税号、付款条件等字段
  
- ✅ **库位管理** (LocationForm.vb)
  - 库位信息的增删改查
  - 支持多种库位类型：storage(存储)、wip(在制品)、qc(质检)、shipping(发货)、receiving(收货)

### 2. 库存管理
- ✅ **库存查询** (InventoryForm.vb)
  - 实时库存查询
  - 支持按物料编码、物料名称、库位筛选
  - 显示当前库存、预留库存、可用库存
  - 安全库存预警（低于安全库存的记录会高亮显示）
  
- ✅ **入库管理** (InboundForm.vb)
  - 入库单的增删改查
  - 支持多种入库类型：purchase(采购)、return(退货)、transfer(调拨)、adjustment(调整)
  - 自动生成入库单号
  
- ✅ **出库管理** (OutboundForm.vb)
  - 出库单的增删改查
  - 支持多种出库类型：sales(销售)、return(退货)、transfer(调拨)、adjustment(调整)
  - 自动生成出库单号

### 3. 系统功能
- ✅ **用户登录** (LoginForm.vb)
  - 用户身份验证
  - 默认管理员账户：admin/admin123
  
- ✅ **主界面** (MainForm.vb)
  - MDI多文档界面
  - 完整的菜单系统
  - 工具栏快捷操作
  - 状态栏显示实时时间

## 数据库结构

### 核心表结构
1. **users** - 用户表
2. **customers** - 客户表
3. **suppliers** - 供应商表
4. **materials** - 物料表
5. **locations** - 库位表
6. **inventory** - 库存表
7. **inbound_orders** - 入库单表
8. **inbound_details** - 入库明细表
9. **outbound_orders** - 出库单表
10. **outbound_details** - 出库明细表
11. **orders** - 订单表
12. **order_details** - 订单明细表

### 测试数据
系统包含预置测试数据：
- 客户：卓郎
- 物料：
  - 线槽T670 (TW205473B) - 171.26元
  - 隔纱板T670 (TW205481B) - 147.24元
  - 覆板T670 (TW205489A) - 75.57元
- 库位：T01

## 运行说明

### 环境要求
1. Windows 10/11
2. .NET 6.0 Runtime
3. MySQL 8.0服务器
4. Visual Studio 2022 (开发环境)

### 启动步骤
1. 确保MySQL服务器运行在127.0.0.1:3306
2. 确保数据库用户root密码为521223
3. 运行命令：`dotnet run`
4. 使用默认账户登录：admin/admin123

### 编译说明
```bash
cd ERP
dotnet build
dotnet run
```

## 开发状态
- ✅ 项目架构完成
- ✅ 数据库设计完成
- ✅ 基础数据管理模块完成
- ✅ 库存管理核心功能完成
- ✅ 用户界面完成
- ✅ 编译测试通过
- ✅ 应用程序可正常启动

## 待开发功能
- 📋 入库/出库明细管理
- 📋 库存盘点功能
- 📋 安全库存预警系统
- 📋 报表功能
- 📋 订单管理
- 📋 用户权限管理
- 📋 数据导入导出

## 项目特点
1. **完整的CRUD操作** - 所有管理窗体都实现了完整的增删改查功能
2. **数据验证** - 表单输入验证和错误处理
3. **中文界面** - 完全中文化的用户界面
4. **模块化设计** - 清晰的代码结构和模块分离
5. **数据库事务** - 安全的数据库操作
6. **错误处理** - 完善的异常处理机制

## 联系信息
这是一个完整的ERP库存管理系统基础框架，可以根据具体业务需求进行扩展和定制。
