-- 财务管理相关数据库表创建脚本
-- 执行前请确保已连接到ERPSystem数据库

USE ERPSystem;

-- 创建应收账款表
CREATE TABLE IF NOT EXISTS accounts_receivable (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ar_number VARCHAR(50) NOT NULL UNIQUE COMMENT '应收单号',
    customer_id INT NOT NULL COMMENT '客户ID',
    order_id INT COMMENT '关联订单ID',
    invoice_number VARCHAR(50) COMMENT '发票号',
    invoice_date DATE NOT NULL COMMENT '发票日期',
    due_date DATE NOT NULL COMMENT '到期日期',
    original_amount DECIMAL(15,2) NOT NULL COMMENT '原始金额',
    paid_amount DECIMAL(15,2) DEFAULT 0 COMMENT '已付金额',
    outstanding_amount DECIMAL(15,2) NOT NULL COMMENT '未付金额',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '币种',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000 COMMENT '汇率',
    status ENUM('pending', 'partial', 'paid', 'overdue', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    payment_terms VARCHAR(100) COMMENT '付款条件',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
    INDEX idx_ar_number (ar_number),
    INDEX idx_customer_id (customer_id),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应收账款表';

-- 创建应付账款表
CREATE TABLE IF NOT EXISTS accounts_payable (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ap_number VARCHAR(50) NOT NULL UNIQUE COMMENT '应付单号',
    supplier_id INT NOT NULL COMMENT '供应商ID',
    purchase_order_number VARCHAR(50) COMMENT '采购订单号',
    invoice_number VARCHAR(50) COMMENT '发票号',
    invoice_date DATE NOT NULL COMMENT '发票日期',
    due_date DATE NOT NULL COMMENT '到期日期',
    original_amount DECIMAL(15,2) NOT NULL COMMENT '原始金额',
    paid_amount DECIMAL(15,2) DEFAULT 0 COMMENT '已付金额',
    outstanding_amount DECIMAL(15,2) NOT NULL COMMENT '未付金额',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '币种',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000 COMMENT '汇率',
    status ENUM('pending', 'partial', 'paid', 'overdue', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    payment_terms VARCHAR(100) COMMENT '付款条件',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    INDEX idx_ap_number (ap_number),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应付账款表';

-- 创建付款记录表
CREATE TABLE IF NOT EXISTS payment_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    payment_number VARCHAR(50) NOT NULL UNIQUE COMMENT '付款单号',
    payment_type ENUM('receivable', 'payable') NOT NULL COMMENT '付款类型',
    reference_id INT NOT NULL COMMENT '关联ID(应收或应付ID)',
    payment_date DATE NOT NULL COMMENT '付款日期',
    payment_amount DECIMAL(15,2) NOT NULL COMMENT '付款金额',
    payment_method VARCHAR(50) NOT NULL COMMENT '付款方式',
    bank_account VARCHAR(100) COMMENT '银行账户',
    reference_number VARCHAR(100) COMMENT '参考号',
    description TEXT COMMENT '描述',
    created_by VARCHAR(100) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_payment_number (payment_number),
    INDEX idx_payment_type (payment_type),
    INDEX idx_reference_id (reference_id),
    INDEX idx_payment_date (payment_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='付款记录表';

-- 创建财务科目表
CREATE TABLE IF NOT EXISTS financial_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    account_code VARCHAR(20) NOT NULL UNIQUE COMMENT '科目编码',
    account_name VARCHAR(100) NOT NULL COMMENT '科目名称',
    account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL COMMENT '科目类型',
    parent_id INT COMMENT '父科目ID',
    level INT DEFAULT 1 COMMENT '科目级别',
    is_leaf BOOLEAN DEFAULT TRUE COMMENT '是否叶子节点',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (parent_id) REFERENCES financial_accounts(id) ON DELETE SET NULL,
    INDEX idx_account_code (account_code),
    INDEX idx_account_type (account_type),
    INDEX idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财务科目表';

-- 创建财务凭证表
CREATE TABLE IF NOT EXISTS financial_vouchers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    voucher_number VARCHAR(50) NOT NULL UNIQUE COMMENT '凭证号',
    voucher_date DATE NOT NULL COMMENT '凭证日期',
    voucher_type VARCHAR(50) COMMENT '凭证类型',
    total_debit DECIMAL(15,2) NOT NULL COMMENT '借方总额',
    total_credit DECIMAL(15,2) NOT NULL COMMENT '贷方总额',
    description TEXT COMMENT '摘要',
    reference_type VARCHAR(50) COMMENT '关联类型',
    reference_id INT COMMENT '关联ID',
    status ENUM('draft', 'posted', 'cancelled') DEFAULT 'draft' COMMENT '状态',
    created_by VARCHAR(100) COMMENT '制单人',
    posted_by VARCHAR(100) COMMENT '过账人',
    posted_at TIMESTAMP NULL COMMENT '过账时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_voucher_number (voucher_number),
    INDEX idx_voucher_date (voucher_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财务凭证表';

-- 创建财务凭证明细表
CREATE TABLE IF NOT EXISTS financial_voucher_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    voucher_id INT NOT NULL COMMENT '凭证ID',
    line_number INT NOT NULL COMMENT '行号',
    account_id INT NOT NULL COMMENT '科目ID',
    debit_amount DECIMAL(15,2) DEFAULT 0 COMMENT '借方金额',
    credit_amount DECIMAL(15,2) DEFAULT 0 COMMENT '贷方金额',
    description TEXT COMMENT '摘要',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (voucher_id) REFERENCES financial_vouchers(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES financial_accounts(id) ON DELETE CASCADE,
    INDEX idx_voucher_id (voucher_id),
    INDEX idx_account_id (account_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财务凭证明细表';

-- 插入默认财务科目
INSERT INTO financial_accounts (account_code, account_name, account_type, level, is_leaf, description) VALUES
-- 资产类
('1001', '库存现金', 'asset', 1, TRUE, '库存现金'),
('1002', '银行存款', 'asset', 1, TRUE, '银行存款'),
('1122', '应收账款', 'asset', 1, TRUE, '应收账款'),
('1123', '预付账款', 'asset', 1, TRUE, '预付账款'),
('1401', '原材料', 'asset', 1, TRUE, '原材料'),
('1402', '库存商品', 'asset', 1, TRUE, '库存商品'),
('1601', '固定资产', 'asset', 1, TRUE, '固定资产'),

-- 负债类
('2202', '应付账款', 'liability', 1, TRUE, '应付账款'),
('2203', '预收账款', 'liability', 1, TRUE, '预收账款'),
('2221', '应付职工薪酬', 'liability', 1, TRUE, '应付职工薪酬'),
('2401', '应交税费', 'liability', 1, TRUE, '应交税费'),

-- 所有者权益类
('4001', '实收资本', 'equity', 1, TRUE, '实收资本'),
('4103', '本年利润', 'equity', 1, TRUE, '本年利润'),

-- 收入类
('6001', '主营业务收入', 'revenue', 1, TRUE, '主营业务收入'),
('6051', '其他业务收入', 'revenue', 1, TRUE, '其他业务收入'),

-- 费用类
('6401', '主营业务成本', 'expense', 1, TRUE, '主营业务成本'),
('6402', '其他业务成本', 'expense', 1, TRUE, '其他业务成本'),
('6601', '销售费用', 'expense', 1, TRUE, '销售费用'),
('6602', '管理费用', 'expense', 1, TRUE, '管理费用'),
('6603', '财务费用', 'expense', 1, TRUE, '财务费用')

ON DUPLICATE KEY UPDATE 
    account_name = VALUES(account_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- 插入测试数据
-- 应收账款测试数据
INSERT INTO accounts_receivable (ar_number, customer_id, invoice_number, invoice_date, due_date, original_amount, outstanding_amount, status, payment_terms, description) 
SELECT 
    CONCAT('AR', DATE_FORMAT(CURDATE(), '%Y%m%d'), LPAD(ROW_NUMBER() OVER (ORDER BY c.id), 3, '0')) as ar_number,
    c.id as customer_id,
    CONCAT('INV', DATE_FORMAT(CURDATE(), '%Y%m%d'), LPAD(ROW_NUMBER() OVER (ORDER BY c.id), 3, '0')) as invoice_number,
    CURDATE() - INTERVAL FLOOR(RAND() * 90) DAY as invoice_date,
    CURDATE() + INTERVAL (30 - FLOOR(RAND() * 60)) DAY as due_date,
    ROUND(RAND() * 50000 + 10000, 2) as original_amount,
    ROUND(RAND() * 50000 + 10000, 2) as outstanding_amount,
    CASE FLOOR(RAND() * 4)
        WHEN 0 THEN 'pending'
        WHEN 1 THEN 'partial'
        WHEN 2 THEN 'paid'
        ELSE 'overdue'
    END as status,
    '30天' as payment_terms,
    '测试应收账款数据' as description
FROM customers c
WHERE c.is_active = TRUE
LIMIT 5;

-- 应付账款测试数据
INSERT INTO accounts_payable (ap_number, supplier_id, invoice_number, invoice_date, due_date, original_amount, outstanding_amount, status, payment_terms, description)
SELECT 
    CONCAT('AP', DATE_FORMAT(CURDATE(), '%Y%m%d'), LPAD(ROW_NUMBER() OVER (ORDER BY s.id), 3, '0')) as ap_number,
    s.id as supplier_id,
    CONCAT('SINV', DATE_FORMAT(CURDATE(), '%Y%m%d'), LPAD(ROW_NUMBER() OVER (ORDER BY s.id), 3, '0')) as invoice_number,
    CURDATE() - INTERVAL FLOOR(RAND() * 60) DAY as invoice_date,
    CURDATE() + INTERVAL (30 - FLOOR(RAND() * 60)) DAY as due_date,
    ROUND(RAND() * 30000 + 5000, 2) as original_amount,
    ROUND(RAND() * 30000 + 5000, 2) as outstanding_amount,
    CASE FLOOR(RAND() * 4)
        WHEN 0 THEN 'pending'
        WHEN 1 THEN 'partial'
        WHEN 2 THEN 'paid'
        ELSE 'overdue'
    END as status,
    '30天' as payment_terms,
    '测试应付账款数据' as description
FROM suppliers s
WHERE s.is_active = TRUE
LIMIT 5;

-- 创建视图：财务汇总
CREATE OR REPLACE VIEW v_financial_summary AS
SELECT 
    'accounts_receivable' as account_type,
    '应收账款' as account_name,
    SUM(outstanding_amount) as total_amount,
    COUNT(*) as record_count,
    SUM(CASE WHEN due_date < CURDATE() THEN outstanding_amount ELSE 0 END) as overdue_amount,
    SUM(CASE WHEN due_date >= CURDATE() THEN outstanding_amount ELSE 0 END) as current_amount
FROM accounts_receivable
WHERE status IN ('pending', 'partial', 'overdue')

UNION ALL

SELECT 
    'accounts_payable' as account_type,
    '应付账款' as account_name,
    SUM(outstanding_amount) as total_amount,
    COUNT(*) as record_count,
    SUM(CASE WHEN due_date < CURDATE() THEN outstanding_amount ELSE 0 END) as overdue_amount,
    SUM(CASE WHEN due_date >= CURDATE() THEN outstanding_amount ELSE 0 END) as current_amount
FROM accounts_payable
WHERE status IN ('pending', 'partial', 'overdue');

-- 创建触发器：更新应收账款状态
DELIMITER //
CREATE TRIGGER IF NOT EXISTS tr_update_ar_status
BEFORE UPDATE ON accounts_receivable
FOR EACH ROW
BEGIN
    -- 根据付款情况自动更新状态
    IF NEW.paid_amount >= NEW.original_amount THEN
        SET NEW.status = 'paid';
        SET NEW.outstanding_amount = 0;
    ELSEIF NEW.paid_amount > 0 THEN
        SET NEW.status = 'partial';
        SET NEW.outstanding_amount = NEW.original_amount - NEW.paid_amount;
    ELSEIF NEW.due_date < CURDATE() AND NEW.outstanding_amount > 0 THEN
        SET NEW.status = 'overdue';
    ELSE
        SET NEW.status = 'pending';
    END IF;
END //
DELIMITER ;

-- 创建触发器：更新应付账款状态
DELIMITER //
CREATE TRIGGER IF NOT EXISTS tr_update_ap_status
BEFORE UPDATE ON accounts_payable
FOR EACH ROW
BEGIN
    -- 根据付款情况自动更新状态
    IF NEW.paid_amount >= NEW.original_amount THEN
        SET NEW.status = 'paid';
        SET NEW.outstanding_amount = 0;
    ELSEIF NEW.paid_amount > 0 THEN
        SET NEW.status = 'partial';
        SET NEW.outstanding_amount = NEW.original_amount - NEW.paid_amount;
    ELSEIF NEW.due_date < CURDATE() AND NEW.outstanding_amount > 0 THEN
        SET NEW.status = 'overdue';
    ELSE
        SET NEW.status = 'pending';
    END IF;
END //
DELIMITER ;

-- 授权语句（根据需要调整）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.accounts_receivable TO 'erp_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.accounts_payable TO 'erp_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.payment_records TO 'erp_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.financial_accounts TO 'erp_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.financial_vouchers TO 'erp_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ERPSystem.financial_voucher_details TO 'erp_user'@'localhost';

-- 查询语句示例
-- 查看财务汇总
-- SELECT * FROM v_financial_summary;

-- 查看逾期应收账款
-- SELECT * FROM accounts_receivable WHERE status = 'overdue' ORDER BY due_date;

-- 查看逾期应付账款
-- SELECT * FROM accounts_payable WHERE status = 'overdue' ORDER BY due_date;
