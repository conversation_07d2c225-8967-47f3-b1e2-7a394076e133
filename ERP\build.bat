@echo off
echo ========================================
echo    ERP库存管理系统 - 编译脚本
echo ========================================
echo.

echo 正在清理旧的编译文件...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"

echo.
echo 正在还原NuGet包...
dotnet restore InventorySystem.vbproj
if %errorlevel% neq 0 (
    echo 错误: NuGet包还原失败
    pause
    exit /b 1
)

echo.
echo 正在编译Debug版本...
dotnet build InventorySystem.vbproj --configuration Debug
if %errorlevel% neq 0 (
    echo 错误: Debug编译失败
    pause
    exit /b 1
)

echo.
echo 正在编译Release版本...
dotnet build InventorySystem.vbproj --configuration Release
if %errorlevel% neq 0 (
    echo 错误: Release编译失败
    pause
    exit /b 1
)

echo.
echo 正在发布单文件可执行程序...
dotnet publish InventorySystem.vbproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output "publish\win-x64" ^
    /p:PublishSingleFile=true ^
    /p:PublishReadyToRun=true ^
    /p:IncludeNativeLibrariesForSelfExtract=true

if %errorlevel% neq 0 (
    echo 错误: 发布失败
    pause
    exit /b 1
)

echo.
echo 正在发布Framework依赖版本...
dotnet publish InventorySystem.vbproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained false ^
    --output "publish\framework-dependent"

if %errorlevel% neq 0 (
    echo 错误: Framework依赖版本发布失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo           编译完成！
echo ========================================
echo.
echo 输出文件位置:
echo 1. 单文件版本: publish\win-x64\InventorySystem.exe
echo 2. Framework依赖版本: publish\framework-dependent\InventorySystem.exe
echo.
echo 单文件版本包含所有依赖，可以在没有安装.NET的机器上运行
echo Framework依赖版本需要目标机器安装.NET 6.0 Runtime
echo.

echo 正在打开发布目录...
start "" "publish"

echo.
echo 按任意键退出...
pause >nul
