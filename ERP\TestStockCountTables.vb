Imports MySql.Data.MySqlClient

Module TestStockCountTables
    Sub Main()
        Try
            Console.WriteLine("测试库存盘点表是否存在...")
            
            ' 初始化数据库
            DatabaseManager.Initialize()
            
            ' 测试stock_counts表
            Dim query1 As String = "SELECT COUNT(*) FROM stock_counts"
            Dim count1 As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(query1))
            Console.WriteLine($"stock_counts表记录数: {count1}")
            
            ' 测试stock_count_details表
            Dim query2 As String = "SELECT COUNT(*) FROM stock_count_details"
            Dim count2 As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(query2))
            Console.WriteLine($"stock_count_details表记录数: {count2}")
            
            ' 测试stock_alert_history表
            Dim query3 As String = "SELECT COUNT(*) FROM stock_alert_history"
            Dim count3 As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(query3))
            Console.WriteLine($"stock_alert_history表记录数: {count3}")
            
            Console.WriteLine("所有库存盘点表都已成功创建！")
            
        Catch ex As Exception
            Console.WriteLine($"错误: {ex.Message}")
        End Try
        
        Console.WriteLine("按任意键退出...")
        Console.ReadKey()
    End Sub
End Module
