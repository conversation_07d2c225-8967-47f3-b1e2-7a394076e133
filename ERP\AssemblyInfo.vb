Imports System.Reflection
Imports System.Runtime.InteropServices

' 有关程序集的一般信息由以下
' 控制。更改这些特性值可修改
' 与程序集关联的信息。

' 查看程序集特性的值
<Assembly: AssemblyTitle("库存管理系统")>
<Assembly: AssemblyDescription("基于VB.NET和MySQL的ERP库存管理系统")>
<Assembly: AssemblyConfiguration("Debug")>
<Assembly: AssemblyCompany("您的公司名称")>
<Assembly: AssemblyProduct("ERP库存管理系统")>
<Assembly: AssemblyCopyright("Copyright © 2025")>
<Assembly: AssemblyTrademark("")>

' 将 ComVisible 设置为 False 会使此程序集中的类型
' 对 COM 组件不可见。如果需要从 COM 访问此程序集中的类型
' 请将此类型的 ComVisible 特性设置为 True。
<Assembly: ComVisible(False)>

' 如果此项目向 COM 公开，则下列 GUID 用于类型库的 ID
<Assembly: Guid("12345678-1234-1234-1234-123456789012")>

' 程序集的版本信息由下列四个值组成: 
'
'      主版本
'      次版本
'      生成号
'      修订号
'
' 可以指定所有值，也可以使用以下所示的 "*" 预置版本号和修订号
' 方法是按如下所示使用"*": :
' <Assembly: AssemblyVersion("1.0.*")>

<Assembly: AssemblyVersion("*******")>
<Assembly: AssemblyFileVersion("*******")>
<Assembly: AssemblyInformationalVersion("1.0.0")>
