Imports System.Windows.Forms
Imports System.Data
Imports System.Drawing

''' <summary>
''' 库存盘点管理窗体
''' </summary>
Public Class StockCountForm
    Inherits Form

    Private tabControl As TabControl
    Private tabCountList As TabPage
    Private tabCountDetail As TabPage
    Private tabCountAnalysis As TabPage

    ' 盘点单列表控件
    Private dgvCountList As DataGridView
    Private btnNewCount As Button
    Private btnEditCount As Button
    Private btnDeleteCount As Button
    Private btnRefreshList As Button
    Private btnStartCount As Button
    Private btnFinishCount As Button

    ' 盘点明细控件
    Private dgvCountDetail As DataGridView
    Private txtCountNumber As TextBox
    Private txtCountDate As DateTimePicker
    Private txtCountPerson As TextBox
    Private cmbCountStatus As ComboBox
    Private txtRemarks As TextBox
    Private btnAddItem As Button
    Private btnRemoveItem As Button
    Private btnSaveDetail As Button
    Private btnGenerateAdjustment As Button

    ' 盘点分析控件
    Private dgvAnalysis As DataGridView
    Private lblTotalItems As Label
    Private lblDifferenceItems As Label
    Private lblTotalDifference As Label

    Private currentCountId As Integer = 0

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadData()
        ApplyPermissions()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "库存盘点管理"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
    End Sub

    Private Sub SetupUI()
        ' 创建选项卡控件
        tabControl = New TabControl()
        tabControl.Dock = DockStyle.Fill
        Me.Controls.Add(tabControl)

        ' 盘点单列表选项卡
        tabCountList = New TabPage("盘点单列表")
        tabControl.TabPages.Add(tabCountList)
        SetupCountListTab()

        ' 盘点明细选项卡
        tabCountDetail = New TabPage("盘点明细")
        tabControl.TabPages.Add(tabCountDetail)
        SetupCountDetailTab()

        ' 盘点分析选项卡
        tabCountAnalysis = New TabPage("盘点分析")
        tabControl.TabPages.Add(tabCountAnalysis)
        SetupCountAnalysisTab()
    End Sub

    Private Sub SetupCountListTab()
        ' 按钮面板
        Dim pnlButtons As New Panel()
        pnlButtons.Height = 50
        pnlButtons.Dock = DockStyle.Top
        tabCountList.Controls.Add(pnlButtons)

        btnNewCount = New Button()
        btnNewCount.Text = "新建盘点"
        btnNewCount.Location = New Point(20, 10)
        btnNewCount.Size = New Size(100, 30)
        pnlButtons.Controls.Add(btnNewCount)

        btnEditCount = New Button()
        btnEditCount.Text = "编辑盘点"
        btnEditCount.Location = New Point(130, 10)
        btnEditCount.Size = New Size(100, 30)
        pnlButtons.Controls.Add(btnEditCount)

        btnDeleteCount = New Button()
        btnDeleteCount.Text = "删除盘点"
        btnDeleteCount.Location = New Point(240, 10)
        btnDeleteCount.Size = New Size(100, 30)
        pnlButtons.Controls.Add(btnDeleteCount)

        btnStartCount = New Button()
        btnStartCount.Text = "开始盘点"
        btnStartCount.Location = New Point(350, 10)
        btnStartCount.Size = New Size(100, 30)
        pnlButtons.Controls.Add(btnStartCount)

        btnFinishCount = New Button()
        btnFinishCount.Text = "完成盘点"
        btnFinishCount.Location = New Point(460, 10)
        btnFinishCount.Size = New Size(100, 30)
        pnlButtons.Controls.Add(btnFinishCount)

        btnRefreshList = New Button()
        btnRefreshList.Text = "刷新"
        btnRefreshList.Location = New Point(570, 10)
        btnRefreshList.Size = New Size(80, 30)
        pnlButtons.Controls.Add(btnRefreshList)

        ' 盘点单列表数据表格
        dgvCountList = New DataGridView()
        dgvCountList.Dock = DockStyle.Fill
        dgvCountList.ReadOnly = True
        dgvCountList.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvCountList.AllowUserToAddRows = False
        dgvCountList.AllowUserToDeleteRows = False
        dgvCountList.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabCountList.Controls.Add(dgvCountList)

        ' 事件处理
        AddHandler dgvCountList.SelectionChanged, AddressOf DgvCountList_SelectionChanged
        AddHandler btnNewCount.Click, AddressOf BtnNewCount_Click
        AddHandler btnEditCount.Click, AddressOf BtnEditCount_Click
        AddHandler btnDeleteCount.Click, AddressOf BtnDeleteCount_Click
        AddHandler btnStartCount.Click, AddressOf BtnStartCount_Click
        AddHandler btnFinishCount.Click, AddressOf BtnFinishCount_Click
        AddHandler btnRefreshList.Click, AddressOf BtnRefreshList_Click
    End Sub

    Private Sub SetupCountDetailTab()
        ' 盘点单信息面板
        Dim pnlCountInfo As New Panel()
        pnlCountInfo.Height = 120
        pnlCountInfo.Dock = DockStyle.Top
        tabCountDetail.Controls.Add(pnlCountInfo)

        ' 盘点单号
        Dim lblCountNumber As New Label()
        lblCountNumber.Text = "盘点单号:"
        lblCountNumber.Location = New Point(20, 20)
        lblCountNumber.Size = New Size(80, 20)
        pnlCountInfo.Controls.Add(lblCountNumber)

        txtCountNumber = New TextBox()
        txtCountNumber.Location = New Point(110, 18)
        txtCountNumber.Size = New Size(150, 23)
        txtCountNumber.ReadOnly = True
        pnlCountInfo.Controls.Add(txtCountNumber)

        ' 盘点日期
        Dim lblCountDate As New Label()
        lblCountDate.Text = "盘点日期:"
        lblCountDate.Location = New Point(280, 20)
        lblCountDate.Size = New Size(80, 20)
        pnlCountInfo.Controls.Add(lblCountDate)

        txtCountDate = New DateTimePicker()
        txtCountDate.Location = New Point(370, 18)
        txtCountDate.Size = New Size(150, 23)
        txtCountDate.Format = DateTimePickerFormat.Short
        pnlCountInfo.Controls.Add(txtCountDate)

        ' 盘点人
        Dim lblCountPerson As New Label()
        lblCountPerson.Text = "盘点人:"
        lblCountPerson.Location = New Point(540, 20)
        lblCountPerson.Size = New Size(60, 20)
        pnlCountInfo.Controls.Add(lblCountPerson)

        txtCountPerson = New TextBox()
        txtCountPerson.Location = New Point(610, 18)
        txtCountPerson.Size = New Size(150, 23)
        pnlCountInfo.Controls.Add(txtCountPerson)

        ' 盘点状态
        Dim lblCountStatus As New Label()
        lblCountStatus.Text = "盘点状态:"
        lblCountStatus.Location = New Point(20, 55)
        lblCountStatus.Size = New Size(80, 20)
        pnlCountInfo.Controls.Add(lblCountStatus)

        cmbCountStatus = New ComboBox()
        cmbCountStatus.Location = New Point(110, 53)
        cmbCountStatus.Size = New Size(150, 23)
        cmbCountStatus.DropDownStyle = ComboBoxStyle.DropDownList
        cmbCountStatus.Items.AddRange({"计划中", "进行中", "已完成", "已取消"})
        pnlCountInfo.Controls.Add(cmbCountStatus)

        ' 备注
        Dim lblRemarks As New Label()
        lblRemarks.Text = "备注:"
        lblRemarks.Location = New Point(280, 55)
        lblRemarks.Size = New Size(50, 20)
        pnlCountInfo.Controls.Add(lblRemarks)

        txtRemarks = New TextBox()
        txtRemarks.Location = New Point(340, 53)
        txtRemarks.Size = New Size(300, 23)
        pnlCountInfo.Controls.Add(txtRemarks)

        ' 按钮
        btnSaveDetail = New Button()
        btnSaveDetail.Text = "保存"
        btnSaveDetail.Location = New Point(20, 85)
        btnSaveDetail.Size = New Size(80, 25)
        pnlCountInfo.Controls.Add(btnSaveDetail)

        btnGenerateAdjustment = New Button()
        btnGenerateAdjustment.Text = "生成调整单"
        btnGenerateAdjustment.Location = New Point(110, 85)
        btnGenerateAdjustment.Size = New Size(100, 25)
        pnlCountInfo.Controls.Add(btnGenerateAdjustment)

        ' 明细操作面板
        Dim pnlDetailButtons As New Panel()
        pnlDetailButtons.Height = 50
        pnlDetailButtons.Dock = DockStyle.Top
        tabCountDetail.Controls.Add(pnlDetailButtons)

        btnAddItem = New Button()
        btnAddItem.Text = "添加物料"
        btnAddItem.Location = New Point(20, 10)
        btnAddItem.Size = New Size(100, 30)
        pnlDetailButtons.Controls.Add(btnAddItem)

        btnRemoveItem = New Button()
        btnRemoveItem.Text = "移除物料"
        btnRemoveItem.Location = New Point(130, 10)
        btnRemoveItem.Size = New Size(100, 30)
        pnlDetailButtons.Controls.Add(btnRemoveItem)

        ' 盘点明细数据表格
        dgvCountDetail = New DataGridView()
        dgvCountDetail.Dock = DockStyle.Fill
        dgvCountDetail.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvCountDetail.AllowUserToAddRows = False
        dgvCountDetail.AllowUserToDeleteRows = False
        dgvCountDetail.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabCountDetail.Controls.Add(dgvCountDetail)

        ' 事件处理
        AddHandler btnSaveDetail.Click, AddressOf BtnSaveDetail_Click
        AddHandler btnGenerateAdjustment.Click, AddressOf BtnGenerateAdjustment_Click
        AddHandler btnAddItem.Click, AddressOf BtnAddItem_Click
        AddHandler btnRemoveItem.Click, AddressOf BtnRemoveItem_Click
    End Sub

    Private Sub SetupCountAnalysisTab()
        ' 统计信息面板
        Dim pnlStats As New Panel()
        pnlStats.Height = 80
        pnlStats.Dock = DockStyle.Top
        pnlStats.BackColor = Color.LightGray
        tabCountAnalysis.Controls.Add(pnlStats)

        lblTotalItems = New Label()
        lblTotalItems.Text = "盘点物料总数: 0"
        lblTotalItems.Location = New Point(20, 20)
        lblTotalItems.Size = New Size(150, 20)
        lblTotalItems.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        pnlStats.Controls.Add(lblTotalItems)

        lblDifferenceItems = New Label()
        lblDifferenceItems.Text = "有差异物料数: 0"
        lblDifferenceItems.Location = New Point(200, 20)
        lblDifferenceItems.Size = New Size(150, 20)
        lblDifferenceItems.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblDifferenceItems.ForeColor = Color.Red
        pnlStats.Controls.Add(lblDifferenceItems)

        lblTotalDifference = New Label()
        lblTotalDifference.Text = "总差异金额: ¥0.00"
        lblTotalDifference.Location = New Point(380, 20)
        lblTotalDifference.Size = New Size(150, 20)
        lblTotalDifference.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblTotalDifference.ForeColor = Color.Blue
        pnlStats.Controls.Add(lblTotalDifference)

        ' 盘点分析数据表格
        dgvAnalysis = New DataGridView()
        dgvAnalysis.Dock = DockStyle.Fill
        dgvAnalysis.ReadOnly = True
        dgvAnalysis.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvAnalysis.AllowUserToAddRows = False
        dgvAnalysis.AllowUserToDeleteRows = False
        dgvAnalysis.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabCountAnalysis.Controls.Add(dgvAnalysis)
    End Sub

    Private Sub LoadData()
        LoadCountList()
    End Sub

    Private Sub LoadCountList()
        Try
            Dim query As String = "
                SELECT
                    sc.id,
                    sc.count_number AS '盘点单号',
                    sc.count_date AS '盘点日期',
                    sc.count_person AS '盘点人',
                    CASE sc.count_status
                        WHEN 'planned' THEN '计划中'
                        WHEN 'in_progress' THEN '进行中'
                        WHEN 'completed' THEN '已完成'
                        WHEN 'cancelled' THEN '已取消'
                        ELSE sc.count_status
                    END AS '盘点状态',
                    sc.remarks AS '备注',
                    sc.created_at AS '创建时间'
                FROM stock_counts sc
                ORDER BY sc.created_at DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvCountList.DataSource = dt

            ' 隐藏ID列
            If dgvCountList.Columns.Contains("id") Then
                dgvCountList.Columns("id").Visible = False
            End If

            ' 设置行颜色
            For Each row As DataGridViewRow In dgvCountList.Rows
                Select Case row.Cells("盘点状态").Value?.ToString()
                    Case "计划中"
                        row.DefaultCellStyle.BackColor = Color.LightBlue
                    Case "进行中"
                        row.DefaultCellStyle.BackColor = Color.LightYellow
                    Case "已完成"
                        row.DefaultCellStyle.BackColor = Color.LightGreen
                    Case "已取消"
                        row.DefaultCellStyle.BackColor = Color.LightGray
                End Select
            Next

        Catch ex As Exception
            MessageBox.Show($"加载盘点单列表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub DgvCountList_SelectionChanged(sender As Object, e As EventArgs)
        If dgvCountList.SelectedRows.Count > 0 Then
            currentCountId = Convert.ToInt32(dgvCountList.SelectedRows(0).Cells("id").Value)
            LoadCountDetail(currentCountId)
            LoadCountAnalysis(currentCountId)
        End If
    End Sub

    Private Sub LoadCountDetail(countId As Integer)
        Try
            ' 加载盘点单基本信息
            Dim query As String = "SELECT * FROM stock_counts WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {{"id", countId}}
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)

            If dt.Rows.Count > 0 Then
                Dim row As DataRow = dt.Rows(0)
                txtCountNumber.Text = row("count_number").ToString()
                txtCountDate.Value = Convert.ToDateTime(row("count_date"))
                txtCountPerson.Text = row("count_person").ToString()
                txtRemarks.Text = row("remarks").ToString()

                ' 设置状态
                Select Case row("count_status").ToString()
                    Case "planned"
                        cmbCountStatus.SelectedIndex = 0
                    Case "in_progress"
                        cmbCountStatus.SelectedIndex = 1
                    Case "completed"
                        cmbCountStatus.SelectedIndex = 2
                    Case "cancelled"
                        cmbCountStatus.SelectedIndex = 3
                End Select
            End If

            ' 加载盘点明细
            LoadCountDetailItems(countId)

        Catch ex As Exception
            MessageBox.Show($"加载盘点详情失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCountDetailItems(countId As Integer)
        Try
            Dim query As String = "
                SELECT
                    scd.id,
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    l.location_code AS '库位编码',
                    scd.book_quantity AS '账面数量',
                    scd.actual_quantity AS '实盘数量',
                    (scd.actual_quantity - scd.book_quantity) AS '差异数量',
                    m.unit AS '单位',
                    scd.remarks AS '备注'
                FROM stock_count_details scd
                INNER JOIN materials m ON scd.material_id = m.id
                INNER JOIN locations l ON scd.location_id = l.id
                WHERE scd.stock_count_id = @count_id
                ORDER BY m.material_code, l.location_code"

            Dim parameters As New Dictionary(Of String, Object) From {{"count_id", countId}}
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvCountDetail.DataSource = dt

            ' 隐藏ID列
            If dgvCountDetail.Columns.Contains("id") Then
                dgvCountDetail.Columns("id").Visible = False
            End If

            ' 设置差异行颜色
            For Each row As DataGridViewRow In dgvCountDetail.Rows
                Dim difference As Decimal = Convert.ToDecimal(row.Cells("差异数量").Value)
                If difference <> 0 Then
                    If difference > 0 Then
                        row.DefaultCellStyle.BackColor = Color.LightGreen ' 盘盈
                    Else
                        row.DefaultCellStyle.BackColor = Color.LightPink ' 盘亏
                    End If
                End If
            Next

        Catch ex As Exception
            MessageBox.Show($"加载盘点明细失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCountAnalysis(countId As Integer)
        Try
            Dim query As String = "
                SELECT
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    l.location_code AS '库位编码',
                    scd.book_quantity AS '账面数量',
                    scd.actual_quantity AS '实盘数量',
                    (scd.actual_quantity - scd.book_quantity) AS '差异数量',
                    m.standard_price AS '标准价格',
                    ((scd.actual_quantity - scd.book_quantity) * m.standard_price) AS '差异金额',
                    m.unit AS '单位',
                    CASE
                        WHEN (scd.actual_quantity - scd.book_quantity) > 0 THEN '盘盈'
                        WHEN (scd.actual_quantity - scd.book_quantity) < 0 THEN '盘亏'
                        ELSE '无差异'
                    END AS '差异类型'
                FROM stock_count_details scd
                INNER JOIN materials m ON scd.material_id = m.id
                INNER JOIN locations l ON scd.location_id = l.id
                WHERE scd.stock_count_id = @count_id
                AND (scd.actual_quantity - scd.book_quantity) <> 0
                ORDER BY ABS(scd.actual_quantity - scd.book_quantity) DESC"

            Dim parameters As New Dictionary(Of String, Object) From {{"count_id", countId}}
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvAnalysis.DataSource = dt

            ' 更新统计信息
            UpdateAnalysisStatistics(countId)

            ' 设置行颜色
            For Each row As DataGridViewRow In dgvAnalysis.Rows
                Select Case row.Cells("差异类型").Value?.ToString()
                    Case "盘盈"
                        row.DefaultCellStyle.BackColor = Color.LightGreen
                    Case "盘亏"
                        row.DefaultCellStyle.BackColor = Color.LightPink
                End Select
            Next

        Catch ex As Exception
            MessageBox.Show($"加载盘点分析失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateAnalysisStatistics(countId As Integer)
        Try
            ' 获取统计数据
            Dim query As String = "
                SELECT
                    COUNT(*) as total_items,
                    SUM(CASE WHEN (actual_quantity - book_quantity) <> 0 THEN 1 ELSE 0 END) as difference_items,
                    SUM((actual_quantity - book_quantity) * (SELECT standard_price FROM materials WHERE id = material_id)) as total_difference_amount
                FROM stock_count_details
                WHERE stock_count_id = @count_id"

            Dim parameters As New Dictionary(Of String, Object) From {{"count_id", countId}}
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)

            If dt.Rows.Count > 0 Then
                Dim row As DataRow = dt.Rows(0)
                lblTotalItems.Text = $"盘点物料总数: {row("total_items")}"
                lblDifferenceItems.Text = $"有差异物料数: {row("difference_items")}"

                Dim totalDifference As Decimal = If(IsDBNull(row("total_difference_amount")), 0, Convert.ToDecimal(row("total_difference_amount")))
                lblTotalDifference.Text = $"总差异金额: ¥{totalDifference:N2}"
            End If

        Catch ex As Exception
            MessageBox.Show($"更新统计信息失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 按钮事件处理
    Private Sub BtnNewCount_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageInventoryAdjustment) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        Try
            ' 生成盘点单号
            Dim countNumber As String = GenerateCountNumber()

            Dim parameters As New Dictionary(Of String, Object) From {
                {"count_number", countNumber},
                {"count_date", DateTime.Now.Date},
                {"count_person", UserSession.GetDisplayName()},
                {"count_status", "planned"},
                {"remarks", ""}
            }

            Dim query As String = "
                INSERT INTO stock_counts (count_number, count_date, count_person, count_status, remarks)
                VALUES (@count_number, @count_date, @count_person, @count_status, @remarks)"

            DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
            MessageBox.Show("盘点单创建成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadCountList()

        Catch ex As Exception
            MessageBox.Show($"创建盘点单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function GenerateCountNumber() As String
        Dim today As String = DateTime.Now.ToString("yyyyMMdd")
        Dim query As String = "SELECT COUNT(*) FROM stock_counts WHERE count_number LIKE @pattern"
        Dim parameters As New Dictionary(Of String, Object) From {{"pattern", $"SC{today}%"}}

        Dim count As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(query, parameters))
        Return $"SC{today}{(count + 1):D3}"
    End Function

    Private Sub BtnEditCount_Click(sender As Object, e As EventArgs)
        If currentCountId = 0 Then
            MessageBox.Show("请先选择要编辑的盘点单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        tabControl.SelectedTab = tabCountDetail
    End Sub

    Private Sub BtnDeleteCount_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageInventoryAdjustment) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        If currentCountId = 0 Then
            MessageBox.Show("请先选择要删除的盘点单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        If MessageBox.Show("确定要删除选中的盘点单吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                ' 先删除明细
                Dim deleteDetailQuery As String = "DELETE FROM stock_count_details WHERE stock_count_id = @id"
                Dim deleteMainQuery As String = "DELETE FROM stock_counts WHERE id = @id"
                Dim parameters As New Dictionary(Of String, Object) From {{"id", currentCountId}}

                DatabaseManager.Instance.ExecuteNonQuery(deleteDetailQuery, parameters)
                DatabaseManager.Instance.ExecuteNonQuery(deleteMainQuery, parameters)

                MessageBox.Show("盘点单删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadCountList()
                currentCountId = 0

            Catch ex As Exception
                MessageBox.Show($"删除盘点单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnStartCount_Click(sender As Object, e As EventArgs)
        If currentCountId = 0 Then
            MessageBox.Show("请先选择要开始的盘点单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Try
            Dim parameters As New Dictionary(Of String, Object) From {
                {"id", currentCountId},
                {"status", "in_progress"}
            }

            Dim query As String = "UPDATE stock_counts SET count_status = @status WHERE id = @id"
            DatabaseManager.Instance.ExecuteNonQuery(query, parameters)

            MessageBox.Show("盘点已开始！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadCountList()

        Catch ex As Exception
            MessageBox.Show($"开始盘点失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnFinishCount_Click(sender As Object, e As EventArgs)
        If currentCountId = 0 Then
            MessageBox.Show("请先选择要完成的盘点单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Try
            Dim parameters As New Dictionary(Of String, Object) From {
                {"id", currentCountId},
                {"status", "completed"}
            }

            Dim query As String = "UPDATE stock_counts SET count_status = @status WHERE id = @id"
            DatabaseManager.Instance.ExecuteNonQuery(query, parameters)

            MessageBox.Show("盘点已完成！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadCountList()

        Catch ex As Exception
            MessageBox.Show($"完成盘点失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnRefreshList_Click(sender As Object, e As EventArgs)
        LoadCountList()
    End Sub

    Private Sub BtnSaveDetail_Click(sender As Object, e As EventArgs)
        If currentCountId = 0 Then
            MessageBox.Show("请先选择盘点单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Try
            Dim parameters As New Dictionary(Of String, Object) From {
                {"id", currentCountId},
                {"count_date", txtCountDate.Value.Date},
                {"count_person", txtCountPerson.Text.Trim()},
                {"count_status", GetStatusValue(cmbCountStatus.SelectedIndex)},
                {"remarks", txtRemarks.Text.Trim()}
            }

            Dim query As String = "
                UPDATE stock_counts
                SET count_date = @count_date, count_person = @count_person,
                    count_status = @count_status, remarks = @remarks
                WHERE id = @id"

            DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
            MessageBox.Show("盘点单信息保存成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadCountList()

        Catch ex As Exception
            MessageBox.Show($"保存盘点单信息失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function GetStatusValue(index As Integer) As String
        Select Case index
            Case 0
                Return "planned"
            Case 1
                Return "in_progress"
            Case 2
                Return "completed"
            Case 3
                Return "cancelled"
            Case Else
                Return "planned"
        End Select
    End Function

    Private Sub BtnAddItem_Click(sender As Object, e As EventArgs)
        If currentCountId = 0 Then
            MessageBox.Show("请先选择盘点单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' 这里可以打开物料选择对话框
        MessageBox.Show("添加盘点物料功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BtnRemoveItem_Click(sender As Object, e As EventArgs)
        If dgvCountDetail.SelectedRows.Count = 0 Then
            MessageBox.Show("请选择要移除的盘点物料！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        If MessageBox.Show("确定要移除选中的盘点物料吗？", "确认移除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim detailId As Integer = Convert.ToInt32(dgvCountDetail.SelectedRows(0).Cells("id").Value)
                Dim parameters As New Dictionary(Of String, Object) From {{"id", detailId}}
                Dim query As String = "DELETE FROM stock_count_details WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("盘点物料移除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadCountDetailItems(currentCountId)
                LoadCountAnalysis(currentCountId)

            Catch ex As Exception
                MessageBox.Show($"移除盘点物料失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnGenerateAdjustment_Click(sender As Object, e As EventArgs)
        If currentCountId = 0 Then
            MessageBox.Show("请先选择盘点单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' 检查是否有差异需要调整
        Try
            Dim checkQuery As String = "
                SELECT COUNT(*)
                FROM stock_count_details
                WHERE stock_count_id = @count_id
                AND (actual_quantity - book_quantity) <> 0"

            Dim parameters As New Dictionary(Of String, Object) From {{"count_id", currentCountId}}
            Dim differenceCount As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(checkQuery, parameters))

            If differenceCount = 0 Then
                MessageBox.Show("没有发现库存差异，无需生成调整单！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            If MessageBox.Show($"发现 {differenceCount} 项库存差异，确定要生成库存调整单吗？", "确认生成", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                GenerateInventoryAdjustment()
            End If

        Catch ex As Exception
            MessageBox.Show($"检查库存差异失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub GenerateInventoryAdjustment()
        Try
            ' 这里可以实现生成库存调整单的逻辑
            ' 根据盘点差异自动创建入库单或出库单
            MessageBox.Show("库存调整单生成功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"生成库存调整单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ApplyPermissions()
        ' 检查基本查看权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewInventory) Then
            MessageBox.Show("您没有权限访问库存盘点功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 根据权限设置按钮状态
        Dim canManageInventory As Boolean = PermissionManager.HasPermission(PermissionManager.Permission.ManageInventoryAdjustment)

        btnNewCount.Enabled = canManageInventory
        btnEditCount.Enabled = canManageInventory
        btnDeleteCount.Enabled = canManageInventory
        btnStartCount.Enabled = canManageInventory
        btnFinishCount.Enabled = canManageInventory
        btnSaveDetail.Enabled = canManageInventory
        btnAddItem.Enabled = canManageInventory
        btnRemoveItem.Enabled = canManageInventory
        btnGenerateAdjustment.Enabled = canManageInventory

        ' 设置输入控件状态
        txtCountDate.Enabled = canManageInventory
        txtCountPerson.ReadOnly = Not canManageInventory
        cmbCountStatus.Enabled = canManageInventory
        txtRemarks.ReadOnly = Not canManageInventory

        ' 如果没有管理权限，显示只读提示
        If Not canManageInventory Then
            Me.Text = "库存盘点管理 (只读)"
        End If
    End Sub
End Class
