; ERP库存管理系统 - Inno Setup安装脚本
; 需要安装Inno Setup来使用此脚本: https://jrsoftware.org/isinfo.php

[Setup]
AppName=ERP库存管理系统
AppVersion=1.0.0
AppPublisher=您的公司名称
AppPublisherURL=https://www.yourcompany.com
AppSupportURL=https://www.yourcompany.com/support
AppUpdatesURL=https://www.yourcompany.com/updates
DefaultDirName={autopf}\ERP库存管理系统
DefaultGroupName=ERP库存管理系统
AllowNoIcons=yes
LicenseFile=LICENSE.txt
OutputDir=installer
OutputBaseFilename=ERP库存管理系统_v1.0.0_Setup
SetupIconFile=app.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "publish\win-x64\InventorySystem.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "publish\win-x64\InventorySystem.exe.config"; DestDir: "{app}"; Flags: ignoreversion
Source: "README.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "LICENSE.txt"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

[Icons]
Name: "{group}\ERP库存管理系统"; Filename: "{app}\InventorySystem.exe"
Name: "{group}\{cm:UninstallProgram,ERP库存管理系统}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\ERP库存管理系统"; Filename: "{app}\InventorySystem.exe"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\ERP库存管理系统"; Filename: "{app}\InventorySystem.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\InventorySystem.exe"; Description: "{cm:LaunchProgram,ERP库存管理系统}"; Flags: nowait postinstall skipifsilent

[Code]
function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;
