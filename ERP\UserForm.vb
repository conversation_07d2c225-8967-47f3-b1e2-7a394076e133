Imports System.Windows.Forms
Imports MySql.Data.MySqlClient

Public Class UserForm
    Inherits Form

    Private dgvUsers As DataGridView
    Private txtUsername As TextBox
    Private txtPassword As TextBox
    Private txtFullName As TextBox
    Private txtEmail As TextBox
    Private cmbRole As ComboBox
    Private chkIsActive As CheckBox

    Private btnAdd As Button
    Private btnEdit As Button
    Private btnDelete As Button
    Private btnRefresh As Button
    Private btnClear As Button
    Private btnResetPassword As Button

    Private currentUserId As Integer = 0

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadData()
        SetupRoles()
        ApplyPermissions()
    End Sub

    Private Sub SetupUI()
        Me.Text = "用户管理"
        Me.Size = New Size(1000, 600)
        Me.StartPosition = FormStartPosition.CenterScreen

        ' 数据表格
        dgvUsers = New DataGridView()
        dgvUsers.Location = New Point(12, 12)
        dgvUsers.Size = New Size(960, 300)
        dgvUsers.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        dgvUsers.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvUsers.MultiSelect = False
        dgvUsers.ReadOnly = True
        dgvUsers.AllowUserToAddRows = False
        dgvUsers.AllowUserToDeleteRows = False
        Me.Controls.Add(dgvUsers)

        ' 用户名
        Dim lblUsername As New Label()
        lblUsername.Text = "用户名:"
        lblUsername.Location = New Point(12, 330)
        lblUsername.Size = New Size(60, 23)
        Me.Controls.Add(lblUsername)

        txtUsername = New TextBox()
        txtUsername.Location = New Point(80, 328)
        txtUsername.Size = New Size(150, 23)
        Me.Controls.Add(txtUsername)

        ' 密码
        Dim lblPassword As New Label()
        lblPassword.Text = "密码:"
        lblPassword.Location = New Point(250, 330)
        lblPassword.Size = New Size(50, 23)
        Me.Controls.Add(lblPassword)

        txtPassword = New TextBox()
        txtPassword.Location = New Point(310, 328)
        txtPassword.Size = New Size(150, 23)
        txtPassword.UseSystemPasswordChar = True
        Me.Controls.Add(txtPassword)

        ' 姓名
        Dim lblFullName As New Label()
        lblFullName.Text = "姓名:"
        lblFullName.Location = New Point(480, 330)
        lblFullName.Size = New Size(50, 23)
        Me.Controls.Add(lblFullName)

        txtFullName = New TextBox()
        txtFullName.Location = New Point(540, 328)
        txtFullName.Size = New Size(150, 23)
        Me.Controls.Add(txtFullName)

        ' 邮箱
        Dim lblEmail As New Label()
        lblEmail.Text = "邮箱:"
        lblEmail.Location = New Point(12, 370)
        lblEmail.Size = New Size(50, 23)
        Me.Controls.Add(lblEmail)

        txtEmail = New TextBox()
        txtEmail.Location = New Point(80, 368)
        txtEmail.Size = New Size(200, 23)
        Me.Controls.Add(txtEmail)

        ' 角色
        Dim lblRole As New Label()
        lblRole.Text = "角色:"
        lblRole.Location = New Point(300, 370)
        lblRole.Size = New Size(50, 23)
        Me.Controls.Add(lblRole)

        cmbRole = New ComboBox()
        cmbRole.Location = New Point(360, 368)
        cmbRole.Size = New Size(120, 23)
        cmbRole.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbRole)

        ' 是否激活
        chkIsActive = New CheckBox()
        chkIsActive.Text = "激活状态"
        chkIsActive.Location = New Point(500, 370)
        chkIsActive.Size = New Size(80, 23)
        chkIsActive.Checked = True
        Me.Controls.Add(chkIsActive)

        ' 按钮
        btnAdd = New Button()
        btnAdd.Text = "新增"
        btnAdd.Location = New Point(12, 420)
        btnAdd.Size = New Size(80, 30)
        Me.Controls.Add(btnAdd)

        btnEdit = New Button()
        btnEdit.Text = "修改"
        btnEdit.Location = New Point(102, 420)
        btnEdit.Size = New Size(80, 30)
        Me.Controls.Add(btnEdit)

        btnDelete = New Button()
        btnDelete.Text = "删除"
        btnDelete.Location = New Point(192, 420)
        btnDelete.Size = New Size(80, 30)
        Me.Controls.Add(btnDelete)

        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(282, 420)
        btnRefresh.Size = New Size(80, 30)
        Me.Controls.Add(btnRefresh)

        btnClear = New Button()
        btnClear.Text = "清空"
        btnClear.Location = New Point(372, 420)
        btnClear.Size = New Size(80, 30)
        Me.Controls.Add(btnClear)

        btnResetPassword = New Button()
        btnResetPassword.Text = "重置密码"
        btnResetPassword.Location = New Point(462, 420)
        btnResetPassword.Size = New Size(80, 30)
        Me.Controls.Add(btnResetPassword)

        ' 事件处理
        AddHandler dgvUsers.SelectionChanged, AddressOf DgvUsers_SelectionChanged
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        AddHandler btnClear.Click, AddressOf BtnClear_Click
        AddHandler btnResetPassword.Click, AddressOf BtnResetPassword_Click
    End Sub

    Private Sub LoadData()
        Try
            Dim query As String = "
                SELECT 
                    id,
                    username AS '用户名',
                    role AS '角色',
                    full_name AS '姓名',
                    email AS '邮箱',
                    CASE WHEN is_active = 1 THEN '是' ELSE '否' END AS '激活状态',
                    created_at AS '创建时间',
                    updated_at AS '更新时间'
                FROM users 
                ORDER BY created_at DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvUsers.DataSource = dt

            ' 隐藏ID列
            If dgvUsers.Columns.Contains("id") Then
                dgvUsers.Columns("id").Visible = False
            End If

            ' 设置列宽
            dgvUsers.Columns("用户名").Width = 120
            dgvUsers.Columns("角色").Width = 80
            dgvUsers.Columns("姓名").Width = 120
            dgvUsers.Columns("邮箱").Width = 180
            dgvUsers.Columns("激活状态").Width = 80
            dgvUsers.Columns("创建时间").Width = 150
            dgvUsers.Columns("更新时间").Width = 150

        Catch ex As Exception
            MessageBox.Show($"加载用户数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetupRoles()
        cmbRole.Items.Clear()
        cmbRole.Items.Add(New With {.Text = "系统管理员", .Value = "admin"})
        cmbRole.Items.Add(New With {.Text = "部门经理", .Value = "manager"})
        cmbRole.Items.Add(New With {.Text = "普通用户", .Value = "user"})
        cmbRole.Items.Add(New With {.Text = "只读用户", .Value = "readonly"})
        cmbRole.DisplayMember = "Text"
        cmbRole.ValueMember = "Value"
    End Sub

    Private Sub DgvUsers_SelectionChanged(sender As Object, e As EventArgs)
        If dgvUsers.SelectedRows.Count > 0 Then
            Dim row As DataGridViewRow = dgvUsers.SelectedRows(0)
            currentUserId = Convert.ToInt32(row.Cells("id").Value)
            
            ' 加载用户详细信息
            LoadUserDetails(currentUserId)
        End If
    End Sub

    Private Sub LoadUserDetails(userId As Integer)
        Try
            Dim query As String = "SELECT username, role, full_name, email, is_active FROM users WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {{"id", userId}}
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)

            If dt.Rows.Count > 0 Then
                Dim row As DataRow = dt.Rows(0)
                txtUsername.Text = row("username").ToString()
                txtFullName.Text = row("full_name").ToString()
                txtEmail.Text = row("email").ToString()
                chkIsActive.Checked = Convert.ToBoolean(row("is_active"))
                
                ' 设置角色
                Dim roleValue As String = row("role").ToString()
                For i As Integer = 0 To cmbRole.Items.Count - 1
                    Dim item = cmbRole.Items(i)
                    If item.Value = roleValue Then
                        cmbRole.SelectedIndex = i
                        Exit For
                    End If
                Next
                
                ' 清空密码字段（安全考虑）
                txtPassword.Clear()
            End If

        Catch ex As Exception
            MessageBox.Show($"加载用户详情失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageUsers) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        If Not ValidateInput() Then Return

        Try
            Dim parameters As New Dictionary(Of String, Object) From {
                {"username", txtUsername.Text.Trim()},
                {"password", txtPassword.Text},
                {"role", cmbRole.SelectedItem.Value},
                {"full_name", txtFullName.Text.Trim()},
                {"email", txtEmail.Text.Trim()},
                {"is_active", chkIsActive.Checked}
            }

            Dim query As String = "
                INSERT INTO users (username, password, role, full_name, email, is_active)
                VALUES (@username, @password, @role, @full_name, @email, @is_active)"

            DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
            MessageBox.Show("用户添加成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadData()
            ClearForm()

        Catch ex As Exception
            MessageBox.Show($"添加用户失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageUsers) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        If currentUserId = 0 Then
            MessageBox.Show("请先选择要修改的用户！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If Not ValidateInput(False) Then Return

        Try
            Dim parameters As New Dictionary(Of String, Object) From {
                {"id", currentUserId},
                {"username", txtUsername.Text.Trim()},
                {"role", cmbRole.SelectedItem.Value},
                {"full_name", txtFullName.Text.Trim()},
                {"email", txtEmail.Text.Trim()},
                {"is_active", chkIsActive.Checked}
            }

            Dim query As String = "
                UPDATE users
                SET username = @username, role = @role, full_name = @full_name,
                    email = @email, is_active = @is_active
                WHERE id = @id"

            DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
            MessageBox.Show("用户信息修改成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadData()

        Catch ex As Exception
            MessageBox.Show($"修改用户失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageUsers) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        If currentUserId = 0 Then
            MessageBox.Show("请先选择要删除的用户！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        ' 防止删除当前登录用户
        If currentUserId = UserSession.CurrentUserId Then
            MessageBox.Show("不能删除当前登录的用户！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If MessageBox.Show("确定要删除选中的用户吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim parameters As New Dictionary(Of String, Object) From {{"id", currentUserId}}
                Dim query As String = "DELETE FROM users WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("用户删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadData()
                ClearForm()

            Catch ex As Exception
                MessageBox.Show($"删除用户失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadData()
        ClearForm()
    End Sub

    Private Sub BtnClear_Click(sender As Object, e As EventArgs)
        ClearForm()
    End Sub

    Private Sub BtnResetPassword_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageUsers) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        If currentUserId = 0 Then
            MessageBox.Show("请先选择要重置密码的用户！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim newPassword As String = InputBox("请输入新密码:", "重置密码", "123456")
        If String.IsNullOrWhiteSpace(newPassword) Then Return

        Try
            Dim parameters As New Dictionary(Of String, Object) From {
                {"id", currentUserId},
                {"password", newPassword}
            }

            Dim query As String = "UPDATE users SET password = @password WHERE id = @id"
            DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
            MessageBox.Show("密码重置成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"重置密码失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function ValidateInput(Optional checkPassword As Boolean = True) As Boolean
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("请输入用户名！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return False
        End If

        If checkPassword AndAlso String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("请输入密码！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return False
        End If

        If cmbRole.SelectedItem Is Nothing Then
            MessageBox.Show("请选择用户角色！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbRole.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtFullName.Text) Then
            MessageBox.Show("请输入用户姓名！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtFullName.Focus()
            Return False
        End If

        ' 验证邮箱格式
        If Not String.IsNullOrWhiteSpace(txtEmail.Text) AndAlso Not IsValidEmail(txtEmail.Text) Then
            MessageBox.Show("请输入有效的邮箱地址！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtEmail.Focus()
            Return False
        End If

        Return True
    End Function

    Private Function IsValidEmail(email As String) As Boolean
        Try
            Dim addr As New System.Net.Mail.MailAddress(email)
            Return addr.Address = email
        Catch
            Return False
        End Try
    End Function

    Private Sub ClearForm()
        txtUsername.Clear()
        txtPassword.Clear()
        txtFullName.Clear()
        txtEmail.Clear()
        cmbRole.SelectedIndex = -1
        chkIsActive.Checked = True
        currentUserId = 0
    End Sub

    ''' <summary>
    ''' 应用权限控制
    ''' </summary>
    Private Sub ApplyPermissions()
        ' 检查基本查看权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewUsers) Then
            MessageBox.Show("您没有权限访问用户管理功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 根据权限设置按钮状态
        Dim canManageUsers As Boolean = PermissionManager.HasPermission(PermissionManager.Permission.ManageUsers)

        btnAdd.Enabled = canManageUsers
        btnEdit.Enabled = canManageUsers
        btnDelete.Enabled = canManageUsers
        btnResetPassword.Enabled = canManageUsers

        ' 设置输入控件状态
        txtUsername.ReadOnly = Not canManageUsers
        txtPassword.ReadOnly = Not canManageUsers
        txtFullName.ReadOnly = Not canManageUsers
        txtEmail.ReadOnly = Not canManageUsers
        cmbRole.Enabled = canManageUsers
        chkIsActive.Enabled = canManageUsers

        ' 如果没有管理权限，显示提示
        If Not canManageUsers Then
            Me.Text = "用户管理 (只读)"
        End If
    End Sub
End Class
