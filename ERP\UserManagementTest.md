# 用户管理和权限控制测试指南

## 测试用户账户

系统已预置以下测试用户：

| 用户名 | 密码 | 角色 | 权限级别 |
|--------|------|------|----------|
| admin | admin123 | 系统管理员 | 全部权限 |
| manager | manager123 | 部门经理 | 大部分权限（除系统管理） |
| user1 | user123 | 普通用户 | 基础操作权限 |
| readonly | readonly123 | 只读用户 | 仅查看权限 |
| test_user | test123 | 普通用户 | 基础操作权限 |

## 权限测试步骤

### 1. 管理员权限测试 (admin/admin123)
- ✅ 可以访问所有菜单项
- ✅ 可以打开用户管理
- ✅ 可以新增、修改、删除用户
- ✅ 可以重置用户密码
- ✅ 可以访问所有基础数据管理
- ✅ 可以访问库存管理功能
- ✅ 可以访问订单管理功能

### 2. 部门经理权限测试 (manager/manager123)
- ✅ 可以访问大部分菜单项
- ✅ 可以查看用户管理（但不能修改）
- ✅ 可以管理基础数据
- ✅ 可以管理库存和订单
- ❌ 不能访问系统设置

### 3. 普通用户权限测试 (user1/user123)
- ✅ 可以查看基础数据
- ✅ 可以进行入库、出库操作
- ✅ 可以管理订单
- ❌ 不能访问用户管理
- ❌ 不能访问财务相关功能

### 4. 只读用户权限测试 (readonly/readonly123)
- ✅ 可以查看基础数据
- ✅ 可以查看库存信息
- ✅ 可以查看订单信息
- ❌ 不能进行任何修改操作
- ❌ 不能访问用户管理

## 功能测试清单

### 用户管理功能
- [ ] 用户列表显示正常
- [ ] 新增用户功能
- [ ] 修改用户信息
- [ ] 删除用户（不能删除当前登录用户）
- [ ] 重置用户密码
- [ ] 用户状态管理（激活/禁用）
- [ ] 角色选择功能
- [ ] 邮箱格式验证

### 权限控制功能
- [ ] 菜单项根据权限显示/隐藏
- [ ] 无权限操作时显示提示信息
- [ ] 不同角色登录后状态栏显示正确信息
- [ ] 权限检查在各个窗体中正常工作

### 登录系统增强
- [ ] 用户会话信息正确保存
- [ ] 状态栏显示当前用户和角色
- [ ] 登录失败时正确提示

## 测试注意事项

1. **安全性测试**：
   - 不能删除当前登录的用户
   - 密码字段在编辑时不显示原密码
   - 权限检查在所有关键操作中都有效

2. **用户体验测试**：
   - 权限不足时提示信息友好
   - 菜单项根据权限动态调整
   - 状态栏信息实时更新

3. **数据完整性测试**：
   - 用户名唯一性检查
   - 邮箱格式验证
   - 必填字段验证

## 预期结果

- 所有权限控制按预期工作
- 不同角色用户看到不同的界面和功能
- 用户管理功能完整可用
- 系统安全性得到保障
