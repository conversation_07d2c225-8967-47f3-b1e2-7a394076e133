Imports System.Windows.Forms
Imports System.Data

''' <summary>
''' 入库管理窗体
''' </summary>
Public Class InboundForm
    Inherits Form

    Private dgvInbound As DataGridView
    Private txtInboundNumber As TextBox
    Private dtpInboundDate As DateTimePicker
    Private cmbSupplier As ComboBox
    Private cmbInboundType As ComboBox
    Private cmbStatus As ComboBox
    Private txtRemarks As TextBox
    Private txtTotalAmount As TextBox

    Private btnNew As Button
    Private btnEdit As Button
    Private btnSave As Button
    Private btnCancel As Button
    Private btnDelete As Button
    Private btnRefresh As Button
    Private btnViewDetails As Button

    Private isEditMode As Boolean = False
    Private currentInboundId As Integer = 0

    Public Sub New()
        InitializeComponent()
        LoadSuppliers()
        LoadInboundOrders()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "入库管理"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterParent

        ' 创建数据网格
        dgvInbound = New DataGridView()
        dgvInbound.Location = New Point(10, 10)
        dgvInbound.Size = New Size(800, 400)
        dgvInbound.ReadOnly = True
        dgvInbound.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvInbound.MultiSelect = False
        dgvInbound.AllowUserToAddRows = False
        dgvInbound.AllowUserToDeleteRows = False
        AddHandler dgvInbound.SelectionChanged, AddressOf dgvInbound_SelectionChanged
        Me.Controls.Add(dgvInbound)

        ' 创建输入控件
        CreateInputControls()

        ' 创建按钮
        CreateButtons()

        ' 设置初始状态
        SetFormMode(False)
    End Sub

    Private Sub CreateInputControls()
        Dim startX As Integer = 830
        Dim startY As Integer = 30
        Dim labelWidth As Integer = 80
        Dim textWidth As Integer = 200
        Dim rowHeight As Integer = 35

        ' 入库单号
        Dim lblInboundNumber As New Label()
        lblInboundNumber.Text = "入库单号:"
        lblInboundNumber.Location = New Point(startX, startY)
        lblInboundNumber.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblInboundNumber)

        txtInboundNumber = New TextBox()
        txtInboundNumber.Location = New Point(startX + labelWidth + 10, startY)
        txtInboundNumber.Size = New Size(textWidth, 25)
        txtInboundNumber.ReadOnly = True
        Me.Controls.Add(txtInboundNumber)

        ' 入库日期
        startY += rowHeight
        Dim lblInboundDate As New Label()
        lblInboundDate.Text = "入库日期:"
        lblInboundDate.Location = New Point(startX, startY)
        lblInboundDate.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblInboundDate)

        dtpInboundDate = New DateTimePicker()
        dtpInboundDate.Location = New Point(startX + labelWidth + 10, startY)
        dtpInboundDate.Size = New Size(textWidth, 25)
        dtpInboundDate.Format = DateTimePickerFormat.Short
        Me.Controls.Add(dtpInboundDate)

        ' 供应商
        startY += rowHeight
        Dim lblSupplier As New Label()
        lblSupplier.Text = "供应商:"
        lblSupplier.Location = New Point(startX, startY)
        lblSupplier.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblSupplier)

        cmbSupplier = New ComboBox()
        cmbSupplier.Location = New Point(startX + labelWidth + 10, startY)
        cmbSupplier.Size = New Size(textWidth, 25)
        cmbSupplier.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbSupplier)

        ' 入库类型
        startY += rowHeight
        Dim lblInboundType As New Label()
        lblInboundType.Text = "入库类型:"
        lblInboundType.Location = New Point(startX, startY)
        lblInboundType.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblInboundType)

        cmbInboundType = New ComboBox()
        cmbInboundType.Location = New Point(startX + labelWidth + 10, startY)
        cmbInboundType.Size = New Size(textWidth, 25)
        cmbInboundType.DropDownStyle = ComboBoxStyle.DropDownList
        cmbInboundType.Items.AddRange({"purchase", "return", "transfer", "adjustment"})
        cmbInboundType.SelectedIndex = 0
        Me.Controls.Add(cmbInboundType)

        ' 状态
        startY += rowHeight
        Dim lblStatus As New Label()
        lblStatus.Text = "状态:"
        lblStatus.Location = New Point(startX, startY)
        lblStatus.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblStatus)

        cmbStatus = New ComboBox()
        cmbStatus.Location = New Point(startX + labelWidth + 10, startY)
        cmbStatus.Size = New Size(textWidth, 25)
        cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList
        cmbStatus.Items.AddRange({"pending", "confirmed", "completed", "cancelled"})
        cmbStatus.SelectedIndex = 0
        Me.Controls.Add(cmbStatus)

        ' 总金额
        startY += rowHeight
        Dim lblTotalAmount As New Label()
        lblTotalAmount.Text = "总金额:"
        lblTotalAmount.Location = New Point(startX, startY)
        lblTotalAmount.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblTotalAmount)

        txtTotalAmount = New TextBox()
        txtTotalAmount.Location = New Point(startX + labelWidth + 10, startY)
        txtTotalAmount.Size = New Size(textWidth, 25)
        txtTotalAmount.Text = "0"
        Me.Controls.Add(txtTotalAmount)

        ' 备注
        startY += rowHeight
        Dim lblRemarks As New Label()
        lblRemarks.Text = "备注:"
        lblRemarks.Location = New Point(startX, startY)
        lblRemarks.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblRemarks)

        txtRemarks = New TextBox()
        txtRemarks.Location = New Point(startX + labelWidth + 10, startY)
        txtRemarks.Size = New Size(textWidth, 80)
        txtRemarks.Multiline = True
        Me.Controls.Add(txtRemarks)
    End Sub

    Private Sub CreateButtons()
        Dim buttonY As Integer = 450
        Dim buttonWidth As Integer = 80
        Dim buttonHeight As Integer = 30
        Dim buttonSpacing As Integer = 90

        btnNew = New Button()
        btnNew.Text = "新增"
        btnNew.Location = New Point(10, buttonY)
        btnNew.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnNew.Click, AddressOf btnNew_Click
        Me.Controls.Add(btnNew)

        btnEdit = New Button()
        btnEdit.Text = "编辑"
        btnEdit.Location = New Point(10 + buttonSpacing, buttonY)
        btnEdit.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnEdit.Click, AddressOf btnEdit_Click
        Me.Controls.Add(btnEdit)

        btnSave = New Button()
        btnSave.Text = "保存"
        btnSave.Location = New Point(10 + buttonSpacing * 2, buttonY)
        btnSave.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnSave.Click, AddressOf btnSave_Click
        Me.Controls.Add(btnSave)

        btnCancel = New Button()
        btnCancel.Text = "取消"
        btnCancel.Location = New Point(10 + buttonSpacing * 3, buttonY)
        btnCancel.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnCancel.Click, AddressOf btnCancel_Click
        Me.Controls.Add(btnCancel)

        btnDelete = New Button()
        btnDelete.Text = "删除"
        btnDelete.Location = New Point(10 + buttonSpacing * 4, buttonY)
        btnDelete.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnDelete.Click, AddressOf btnDelete_Click
        Me.Controls.Add(btnDelete)

        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(10 + buttonSpacing * 5, buttonY)
        btnRefresh.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnRefresh.Click, AddressOf btnRefresh_Click
        Me.Controls.Add(btnRefresh)

        btnViewDetails = New Button()
        btnViewDetails.Text = "查看明细"
        btnViewDetails.Location = New Point(10 + buttonSpacing * 6, buttonY)
        btnViewDetails.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnViewDetails.Click, AddressOf btnViewDetails_Click
        Me.Controls.Add(btnViewDetails)
    End Sub

    Private Sub LoadSuppliers()
        Try
            cmbSupplier.Items.Clear()
            cmbSupplier.Items.Add("")
            
            Dim sql = "SELECT id, supplier_name FROM suppliers WHERE is_active = TRUE ORDER BY supplier_name"
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            
            For Each row As DataRow In dt.Rows
                cmbSupplier.Items.Add(New ComboBoxItem(row("supplier_name").ToString(), Convert.ToInt32(row("id"))))
            Next
            
            cmbSupplier.SelectedIndex = 0
        Catch ex As Exception
            MessageBox.Show($"加载供应商数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadInboundOrders()
        Try
            Dim sql = "SELECT io.id, io.inbound_number AS '入库单号', io.inbound_date AS '入库日期', " &
                     "s.supplier_name AS '供应商', io.inbound_type AS '入库类型', " &
                     "io.status AS '状态', io.total_amount AS '总金额' " &
                     "FROM inbound_orders io " &
                     "LEFT JOIN suppliers s ON io.supplier_id = s.id " &
                     "ORDER BY io.inbound_date DESC, io.inbound_number DESC"
            
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvInbound.DataSource = dt
            
            ' 隐藏ID列
            If dgvInbound.Columns.Contains("id") Then
                dgvInbound.Columns("id").Visible = False
            End If
            
            ' 设置列宽
            dgvInbound.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        Catch ex As Exception
            MessageBox.Show($"加载入库单数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub dgvInbound_SelectionChanged(sender As Object, e As EventArgs)
        If dgvInbound.CurrentRow IsNot Nothing AndAlso Not isEditMode Then
            LoadInboundDetails()
        End If
    End Sub

    Private Sub LoadInboundDetails()
        Try
            If dgvInbound.CurrentRow Is Nothing Then Return
            
            currentInboundId = Convert.ToInt32(dgvInbound.CurrentRow.Cells("id").Value)
            
            Dim sql = "SELECT * FROM inbound_orders WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentInboundId}}
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql, parameters)
            
            If dt.Rows.Count > 0 Then
                Dim row = dt.Rows(0)
                txtInboundNumber.Text = row("inbound_number").ToString()
                dtpInboundDate.Value = Convert.ToDateTime(row("inbound_date"))
                
                ' 设置供应商
                Dim supplierId = If(IsDBNull(row("supplier_id")), 0, Convert.ToInt32(row("supplier_id")))
                For i As Integer = 0 To cmbSupplier.Items.Count - 1
                    If TypeOf cmbSupplier.Items(i) Is ComboBoxItem Then
                        Dim item = DirectCast(cmbSupplier.Items(i), ComboBoxItem)
                        If item.Value = supplierId Then
                            cmbSupplier.SelectedIndex = i
                            Exit For
                        End If
                    End If
                Next
                
                cmbInboundType.Text = row("inbound_type").ToString()
                cmbStatus.Text = row("status").ToString()
                txtTotalAmount.Text = row("total_amount").ToString()
                txtRemarks.Text = row("remarks").ToString()
            End If
        Catch ex As Exception
            MessageBox.Show($"加载入库单详情时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode

        ' 设置输入控件的启用状态
        dtpInboundDate.Enabled = editMode
        cmbSupplier.Enabled = editMode
        cmbInboundType.Enabled = editMode
        cmbStatus.Enabled = editMode
        txtTotalAmount.Enabled = editMode
        txtRemarks.Enabled = editMode

        ' 设置按钮的启用状态
        btnNew.Enabled = Not editMode
        btnEdit.Enabled = Not editMode AndAlso currentInboundId > 0
        btnDelete.Enabled = Not editMode AndAlso currentInboundId > 0
        btnSave.Enabled = editMode
        btnCancel.Enabled = editMode
        btnRefresh.Enabled = Not editMode
        btnViewDetails.Enabled = Not editMode AndAlso currentInboundId > 0

        dgvInbound.Enabled = Not editMode
    End Sub

    Private Sub ClearForm()
        txtInboundNumber.Clear()
        dtpInboundDate.Value = DateTime.Now
        cmbSupplier.SelectedIndex = 0
        cmbInboundType.SelectedIndex = 0
        cmbStatus.SelectedIndex = 0
        txtTotalAmount.Text = "0"
        txtRemarks.Clear()
        currentInboundId = 0
    End Sub

    Private Function GenerateInboundNumber() As String
        Return "IN" & DateTime.Now.ToString("yyyyMMddHHmmss")
    End Function

    ' 按钮事件处理
    Private Sub btnNew_Click(sender As Object, e As EventArgs)
        ClearForm()
        txtInboundNumber.Text = GenerateInboundNumber()
        SetFormMode(True)
        dtpInboundDate.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs)
        If currentInboundId <= 0 Then
            MessageBox.Show("请先选择要编辑的入库单", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        SetFormMode(True)
        dtpInboundDate.Focus()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs)
        If Not ValidateForm() Then Return

        Try
            Dim supplierId As Integer? = Nothing
            If cmbSupplier.SelectedIndex > 0 AndAlso TypeOf cmbSupplier.SelectedItem Is ComboBoxItem Then
                supplierId = DirectCast(cmbSupplier.SelectedItem, ComboBoxItem).Value
            End If

            If currentInboundId = 0 Then
                ' 新增
                Dim sql = "INSERT INTO inbound_orders (inbound_number, inbound_date, supplier_id, inbound_type, status, total_amount, remarks) " &
                         "VALUES (@inbound_number, @inbound_date, @supplier_id, @inbound_type, @status, @total_amount, @remarks)"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@inbound_number", txtInboundNumber.Text.Trim()},
                    {"@inbound_date", dtpInboundDate.Value.Date},
                    {"@supplier_id", If(supplierId, DBNull.Value)},
                    {"@inbound_type", cmbInboundType.Text},
                    {"@status", cmbStatus.Text},
                    {"@total_amount", Convert.ToDecimal(txtTotalAmount.Text)},
                    {"@remarks", txtRemarks.Text.Trim()}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("入库单保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' 更新
                Dim sql = "UPDATE inbound_orders SET inbound_date = @inbound_date, supplier_id = @supplier_id, " &
                         "inbound_type = @inbound_type, status = @status, total_amount = @total_amount, " &
                         "remarks = @remarks WHERE id = @id"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@inbound_date", dtpInboundDate.Value.Date},
                    {"@supplier_id", If(supplierId, DBNull.Value)},
                    {"@inbound_type", cmbInboundType.Text},
                    {"@status", cmbStatus.Text},
                    {"@total_amount", Convert.ToDecimal(txtTotalAmount.Text)},
                    {"@remarks", txtRemarks.Text.Trim()},
                    {"@id", currentInboundId}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("入库单更新成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            SetFormMode(False)
            LoadInboundOrders()
        Catch ex As Exception
            MessageBox.Show($"保存入库单时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        SetFormMode(False)
        If currentInboundId > 0 Then
            LoadInboundDetails()
        Else
            ClearForm()
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs)
        If currentInboundId <= 0 Then
            MessageBox.Show("请先选择要删除的入库单", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If MessageBox.Show("确定要删除选中的入库单吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim sql = "DELETE FROM inbound_orders WHERE id = @id"
                Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentInboundId}}

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("入库单删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ClearForm()
                LoadInboundOrders()
            Catch ex As Exception
                MessageBox.Show($"删除入库单时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs)
        LoadInboundOrders()
    End Sub

    Private Sub btnViewDetails_Click(sender As Object, e As EventArgs)
        MessageBox.Show("入库明细功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtInboundNumber.Text) Then
            MessageBox.Show("请输入入库单号", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtInboundNumber.Focus()
            Return False
        End If

        If Not IsNumeric(txtTotalAmount.Text) Then
            MessageBox.Show("请输入有效的总金额", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtTotalAmount.Focus()
            Return False
        End If

        Return True
    End Function
End Class
