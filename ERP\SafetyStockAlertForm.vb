Imports System.Windows.Forms
Imports System.Data
Imports System.Drawing

''' <summary>
''' 安全库存预警管理窗体
''' </summary>
Public Class SafetyStockAlertForm
    Inherits Form

    Private dgvAlerts As DataGridView
    Private dgvAlertHistory As DataGridView
    Private tabControl As TabControl
    Private tabCurrentAlerts As TabPage
    Private tabAlertHistory As TabPage
    Private tabSettings As TabPage

    ' 预警设置控件
    Private dgvSettings As DataGridView
    Private txtMaterialCode As TextBox
    Private txtMaterialName As TextBox
    Private txtCurrentStock As TextBox
    Private txtSafetyStock As TextBox
    Private txtNewSafetyStock As TextBox
    Private btnUpdateSafetyStock As Button
    Private btnRefreshAlerts As Button
    Private btnMarkAsHandled As Button
    Private btnExportAlerts As Button

    ' 统计信息控件
    Private lblTotalAlerts As Label
    Private lblCriticalAlerts As Label
    Private lblWarningAlerts As Label

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadData()
        ApplyPermissions()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "安全库存预警管理"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
    End Sub

    Private Sub SetupUI()
        ' 创建选项卡控件
        tabControl = New TabControl()
        tabControl.Dock = DockStyle.Fill
        Me.Controls.Add(tabControl)

        ' 当前预警选项卡
        tabCurrentAlerts = New TabPage("当前预警")
        tabControl.TabPages.Add(tabCurrentAlerts)
        SetupCurrentAlertsTab()

        ' 预警历史选项卡
        tabAlertHistory = New TabPage("预警历史")
        tabControl.TabPages.Add(tabAlertHistory)
        SetupAlertHistoryTab()

        ' 预警设置选项卡
        tabSettings = New TabPage("预警设置")
        tabControl.TabPages.Add(tabSettings)
        SetupSettingsTab()
    End Sub

    Private Sub SetupCurrentAlertsTab()
        ' 统计信息面板
        Dim pnlStats As New Panel()
        pnlStats.Height = 60
        pnlStats.Dock = DockStyle.Top
        pnlStats.BackColor = Color.LightGray
        tabCurrentAlerts.Controls.Add(pnlStats)

        lblTotalAlerts = New Label()
        lblTotalAlerts.Text = "总预警数: 0"
        lblTotalAlerts.Location = New Point(20, 20)
        lblTotalAlerts.Size = New Size(120, 20)
        lblTotalAlerts.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        pnlStats.Controls.Add(lblTotalAlerts)

        lblCriticalAlerts = New Label()
        lblCriticalAlerts.Text = "严重预警: 0"
        lblCriticalAlerts.Location = New Point(160, 20)
        lblCriticalAlerts.Size = New Size(120, 20)
        lblCriticalAlerts.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblCriticalAlerts.ForeColor = Color.Red
        pnlStats.Controls.Add(lblCriticalAlerts)

        lblWarningAlerts = New Label()
        lblWarningAlerts.Text = "一般预警: 0"
        lblWarningAlerts.Location = New Point(300, 20)
        lblWarningAlerts.Size = New Size(120, 20)
        lblWarningAlerts.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblWarningAlerts.ForeColor = Color.Orange
        pnlStats.Controls.Add(lblWarningAlerts)

        ' 按钮面板
        Dim pnlButtons As New Panel()
        pnlButtons.Height = 50
        pnlButtons.Dock = DockStyle.Top
        tabCurrentAlerts.Controls.Add(pnlButtons)

        btnRefreshAlerts = New Button()
        btnRefreshAlerts.Text = "刷新预警"
        btnRefreshAlerts.Location = New Point(20, 10)
        btnRefreshAlerts.Size = New Size(100, 30)
        pnlButtons.Controls.Add(btnRefreshAlerts)

        btnMarkAsHandled = New Button()
        btnMarkAsHandled.Text = "标记已处理"
        btnMarkAsHandled.Location = New Point(130, 10)
        btnMarkAsHandled.Size = New Size(100, 30)
        pnlButtons.Controls.Add(btnMarkAsHandled)

        btnExportAlerts = New Button()
        btnExportAlerts.Text = "导出预警"
        btnExportAlerts.Location = New Point(240, 10)
        btnExportAlerts.Size = New Size(100, 30)
        pnlButtons.Controls.Add(btnExportAlerts)

        ' 当前预警数据表格
        dgvAlerts = New DataGridView()
        dgvAlerts.Dock = DockStyle.Fill
        dgvAlerts.ReadOnly = True
        dgvAlerts.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvAlerts.MultiSelect = True
        dgvAlerts.AllowUserToAddRows = False
        dgvAlerts.AllowUserToDeleteRows = False
        dgvAlerts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabCurrentAlerts.Controls.Add(dgvAlerts)

        ' 事件处理
        AddHandler btnRefreshAlerts.Click, AddressOf BtnRefreshAlerts_Click
        AddHandler btnMarkAsHandled.Click, AddressOf BtnMarkAsHandled_Click
        AddHandler btnExportAlerts.Click, AddressOf BtnExportAlerts_Click
    End Sub

    Private Sub SetupAlertHistoryTab()
        ' 预警历史数据表格
        dgvAlertHistory = New DataGridView()
        dgvAlertHistory.Dock = DockStyle.Fill
        dgvAlertHistory.ReadOnly = True
        dgvAlertHistory.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvAlertHistory.AllowUserToAddRows = False
        dgvAlertHistory.AllowUserToDeleteRows = False
        dgvAlertHistory.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabAlertHistory.Controls.Add(dgvAlertHistory)
    End Sub

    Private Sub SetupSettingsTab()
        ' 物料信息面板
        Dim pnlMaterialInfo As New Panel()
        pnlMaterialInfo.Height = 120
        pnlMaterialInfo.Dock = DockStyle.Top
        tabSettings.Controls.Add(pnlMaterialInfo)

        ' 物料编码
        Dim lblMaterialCode As New Label()
        lblMaterialCode.Text = "物料编码:"
        lblMaterialCode.Location = New Point(20, 20)
        lblMaterialCode.Size = New Size(80, 20)
        pnlMaterialInfo.Controls.Add(lblMaterialCode)

        txtMaterialCode = New TextBox()
        txtMaterialCode.Location = New Point(110, 18)
        txtMaterialCode.Size = New Size(150, 23)
        txtMaterialCode.ReadOnly = True
        pnlMaterialInfo.Controls.Add(txtMaterialCode)

        ' 物料名称
        Dim lblMaterialName As New Label()
        lblMaterialName.Text = "物料名称:"
        lblMaterialName.Location = New Point(280, 20)
        lblMaterialName.Size = New Size(80, 20)
        pnlMaterialInfo.Controls.Add(lblMaterialName)

        txtMaterialName = New TextBox()
        txtMaterialName.Location = New Point(370, 18)
        txtMaterialName.Size = New Size(200, 23)
        txtMaterialName.ReadOnly = True
        pnlMaterialInfo.Controls.Add(txtMaterialName)

        ' 当前库存
        Dim lblCurrentStock As New Label()
        lblCurrentStock.Text = "当前库存:"
        lblCurrentStock.Location = New Point(20, 55)
        lblCurrentStock.Size = New Size(80, 20)
        pnlMaterialInfo.Controls.Add(lblCurrentStock)

        txtCurrentStock = New TextBox()
        txtCurrentStock.Location = New Point(110, 53)
        txtCurrentStock.Size = New Size(150, 23)
        txtCurrentStock.ReadOnly = True
        pnlMaterialInfo.Controls.Add(txtCurrentStock)

        ' 当前安全库存
        Dim lblSafetyStock As New Label()
        lblSafetyStock.Text = "当前安全库存:"
        lblSafetyStock.Location = New Point(280, 55)
        lblSafetyStock.Size = New Size(100, 20)
        pnlMaterialInfo.Controls.Add(lblSafetyStock)

        txtSafetyStock = New TextBox()
        txtSafetyStock.Location = New Point(390, 53)
        txtSafetyStock.Size = New Size(150, 23)
        txtSafetyStock.ReadOnly = True
        pnlMaterialInfo.Controls.Add(txtSafetyStock)

        ' 新安全库存
        Dim lblNewSafetyStock As New Label()
        lblNewSafetyStock.Text = "新安全库存:"
        lblNewSafetyStock.Location = New Point(20, 90)
        lblNewSafetyStock.Size = New Size(80, 20)
        pnlMaterialInfo.Controls.Add(lblNewSafetyStock)

        txtNewSafetyStock = New TextBox()
        txtNewSafetyStock.Location = New Point(110, 88)
        txtNewSafetyStock.Size = New Size(150, 23)
        pnlMaterialInfo.Controls.Add(txtNewSafetyStock)

        btnUpdateSafetyStock = New Button()
        btnUpdateSafetyStock.Text = "更新安全库存"
        btnUpdateSafetyStock.Location = New Point(280, 88)
        btnUpdateSafetyStock.Size = New Size(120, 25)
        pnlMaterialInfo.Controls.Add(btnUpdateSafetyStock)

        ' 安全库存设置数据表格
        dgvSettings = New DataGridView()
        dgvSettings.Dock = DockStyle.Fill
        dgvSettings.ReadOnly = True
        dgvSettings.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvSettings.AllowUserToAddRows = False
        dgvSettings.AllowUserToDeleteRows = False
        dgvSettings.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabSettings.Controls.Add(dgvSettings)

        ' 事件处理
        AddHandler dgvSettings.SelectionChanged, AddressOf DgvSettings_SelectionChanged
        AddHandler btnUpdateSafetyStock.Click, AddressOf BtnUpdateSafetyStock_Click
    End Sub

    Private Sub LoadData()
        LoadCurrentAlerts()
        LoadAlertHistory()
        LoadSafetyStockSettings()
    End Sub

    Private Sub LoadCurrentAlerts()
        Try
            Dim query As String = "
                SELECT 
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    l.location_code AS '库位编码',
                    i.current_stock AS '当前库存',
                    m.safety_stock AS '安全库存',
                    (m.safety_stock - i.current_stock) AS '缺口数量',
                    CASE 
                        WHEN i.current_stock <= m.safety_stock * 0.5 THEN '严重'
                        WHEN i.current_stock <= m.safety_stock THEN '一般'
                        ELSE '正常'
                    END AS '预警级别',
                    m.unit AS '单位',
                    i.last_updated AS '最后更新时间'
                FROM inventory i
                INNER JOIN materials m ON i.material_id = m.id
                INNER JOIN locations l ON i.location_id = l.id
                WHERE i.current_stock <= m.safety_stock 
                AND m.is_active = TRUE 
                AND l.is_active = TRUE
                ORDER BY 
                    CASE 
                        WHEN i.current_stock <= m.safety_stock * 0.5 THEN 1
                        ELSE 2
                    END,
                    (m.safety_stock - i.current_stock) DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvAlerts.DataSource = dt

            ' 设置行颜色
            For Each row As DataGridViewRow In dgvAlerts.Rows
                If row.Cells("预警级别").Value?.ToString() = "严重" Then
                    row.DefaultCellStyle.BackColor = Color.LightPink
                    row.DefaultCellStyle.ForeColor = Color.DarkRed
                ElseIf row.Cells("预警级别").Value?.ToString() = "一般" Then
                    row.DefaultCellStyle.BackColor = Color.LightYellow
                    row.DefaultCellStyle.ForeColor = Color.DarkOrange
                End If
            Next

            ' 更新统计信息
            UpdateAlertStatistics(dt)

        Catch ex As Exception
            MessageBox.Show($"加载预警数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateAlertStatistics(dt As DataTable)
        Dim totalAlerts As Integer = dt.Rows.Count
        Dim criticalAlerts As Integer = dt.Select("预警级别 = '严重'").Length
        Dim warningAlerts As Integer = dt.Select("预警级别 = '一般'").Length

        lblTotalAlerts.Text = $"总预警数: {totalAlerts}"
        lblCriticalAlerts.Text = $"严重预警: {criticalAlerts}"
        lblWarningAlerts.Text = $"一般预警: {warningAlerts}"
    End Sub

    Private Sub LoadAlertHistory()
        Try
            ' 这里可以加载预警处理历史记录
            ' 暂时显示空表格，后续可以扩展
            Dim dt As New DataTable()
            dt.Columns.Add("处理时间", GetType(DateTime))
            dt.Columns.Add("物料编码", GetType(String))
            dt.Columns.Add("物料名称", GetType(String))
            dt.Columns.Add("库位编码", GetType(String))
            dt.Columns.Add("预警级别", GetType(String))
            dt.Columns.Add("处理方式", GetType(String))
            dt.Columns.Add("处理人", GetType(String))
            dt.Columns.Add("备注", GetType(String))

            dgvAlertHistory.DataSource = dt

        Catch ex As Exception
            MessageBox.Show($"加载预警历史失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadSafetyStockSettings()
        Try
            Dim query As String = "
                SELECT
                    m.id,
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    m.safety_stock AS '安全库存',
                    m.unit AS '单位',
                    COALESCE(SUM(i.current_stock), 0) AS '总库存',
                    m.min_order_quantity AS '最小订购量',
                    m.updated_at AS '最后更新时间'
                FROM materials m
                LEFT JOIN inventory i ON m.id = i.material_id
                WHERE m.is_active = TRUE
                GROUP BY m.id, m.material_code, m.material_name, m.safety_stock, m.unit, m.min_order_quantity, m.updated_at
                ORDER BY m.material_code"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvSettings.DataSource = dt

            ' 隐藏ID列
            If dgvSettings.Columns.Contains("id") Then
                dgvSettings.Columns("id").Visible = False
            End If

        Catch ex As Exception
            MessageBox.Show($"加载安全库存设置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub DgvSettings_SelectionChanged(sender As Object, e As EventArgs)
        If dgvSettings.SelectedRows.Count > 0 Then
            Dim row As DataGridViewRow = dgvSettings.SelectedRows(0)
            txtMaterialCode.Text = row.Cells("物料编码").Value?.ToString()
            txtMaterialName.Text = row.Cells("物料名称").Value?.ToString()
            txtCurrentStock.Text = row.Cells("总库存").Value?.ToString()
            txtSafetyStock.Text = row.Cells("安全库存").Value?.ToString()
            txtNewSafetyStock.Text = row.Cells("安全库存").Value?.ToString()
        End If
    End Sub

    Private Sub BtnRefreshAlerts_Click(sender As Object, e As EventArgs)
        LoadCurrentAlerts()
    End Sub

    Private Sub BtnMarkAsHandled_Click(sender As Object, e As EventArgs)
        If dgvAlerts.SelectedRows.Count = 0 Then
            MessageBox.Show("请选择要标记为已处理的预警记录！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' 这里可以实现标记预警为已处理的逻辑
        ' 可以记录到预警处理历史表中
        MessageBox.Show("预警标记功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BtnExportAlerts_Click(sender As Object, e As EventArgs)
        ' 这里可以实现导出预警数据的功能
        MessageBox.Show("预警导出功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BtnUpdateSafetyStock_Click(sender As Object, e As EventArgs)
        If dgvSettings.SelectedRows.Count = 0 Then
            MessageBox.Show("请先选择要更新的物料！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        If String.IsNullOrWhiteSpace(txtNewSafetyStock.Text) Then
            MessageBox.Show("请输入新的安全库存数量！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Dim newSafetyStock As Decimal
        If Not Decimal.TryParse(txtNewSafetyStock.Text, newSafetyStock) OrElse newSafetyStock < 0 Then
            MessageBox.Show("请输入有效的安全库存数量！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Try
            Dim materialId As Integer = Convert.ToInt32(dgvSettings.SelectedRows(0).Cells("id").Value)
            Dim parameters As New Dictionary(Of String, Object) From {
                {"id", materialId},
                {"safety_stock", newSafetyStock}
            }

            Dim query As String = "UPDATE materials SET safety_stock = @safety_stock WHERE id = @id"
            DatabaseManager.Instance.ExecuteNonQuery(query, parameters)

            MessageBox.Show("安全库存更新成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadSafetyStockSettings()
            LoadCurrentAlerts()

        Catch ex As Exception
            MessageBox.Show($"更新安全库存失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ApplyPermissions()
        ' 检查基本查看权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewInventory) Then
            MessageBox.Show("您没有权限访问安全库存预警功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 根据权限设置按钮和控件状态
        Dim canManageInventory As Boolean = PermissionManager.HasPermission(PermissionManager.Permission.ManageInventoryAdjustment)
        Dim canManageMaterials As Boolean = PermissionManager.HasPermission(PermissionManager.Permission.ManageMaterials)

        btnMarkAsHandled.Enabled = canManageInventory
        btnUpdateSafetyStock.Enabled = canManageMaterials
        txtNewSafetyStock.ReadOnly = Not canManageMaterials

        ' 如果没有管理权限，隐藏设置选项卡
        If Not canManageMaterials Then
            tabControl.TabPages.Remove(tabSettings)
        End If
    End Sub
End Class
