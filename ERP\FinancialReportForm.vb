Imports System.Windows.Forms
Imports System.Data
Imports System.Drawing

''' <summary>
''' 财务报表窗体
''' </summary>
Public Class FinancialReportForm
    Inherits Form

    Private tabControl As TabControl
    Private tabARSummary As TabPage
    Private tabAPSummary As TabPage
    Private tabCashFlow As TabPage
    Private tabProfitLoss As TabPage
    Private tabAging As TabPage

    ' 应收账款汇总控件
    Private dgvARSummary As DataGridView
    Private lblTotalAR As Label
    Private lblOverdueAR As Label
    Private lblCurrentAR As Label

    ' 应付账款汇总控件
    Private dgvAPSummary As DataGridView
    Private lblTotalAP As Label
    Private lblOverdueAP As Label
    Private lblCurrentAP As Label

    ' 现金流控件
    Private dgvCashFlow As DataGridView
    Private dtpCashFlowFrom As DateTimePicker
    Private dtpCashFlowTo As DateTimePicker
    Private btnGenerateCashFlow As Button
    Private lblCashInflow As Label
    Private lblCashOutflow As Label
    Private lblNetCashFlow As Label

    ' 损益表控件
    Private dgvProfitLoss As DataGridView
    Private dtpPLFrom As DateTimePicker
    Private dtpPLTo As DateTimePicker
    Private btnGeneratePL As Button
    Private lblTotalRevenue As Label
    Private lblTotalCost As Label
    Private lblGrossProfit As Label
    Private lblNetProfit As Label

    ' 账龄分析控件
    Private dgvAging As DataGridView
    Private cmbAgingType As ComboBox
    Private btnGenerateAging As Button

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadData()
        ApplyPermissions()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "财务报表"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
    End Sub

    Private Sub SetupUI()
        ' 创建选项卡控件
        tabControl = New TabControl()
        tabControl.Dock = DockStyle.Fill
        Me.Controls.Add(tabControl)

        ' 应收账款汇总选项卡
        tabARSummary = New TabPage("应收账款汇总")
        tabControl.TabPages.Add(tabARSummary)
        SetupARSummaryTab()

        ' 应付账款汇总选项卡
        tabAPSummary = New TabPage("应付账款汇总")
        tabControl.TabPages.Add(tabAPSummary)
        SetupAPSummaryTab()

        ' 现金流选项卡
        tabCashFlow = New TabPage("现金流报表")
        tabControl.TabPages.Add(tabCashFlow)
        SetupCashFlowTab()

        ' 损益表选项卡
        tabProfitLoss = New TabPage("损益表")
        tabControl.TabPages.Add(tabProfitLoss)
        SetupProfitLossTab()

        ' 账龄分析选项卡
        tabAging = New TabPage("账龄分析")
        tabControl.TabPages.Add(tabAging)
        SetupAgingTab()
    End Sub

    Private Sub SetupARSummaryTab()
        ' 统计信息面板
        Dim pnlARStats As New Panel()
        pnlARStats.Height = 80
        pnlARStats.Dock = DockStyle.Top
        pnlARStats.BackColor = Color.LightBlue
        tabARSummary.Controls.Add(pnlARStats)

        lblTotalAR = New Label()
        lblTotalAR.Text = "应收账款总额: ¥0.00"
        lblTotalAR.Location = New Point(20, 20)
        lblTotalAR.Size = New Size(200, 20)
        lblTotalAR.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        pnlARStats.Controls.Add(lblTotalAR)

        lblOverdueAR = New Label()
        lblOverdueAR.Text = "逾期应收: ¥0.00"
        lblOverdueAR.Location = New Point(240, 20)
        lblOverdueAR.Size = New Size(200, 20)
        lblOverdueAR.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblOverdueAR.ForeColor = Color.Red
        pnlARStats.Controls.Add(lblOverdueAR)

        lblCurrentAR = New Label()
        lblCurrentAR.Text = "当期应收: ¥0.00"
        lblCurrentAR.Location = New Point(460, 20)
        lblCurrentAR.Size = New Size(200, 20)
        lblCurrentAR.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblCurrentAR.ForeColor = Color.Green
        pnlARStats.Controls.Add(lblCurrentAR)

        ' 应收账款明细表格
        dgvARSummary = New DataGridView()
        dgvARSummary.Dock = DockStyle.Fill
        dgvARSummary.ReadOnly = True
        dgvARSummary.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvARSummary.AllowUserToAddRows = False
        dgvARSummary.AllowUserToDeleteRows = False
        dgvARSummary.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabARSummary.Controls.Add(dgvARSummary)
    End Sub

    Private Sub SetupAPSummaryTab()
        ' 统计信息面板
        Dim pnlAPStats As New Panel()
        pnlAPStats.Height = 80
        pnlAPStats.Dock = DockStyle.Top
        pnlAPStats.BackColor = Color.LightCoral
        tabAPSummary.Controls.Add(pnlAPStats)

        lblTotalAP = New Label()
        lblTotalAP.Text = "应付账款总额: ¥0.00"
        lblTotalAP.Location = New Point(20, 20)
        lblTotalAP.Size = New Size(200, 20)
        lblTotalAP.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        pnlAPStats.Controls.Add(lblTotalAP)

        lblOverdueAP = New Label()
        lblOverdueAP.Text = "逾期应付: ¥0.00"
        lblOverdueAP.Location = New Point(240, 20)
        lblOverdueAP.Size = New Size(200, 20)
        lblOverdueAP.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblOverdueAP.ForeColor = Color.Red
        pnlAPStats.Controls.Add(lblOverdueAP)

        lblCurrentAP = New Label()
        lblCurrentAP.Text = "当期应付: ¥0.00"
        lblCurrentAP.Location = New Point(460, 20)
        lblCurrentAP.Size = New Size(200, 20)
        lblCurrentAP.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblCurrentAP.ForeColor = Color.Green
        pnlAPStats.Controls.Add(lblCurrentAP)

        ' 应付账款明细表格
        dgvAPSummary = New DataGridView()
        dgvAPSummary.Dock = DockStyle.Fill
        dgvAPSummary.ReadOnly = True
        dgvAPSummary.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvAPSummary.AllowUserToAddRows = False
        dgvAPSummary.AllowUserToDeleteRows = False
        dgvAPSummary.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabAPSummary.Controls.Add(dgvAPSummary)
    End Sub

    Private Sub SetupCashFlowTab()
        ' 参数面板
        Dim pnlCashFlowParams As New Panel()
        pnlCashFlowParams.Height = 50
        pnlCashFlowParams.Dock = DockStyle.Top
        tabCashFlow.Controls.Add(pnlCashFlowParams)

        Dim lblFromDate As New Label()
        lblFromDate.Text = "开始日期:"
        lblFromDate.Location = New Point(20, 15)
        lblFromDate.Size = New Size(70, 20)
        pnlCashFlowParams.Controls.Add(lblFromDate)

        dtpCashFlowFrom = New DateTimePicker()
        dtpCashFlowFrom.Location = New Point(100, 13)
        dtpCashFlowFrom.Size = New Size(120, 23)
        dtpCashFlowFrom.Format = DateTimePickerFormat.Short
        dtpCashFlowFrom.Value = DateTime.Now.AddMonths(-1)
        pnlCashFlowParams.Controls.Add(dtpCashFlowFrom)

        Dim lblToDate As New Label()
        lblToDate.Text = "结束日期:"
        lblToDate.Location = New Point(240, 15)
        lblToDate.Size = New Size(70, 20)
        pnlCashFlowParams.Controls.Add(lblToDate)

        dtpCashFlowTo = New DateTimePicker()
        dtpCashFlowTo.Location = New Point(320, 13)
        dtpCashFlowTo.Size = New Size(120, 23)
        dtpCashFlowTo.Format = DateTimePickerFormat.Short
        pnlCashFlowParams.Controls.Add(dtpCashFlowTo)

        btnGenerateCashFlow = New Button()
        btnGenerateCashFlow.Text = "生成报表"
        btnGenerateCashFlow.Location = New Point(460, 13)
        btnGenerateCashFlow.Size = New Size(100, 25)
        pnlCashFlowParams.Controls.Add(btnGenerateCashFlow)

        ' 统计信息面板
        Dim pnlCashFlowStats As New Panel()
        pnlCashFlowStats.Height = 80
        pnlCashFlowStats.Dock = DockStyle.Top
        pnlCashFlowStats.BackColor = Color.LightGreen
        tabCashFlow.Controls.Add(pnlCashFlowStats)

        lblCashInflow = New Label()
        lblCashInflow.Text = "现金流入: ¥0.00"
        lblCashInflow.Location = New Point(20, 20)
        lblCashInflow.Size = New Size(200, 20)
        lblCashInflow.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblCashInflow.ForeColor = Color.Green
        pnlCashFlowStats.Controls.Add(lblCashInflow)

        lblCashOutflow = New Label()
        lblCashOutflow.Text = "现金流出: ¥0.00"
        lblCashOutflow.Location = New Point(240, 20)
        lblCashOutflow.Size = New Size(200, 20)
        lblCashOutflow.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblCashOutflow.ForeColor = Color.Red
        pnlCashFlowStats.Controls.Add(lblCashOutflow)

        lblNetCashFlow = New Label()
        lblNetCashFlow.Text = "净现金流: ¥0.00"
        lblNetCashFlow.Location = New Point(460, 20)
        lblNetCashFlow.Size = New Size(200, 20)
        lblNetCashFlow.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblNetCashFlow.ForeColor = Color.Blue
        pnlCashFlowStats.Controls.Add(lblNetCashFlow)

        ' 现金流明细表格
        dgvCashFlow = New DataGridView()
        dgvCashFlow.Dock = DockStyle.Fill
        dgvCashFlow.ReadOnly = True
        dgvCashFlow.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvCashFlow.AllowUserToAddRows = False
        dgvCashFlow.AllowUserToDeleteRows = False
        dgvCashFlow.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabCashFlow.Controls.Add(dgvCashFlow)

        ' 事件处理
        AddHandler btnGenerateCashFlow.Click, AddressOf BtnGenerateCashFlow_Click
    End Sub

    Private Sub SetupProfitLossTab()
        ' 参数面板
        Dim pnlPLParams As New Panel()
        pnlPLParams.Height = 50
        pnlPLParams.Dock = DockStyle.Top
        tabProfitLoss.Controls.Add(pnlPLParams)

        Dim lblFromDate As New Label()
        lblFromDate.Text = "开始日期:"
        lblFromDate.Location = New Point(20, 15)
        lblFromDate.Size = New Size(70, 20)
        pnlPLParams.Controls.Add(lblFromDate)

        dtpPLFrom = New DateTimePicker()
        dtpPLFrom.Location = New Point(100, 13)
        dtpPLFrom.Size = New Size(120, 23)
        dtpPLFrom.Format = DateTimePickerFormat.Short
        dtpPLFrom.Value = DateTime.Now.AddMonths(-1)
        pnlPLParams.Controls.Add(dtpPLFrom)

        Dim lblToDate As New Label()
        lblToDate.Text = "结束日期:"
        lblToDate.Location = New Point(240, 15)
        lblToDate.Size = New Size(70, 20)
        pnlPLParams.Controls.Add(lblToDate)

        dtpPLTo = New DateTimePicker()
        dtpPLTo.Location = New Point(320, 13)
        dtpPLTo.Size = New Size(120, 23)
        dtpPLTo.Format = DateTimePickerFormat.Short
        pnlPLParams.Controls.Add(dtpPLTo)

        btnGeneratePL = New Button()
        btnGeneratePL.Text = "生成损益表"
        btnGeneratePL.Location = New Point(460, 13)
        btnGeneratePL.Size = New Size(100, 25)
        pnlPLParams.Controls.Add(btnGeneratePL)

        ' 统计信息面板
        Dim pnlPLStats As New Panel()
        pnlPLStats.Height = 120
        pnlPLStats.Dock = DockStyle.Top
        pnlPLStats.BackColor = Color.LightYellow
        tabProfitLoss.Controls.Add(pnlPLStats)

        lblTotalRevenue = New Label()
        lblTotalRevenue.Text = "总收入: ¥0.00"
        lblTotalRevenue.Location = New Point(20, 20)
        lblTotalRevenue.Size = New Size(200, 20)
        lblTotalRevenue.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblTotalRevenue.ForeColor = Color.Green
        pnlPLStats.Controls.Add(lblTotalRevenue)

        lblTotalCost = New Label()
        lblTotalCost.Text = "总成本: ¥0.00"
        lblTotalCost.Location = New Point(240, 20)
        lblTotalCost.Size = New Size(200, 20)
        lblTotalCost.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblTotalCost.ForeColor = Color.Red
        pnlPLStats.Controls.Add(lblTotalCost)

        lblGrossProfit = New Label()
        lblGrossProfit.Text = "毛利润: ¥0.00"
        lblGrossProfit.Location = New Point(20, 50)
        lblGrossProfit.Size = New Size(200, 20)
        lblGrossProfit.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblGrossProfit.ForeColor = Color.Blue
        pnlPLStats.Controls.Add(lblGrossProfit)

        lblNetProfit = New Label()
        lblNetProfit.Text = "净利润: ¥0.00"
        lblNetProfit.Location = New Point(240, 50)
        lblNetProfit.Size = New Size(200, 20)
        lblNetProfit.Font = New Font("微软雅黑", 12, FontStyle.Bold)
        lblNetProfit.ForeColor = Color.DarkBlue
        pnlPLStats.Controls.Add(lblNetProfit)

        ' 损益明细表格
        dgvProfitLoss = New DataGridView()
        dgvProfitLoss.Dock = DockStyle.Fill
        dgvProfitLoss.ReadOnly = True
        dgvProfitLoss.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvProfitLoss.AllowUserToAddRows = False
        dgvProfitLoss.AllowUserToDeleteRows = False
        dgvProfitLoss.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabProfitLoss.Controls.Add(dgvProfitLoss)

        ' 事件处理
        AddHandler btnGeneratePL.Click, AddressOf BtnGeneratePL_Click
    End Sub

    Private Sub SetupAgingTab()
        ' 参数面板
        Dim pnlAgingParams As New Panel()
        pnlAgingParams.Height = 50
        pnlAgingParams.Dock = DockStyle.Top
        tabAging.Controls.Add(pnlAgingParams)

        Dim lblAgingType As New Label()
        lblAgingType.Text = "账龄类型:"
        lblAgingType.Location = New Point(20, 15)
        lblAgingType.Size = New Size(70, 20)
        pnlAgingParams.Controls.Add(lblAgingType)

        cmbAgingType = New ComboBox()
        cmbAgingType.Location = New Point(100, 13)
        cmbAgingType.Size = New Size(120, 23)
        cmbAgingType.DropDownStyle = ComboBoxStyle.DropDownList
        cmbAgingType.Items.AddRange({"应收账款账龄", "应付账款账龄"})
        cmbAgingType.SelectedIndex = 0
        pnlAgingParams.Controls.Add(cmbAgingType)

        btnGenerateAging = New Button()
        btnGenerateAging.Text = "生成账龄分析"
        btnGenerateAging.Location = New Point(240, 13)
        btnGenerateAging.Size = New Size(120, 25)
        pnlAgingParams.Controls.Add(btnGenerateAging)

        ' 账龄分析表格
        dgvAging = New DataGridView()
        dgvAging.Dock = DockStyle.Fill
        dgvAging.ReadOnly = True
        dgvAging.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvAging.AllowUserToAddRows = False
        dgvAging.AllowUserToDeleteRows = False
        dgvAging.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabAging.Controls.Add(dgvAging)

        ' 事件处理
        AddHandler btnGenerateAging.Click, AddressOf BtnGenerateAging_Click
    End Sub

    Private Sub LoadData()
        LoadARSummary()
        LoadAPSummary()
        GenerateCashFlowReport()
        GenerateProfitLossReport()
        GenerateAgingReport()
    End Sub

    Private Sub LoadARSummary()
        Try
            Dim query As String = "
                SELECT
                    ar.ar_number AS '应收单号',
                    c.customer_name AS '客户名称',
                    ar.invoice_number AS '发票号',
                    ar.invoice_date AS '发票日期',
                    ar.due_date AS '到期日期',
                    ar.original_amount AS '原始金额',
                    ar.paid_amount AS '已付金额',
                    ar.outstanding_amount AS '未付金额',
                    CASE ar.status
                        WHEN 'pending' THEN '待付款'
                        WHEN 'partial' THEN '部分付款'
                        WHEN 'paid' THEN '已付款'
                        WHEN 'overdue' THEN '逾期'
                        ELSE ar.status
                    END AS '状态',
                    DATEDIFF(CURDATE(), ar.due_date) AS '逾期天数',
                    ar.currency AS '币种'
                FROM accounts_receivable ar
                LEFT JOIN customers c ON ar.customer_id = c.id
                WHERE ar.outstanding_amount > 0
                ORDER BY ar.due_date ASC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvARSummary.DataSource = dt

            ' 设置数字列格式
            For Each column As DataGridViewColumn In dgvARSummary.Columns
                If column.Name.Contains("金额") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                End If
            Next

            ' 设置行颜色
            For Each row As DataGridViewRow In dgvARSummary.Rows
                Dim overdueDays As Integer = Convert.ToInt32(row.Cells("逾期天数").Value)
                If overdueDays > 0 Then
                    row.DefaultCellStyle.BackColor = Color.LightPink
                ElseIf overdueDays > -7 Then
                    row.DefaultCellStyle.BackColor = Color.LightYellow
                End If
            Next

            ' 更新统计信息
            UpdateARStatistics(dt)

        Catch ex As Exception
            MessageBox.Show($"加载应收账款汇总失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateARStatistics(dt As DataTable)
        Try
            Dim totalAR As Decimal = 0
            Dim overdueAR As Decimal = 0
            Dim currentAR As Decimal = 0

            For Each row As DataRow In dt.Rows
                Dim amount As Decimal = Convert.ToDecimal(row("未付金额"))
                Dim overdueDays As Integer = Convert.ToInt32(row("逾期天数"))

                totalAR += amount
                If overdueDays > 0 Then
                    overdueAR += amount
                Else
                    currentAR += amount
                End If
            Next

            lblTotalAR.Text = $"应收账款总额: ¥{totalAR:N2}"
            lblOverdueAR.Text = $"逾期应收: ¥{overdueAR:N2}"
            lblCurrentAR.Text = $"当期应收: ¥{currentAR:N2}"

        Catch ex As Exception
            MessageBox.Show($"更新应收统计失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadAPSummary()
        Try
            Dim query As String = "
                SELECT
                    ap.ap_number AS '应付单号',
                    s.supplier_name AS '供应商名称',
                    ap.invoice_number AS '发票号',
                    ap.invoice_date AS '发票日期',
                    ap.due_date AS '到期日期',
                    ap.original_amount AS '原始金额',
                    ap.paid_amount AS '已付金额',
                    ap.outstanding_amount AS '未付金额',
                    CASE ap.status
                        WHEN 'pending' THEN '待付款'
                        WHEN 'partial' THEN '部分付款'
                        WHEN 'paid' THEN '已付款'
                        WHEN 'overdue' THEN '逾期'
                        ELSE ap.status
                    END AS '状态',
                    DATEDIFF(CURDATE(), ap.due_date) AS '逾期天数',
                    ap.currency AS '币种'
                FROM accounts_payable ap
                LEFT JOIN suppliers s ON ap.supplier_id = s.id
                WHERE ap.outstanding_amount > 0
                ORDER BY ap.due_date ASC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvAPSummary.DataSource = dt

            ' 设置数字列格式
            For Each column As DataGridViewColumn In dgvAPSummary.Columns
                If column.Name.Contains("金额") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                End If
            Next

            ' 设置行颜色
            For Each row As DataGridViewRow In dgvAPSummary.Rows
                Dim overdueDays As Integer = Convert.ToInt32(row.Cells("逾期天数").Value)
                If overdueDays > 0 Then
                    row.DefaultCellStyle.BackColor = Color.LightPink
                ElseIf overdueDays > -7 Then
                    row.DefaultCellStyle.BackColor = Color.LightYellow
                End If
            Next

            ' 更新统计信息
            UpdateAPStatistics(dt)

        Catch ex As Exception
            MessageBox.Show($"加载应付账款汇总失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateAPStatistics(dt As DataTable)
        Try
            Dim totalAP As Decimal = 0
            Dim overdueAP As Decimal = 0
            Dim currentAP As Decimal = 0

            For Each row As DataRow In dt.Rows
                Dim amount As Decimal = Convert.ToDecimal(row("未付金额"))
                Dim overdueDays As Integer = Convert.ToInt32(row("逾期天数"))

                totalAP += amount
                If overdueDays > 0 Then
                    overdueAP += amount
                Else
                    currentAP += amount
                End If
            Next

            lblTotalAP.Text = $"应付账款总额: ¥{totalAP:N2}"
            lblOverdueAP.Text = $"逾期应付: ¥{overdueAP:N2}"
            lblCurrentAP.Text = $"当期应付: ¥{currentAP:N2}"

        Catch ex As Exception
            MessageBox.Show($"更新应付统计失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnGenerateCashFlow_Click(sender As Object, e As EventArgs)
        GenerateCashFlowReport()
    End Sub

    Private Sub GenerateCashFlowReport()
        Try
            Dim query As String = "
                SELECT
                    '应收款收款' AS '现金流类型',
                    pr.payment_date AS '日期',
                    c.customer_name AS '相关方',
                    pr.payment_amount AS '金额',
                    'inflow' AS '流向',
                    pr.payment_method AS '支付方式',
                    pr.description AS '描述'
                FROM payment_records pr
                LEFT JOIN accounts_receivable ar ON pr.reference_id = ar.id AND pr.payment_type = 'receivable'
                LEFT JOIN customers c ON ar.customer_id = c.id
                WHERE pr.payment_date BETWEEN @from_date AND @to_date
                AND pr.payment_type = 'receivable'

                UNION ALL

                SELECT
                    '应付款付款' AS '现金流类型',
                    pr.payment_date AS '日期',
                    s.supplier_name AS '相关方',
                    pr.payment_amount AS '金额',
                    'outflow' AS '流向',
                    pr.payment_method AS '支付方式',
                    pr.description AS '描述'
                FROM payment_records pr
                LEFT JOIN accounts_payable ap ON pr.reference_id = ap.id AND pr.payment_type = 'payable'
                LEFT JOIN suppliers s ON ap.supplier_id = s.id
                WHERE pr.payment_date BETWEEN @from_date AND @to_date
                AND pr.payment_type = 'payable'

                ORDER BY 日期 DESC"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"from_date", dtpCashFlowFrom.Value.Date},
                {"to_date", dtpCashFlowTo.Value.Date.AddDays(1).AddSeconds(-1)}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvCashFlow.DataSource = dt

            ' 计算现金流统计
            Dim cashInflow As Decimal = 0
            Dim cashOutflow As Decimal = 0

            For Each row As DataRow In dt.Rows
                Dim amount As Decimal = Convert.ToDecimal(row("金额"))
                If row("流向").ToString() = "inflow" Then
                    cashInflow += amount
                Else
                    cashOutflow += amount
                End If
            Next

            Dim netCashFlow As Decimal = cashInflow - cashOutflow

            lblCashInflow.Text = $"现金流入: ¥{cashInflow:N2}"
            lblCashOutflow.Text = $"现金流出: ¥{cashOutflow:N2}"
            lblNetCashFlow.Text = $"净现金流: ¥{netCashFlow:N2}"

            ' 设置净现金流颜色
            If netCashFlow > 0 Then
                lblNetCashFlow.ForeColor = Color.Green
            ElseIf netCashFlow < 0 Then
                lblNetCashFlow.ForeColor = Color.Red
            Else
                lblNetCashFlow.ForeColor = Color.Blue
            End If

        Catch ex As Exception
            MessageBox.Show($"生成现金流报表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnGeneratePL_Click(sender As Object, e As EventArgs)
        GenerateProfitLossReport()
    End Sub

    Private Sub GenerateProfitLossReport()
        Try
            ' 这里可以根据实际业务需求生成损益表
            ' 目前基于订单数据生成简化的损益表
            Dim query As String = "
                SELECT
                    '销售收入' AS '项目',
                    SUM(od.invoiced_amount) AS '金额',
                    'revenue' AS '类型'
                FROM order_details od
                INNER JOIN orders o ON od.order_id = o.id
                WHERE o.order_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')

                UNION ALL

                SELECT
                    '销售成本' AS '项目',
                    SUM(od.invoiced_quantity * m.standard_price * 0.7) AS '金额',  -- 假设成本为标准价格的70%
                    'cost' AS '类型'
                FROM order_details od
                INNER JOIN orders o ON od.order_id = o.id
                INNER JOIN materials m ON od.material_id = m.id
                WHERE o.order_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')

                UNION ALL

                SELECT
                    '管理费用' AS '项目',
                    SUM(od.invoiced_amount) * 0.1 AS '金额',  -- 假设管理费用为收入的10%
                    'expense' AS '类型'
                FROM order_details od
                INNER JOIN orders o ON od.order_id = o.id
                WHERE o.order_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')

                ORDER BY 类型 DESC, 项目"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"from_date", dtpPLFrom.Value.Date},
                {"to_date", dtpPLTo.Value.Date.AddDays(1).AddSeconds(-1)}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvProfitLoss.DataSource = dt

            ' 计算损益统计
            Dim totalRevenue As Decimal = 0
            Dim totalCost As Decimal = 0
            Dim totalExpense As Decimal = 0

            For Each row As DataRow In dt.Rows
                Dim amount As Decimal = If(IsDBNull(row("金额")), 0, Convert.ToDecimal(row("金额")))
                Select Case row("类型").ToString()
                    Case "revenue"
                        totalRevenue += amount
                    Case "cost"
                        totalCost += amount
                    Case "expense"
                        totalExpense += amount
                End Select
            Next

            Dim grossProfit As Decimal = totalRevenue - totalCost
            Dim netProfit As Decimal = grossProfit - totalExpense

            lblTotalRevenue.Text = $"总收入: ¥{totalRevenue:N2}"
            lblTotalCost.Text = $"总成本: ¥{totalCost:N2}"
            lblGrossProfit.Text = $"毛利润: ¥{grossProfit:N2}"
            lblNetProfit.Text = $"净利润: ¥{netProfit:N2}"

            ' 设置净利润颜色
            If netProfit > 0 Then
                lblNetProfit.ForeColor = Color.Green
            ElseIf netProfit < 0 Then
                lblNetProfit.ForeColor = Color.Red
            Else
                lblNetProfit.ForeColor = Color.Blue
            End If

        Catch ex As Exception
            MessageBox.Show($"生成损益表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnGenerateAging_Click(sender As Object, e As EventArgs)
        GenerateAgingReport()
    End Sub

    Private Sub GenerateAgingReport()
        Try
            Dim query As String = ""

            If cmbAgingType.SelectedIndex = 0 Then
                ' 应收账款账龄分析
                query = "
                    SELECT
                        c.customer_name AS '客户名称',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ar.due_date) <= 0 THEN ar.outstanding_amount ELSE 0 END) AS '未到期',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ar.due_date) BETWEEN 1 AND 30 THEN ar.outstanding_amount ELSE 0 END) AS '1-30天',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ar.due_date) BETWEEN 31 AND 60 THEN ar.outstanding_amount ELSE 0 END) AS '31-60天',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ar.due_date) BETWEEN 61 AND 90 THEN ar.outstanding_amount ELSE 0 END) AS '61-90天',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ar.due_date) > 90 THEN ar.outstanding_amount ELSE 0 END) AS '90天以上',
                        SUM(ar.outstanding_amount) AS '合计'
                    FROM accounts_receivable ar
                    LEFT JOIN customers c ON ar.customer_id = c.id
                    WHERE ar.outstanding_amount > 0
                    GROUP BY c.id, c.customer_name
                    ORDER BY SUM(ar.outstanding_amount) DESC"
            Else
                ' 应付账款账龄分析
                query = "
                    SELECT
                        s.supplier_name AS '供应商名称',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ap.due_date) <= 0 THEN ap.outstanding_amount ELSE 0 END) AS '未到期',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ap.due_date) BETWEEN 1 AND 30 THEN ap.outstanding_amount ELSE 0 END) AS '1-30天',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ap.due_date) BETWEEN 31 AND 60 THEN ap.outstanding_amount ELSE 0 END) AS '31-60天',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ap.due_date) BETWEEN 61 AND 90 THEN ap.outstanding_amount ELSE 0 END) AS '61-90天',
                        SUM(CASE WHEN DATEDIFF(CURDATE(), ap.due_date) > 90 THEN ap.outstanding_amount ELSE 0 END) AS '90天以上',
                        SUM(ap.outstanding_amount) AS '合计'
                    FROM accounts_payable ap
                    LEFT JOIN suppliers s ON ap.supplier_id = s.id
                    WHERE ap.outstanding_amount > 0
                    GROUP BY s.id, s.supplier_name
                    ORDER BY SUM(ap.outstanding_amount) DESC"
            End If

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvAging.DataSource = dt

            ' 设置数字列格式
            For Each column As DataGridViewColumn In dgvAging.Columns
                If column.Name <> "客户名称" AndAlso column.Name <> "供应商名称" Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                End If
            Next

        Catch ex As Exception
            MessageBox.Show($"生成账龄分析失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ApplyPermissions()
        ' 检查财务查看权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewFinancial) Then
            MessageBox.Show("您没有权限访问财务报表功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 财务权限用户可以访问所有财务报表功能
    End Sub
End Class
