# ERP系统开发 Todo List

## 1. 基础数据管理
- [ ] 完善客户管理模块（CustomerForm）
- [ ] 完善供应商管理模块（SupplierForm）
- [ ] 完善物料管理模块（MaterialForm）
- [ ] 完善库位管理模块（LocationForm）
- [ ] 用户管理模块权限细化与实现（UserForm）

## 2. 库存管理
- [ ] 实现库存查询功能（InventoryForm）
- [ ] 实现入库管理功能（InboundForm）
- [ ] 实现出库管理功能（OutboundForm）
- [ ] 实现库存盘点功能（StockCountForm）
- [ ] 实现安全库存预警功能（SafetyStockAlertForm）

## 3. 订单管理
- [ ] 实现订单查询功能（OrderForm）
- [ ] 实现订单分析功能（OrderAnalysisForm）

## 4. 财务管理
- [ ] 实现应收账款管理（AccountsReceivableForm）
- [ ] 实现应付账款管理（AccountsPayableForm）
- [ ] 实现收付款记录功能（PaymentRecordsForm，当前为“开发中”）
- [ ] 实现财务报表功能（FinancialReportForm）

## 5. 报表功能
- [ ] 实现库存报表（InventoryReport）
- [ ] 实现出入库报表（InOutReport）
- [ ] 实现订单报表（OrderReport）

## 6. 系统管理
- [ ] 实现系统设置功能（SystemSettingsForm）
- [ ] 实现系统日志查看功能（SystemLogsForm，若有）

## 7. 权限与用户体验
- [ ] 完善权限管理（PermissionManager）
- [ ] 优化菜单和工具栏的权限显示与可用性
- [ ] 增加操作日志记录
- [ ] 增加多用户支持与切换

## 8. 其他
- [ ] 美化UI界面，提升用户体验
- [ ] 增加多语言支持（如有需要）
- [ ] 增加数据导入导出功能
- [ ] 增加帮助文档和关于页面内容 