Imports System.Drawing
Imports System.Windows.Forms
Imports FontAwesome.Sharp

''' <summary>
''' UI主题管理器 - 提供现代化的界面主题和图标支持
''' </summary>
Public Class UIThemeManager

    ' 主题颜色定义
    Public Shared ReadOnly Property PrimaryColor As Color = Color.FromArgb(52, 152, 219)
    Public Shared ReadOnly Property SecondaryColor As Color = Color.FromArgb(46, 204, 113)
    Public Shared ReadOnly Property AccentColor As Color = Color.FromArgb(155, 89, 182)
    Public Shared ReadOnly Property DangerColor As Color = Color.FromArgb(231, 76, 60)
    Public Shared ReadOnly Property WarningColor As Color = Color.FromArgb(241, 196, 15)
    Public Shared ReadOnly Property InfoColor As Color = Color.FromArgb(26, 188, 156)
    Public Shared ReadOnly Property DarkColor As Color = Color.FromArgb(52, 73, 94)
    Public Shared ReadOnly Property LightColor As Color = Color.FromArgb(236, 240, 241)
    Public Shared ReadOnly Property BackgroundColor As Color = Color.FromArgb(245, 247, 250)
    Public Shared ReadOnly Property SurfaceColor As Color = Color.White
    Public Shared ReadOnly Property BorderColor As Color = Color.FromArgb(220, 221, 225)
    Public Shared ReadOnly Property TextPrimaryColor As Color = Color.FromArgb(33, 37, 41)
    Public Shared ReadOnly Property TextSecondaryColor As Color = Color.FromArgb(108, 117, 125)

    ' 字体定义
    Public Shared ReadOnly Property DefaultFont As Font = New Font("Microsoft YaHei", 9)
    Public Shared ReadOnly Property HeaderFont As Font = New Font("Microsoft YaHei", 12, FontStyle.Bold)
    Public Shared ReadOnly Property TitleFont As Font = New Font("Microsoft YaHei", 14, FontStyle.Bold)
    Public Shared ReadOnly Property LargeFont As Font = New Font("Microsoft YaHei", 16, FontStyle.Bold)

    ' 图标映射
    Private Shared ReadOnly IconMap As New Dictionary(Of String, IconChar) From {
        {"dashboard", IconChar.ChartLine},
        {"customer", IconChar.Users},
        {"material", IconChar.Boxes},
        {"location", IconChar.MapMarkerAlt},
        {"supplier", IconChar.Truck},
        {"inventory", IconChar.Warehouse},
        {"inbound", IconChar.ArrowDown},
        {"outbound", IconChar.ArrowUp},
        {"order", IconChar.ShoppingCart},
        {"finance", IconChar.MoneyBillWave},
        {"report", IconChar.ChartBar},
        {"system", IconChar.Cog},
        {"user", IconChar.User},
        {"settings", IconChar.Wrench},
        {"search", IconChar.Search},
        {"add", IconChar.Plus},
        {"edit", IconChar.Edit},
        {"delete", IconChar.Trash},
        {"save", IconChar.Save},
        {"cancel", IconChar.Times},
        {"refresh", IconChar.SyncAlt},
        {"export", IconChar.FileExport},
        {"print", IconChar.Print},
        {"home", IconChar.Home},
        {"back", IconChar.ArrowLeft},
        {"forward", IconChar.ArrowRight},
        {"up", IconChar.ArrowUp},
        {"down", IconChar.ArrowDown},
        {"check", IconChar.Check},
        {"warning", IconChar.ExclamationTriangle},
        {"error", IconChar.ExclamationCircle},
        {"info", IconChar.InfoCircle},
        {"question", IconChar.QuestionCircle},
        {"calendar", IconChar.Calendar},
        {"clock", IconChar.Clock},
        {"email", IconChar.Envelope},
        {"phone", IconChar.Phone},
        {"address", IconChar.MapMarker},
        {"money", IconChar.DollarSign},
        {"percentage", IconChar.Percent},
        {"calculator", IconChar.Calculator},
        {"database", IconChar.Database},
        {"cloud", IconChar.Cloud},
        {"download", IconChar.Download},
        {"upload", IconChar.Upload},
        {"lock", IconChar.Lock},
        {"unlock", IconChar.Unlock},
        {"eye", IconChar.Eye},
        {"eyeSlash", IconChar.EyeSlash},
        {"star", IconChar.Star},
        {"heart", IconChar.Heart},
        {"bookmark", IconChar.Bookmark},
        {"tag", IconChar.Tag},
        {"folder", IconChar.Folder},
        {"file", IconChar.File},
        {"image", IconChar.Image},
        {"video", IconChar.Video},
        {"music", IconChar.Music},
        {"bell", IconChar.Bell},
        {"comment", IconChar.Comment},
        {"share", IconChar.Share},
        {"link", IconChar.Link},
        {"copy", IconChar.Copy},
        {"paste", IconChar.Paste},
        {"cut", IconChar.Cut},
        {"undo", IconChar.Undo},
        {"redo", IconChar.Redo}
    }

    ''' <summary>
    ''' 应用现代化主题到窗体
    ''' </summary>
    Public Shared Sub ApplyTheme(form As Form)
        form.BackColor = BackgroundColor
        form.Font = DefaultFont
        form.ForeColor = TextPrimaryColor

        ' 递归应用主题到所有控件
        ApplyThemeToControls(form.Controls)
    End Sub

    ''' <summary>
    ''' 递归应用主题到控件集合
    ''' </summary>
    Private Shared Sub ApplyThemeToControls(controls As Control.ControlCollection)
        For Each control As Control In controls
            ApplyThemeToControl(control)
            If control.HasChildren Then
                ApplyThemeToControls(control.Controls)
            End If
        Next
    End Sub

    ''' <summary>
    ''' 应用主题到单个控件
    ''' </summary>
    Private Shared Sub ApplyThemeToControl(control As Control)
        control.Font = DefaultFont
        control.ForeColor = TextPrimaryColor

        Select Case control.GetType()
            Case GetType(Button)
                ApplyButtonTheme(DirectCast(control, Button))
            Case GetType(Panel)
                ApplyPanelTheme(DirectCast(control, Panel))
            Case GetType(DataGridView)
                ApplyDataGridViewTheme(DirectCast(control, DataGridView))
            Case GetType(TextBox)
                ApplyTextBoxTheme(DirectCast(control, TextBox))
            Case GetType(ComboBox)
                ApplyComboBoxTheme(DirectCast(control, ComboBox))
            Case GetType(Label)
                ApplyLabelTheme(DirectCast(control, Label))
            Case GetType(TabControl)
                ApplyTabControlTheme(DirectCast(control, TabControl))
            Case GetType(MenuStrip)
                ApplyMenuStripTheme(DirectCast(control, MenuStrip))
            Case GetType(ToolStrip)
                ApplyToolStripTheme(DirectCast(control, ToolStrip))
        End Select
    End Sub

    ''' <summary>
    ''' 应用按钮主题
    ''' </summary>
    Public Shared Sub ApplyButtonTheme(button As Button)
        button.FlatStyle = FlatStyle.Flat
        button.FlatAppearance.BorderSize = 1
        button.FlatAppearance.BorderColor = BorderColor
        button.BackColor = SurfaceColor
        button.ForeColor = TextPrimaryColor
        button.Cursor = Cursors.Hand

        ' 添加悬停效果
        AddHandler button.MouseEnter, Sub(s, e)
                                          button.BackColor = LightColor
                                      End Sub
        AddHandler button.MouseLeave, Sub(s, e)
                                          button.BackColor = SurfaceColor
                                      End Sub
    End Sub

    ''' <summary>
    ''' 创建主要按钮样式
    ''' </summary>
    Public Shared Sub ApplyPrimaryButtonTheme(button As Button)
        button.FlatStyle = FlatStyle.Flat
        button.FlatAppearance.BorderSize = 0
        button.BackColor = PrimaryColor
        button.ForeColor = Color.White
        button.Cursor = Cursors.Hand

        AddHandler button.MouseEnter, Sub(s, e)
                                          button.BackColor = Color.FromArgb(41, 128, 185)
                                      End Sub
        AddHandler button.MouseLeave, Sub(s, e)
                                          button.BackColor = PrimaryColor
                                      End Sub
    End Sub

    ''' <summary>
    ''' 创建成功按钮样式
    ''' </summary>
    Public Shared Sub ApplySuccessButtonTheme(button As Button)
        button.FlatStyle = FlatStyle.Flat
        button.FlatAppearance.BorderSize = 0
        button.BackColor = SecondaryColor
        button.ForeColor = Color.White
        button.Cursor = Cursors.Hand

        AddHandler button.MouseEnter, Sub(s, e)
                                          button.BackColor = Color.FromArgb(39, 174, 96)
                                      End Sub
        AddHandler button.MouseLeave, Sub(s, e)
                                          button.BackColor = SecondaryColor
                                      End Sub
    End Sub

    ''' <summary>
    ''' 创建危险按钮样式
    ''' </summary>
    Public Shared Sub ApplyDangerButtonTheme(button As Button)
        button.FlatStyle = FlatStyle.Flat
        button.FlatAppearance.BorderSize = 0
        button.BackColor = DangerColor
        button.ForeColor = Color.White
        button.Cursor = Cursors.Hand

        AddHandler button.MouseEnter, Sub(s, e)
                                          button.BackColor = Color.FromArgb(192, 57, 43)
                                      End Sub
        AddHandler button.MouseLeave, Sub(s, e)
                                          button.BackColor = DangerColor
                                      End Sub
    End Sub

    ''' <summary>
    ''' 应用面板主题
    ''' </summary>
    Private Shared Sub ApplyPanelTheme(panel As Panel)
        panel.BackColor = SurfaceColor
        If panel.BorderStyle = BorderStyle.FixedSingle Then
            panel.BorderStyle = BorderStyle.None
            ' 可以在这里添加自定义边框绘制
        End If
    End Sub

    ''' <summary>
    ''' 应用DataGridView主题
    ''' </summary>
    Private Shared Sub ApplyDataGridViewTheme(dgv As DataGridView)
        dgv.BackgroundColor = SurfaceColor
        dgv.GridColor = BorderColor
        dgv.BorderStyle = BorderStyle.None
        dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal
        dgv.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None
        dgv.RowHeadersBorderStyle = DataGridViewHeaderBorderStyle.None

        ' 列标题样式
        dgv.ColumnHeadersDefaultCellStyle.BackColor = LightColor
        dgv.ColumnHeadersDefaultCellStyle.ForeColor = TextPrimaryColor
        dgv.ColumnHeadersDefaultCellStyle.Font = New Font(DefaultFont, FontStyle.Bold)
        dgv.ColumnHeadersDefaultCellStyle.SelectionBackColor = LightColor

        ' 行样式
        dgv.DefaultCellStyle.BackColor = SurfaceColor
        dgv.DefaultCellStyle.ForeColor = TextPrimaryColor
        dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(230, 240, 250)
        dgv.DefaultCellStyle.SelectionForeColor = TextPrimaryColor

        ' 交替行颜色
        dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250)
    End Sub

    ''' <summary>
    ''' 应用文本框主题
    ''' </summary>
    Private Shared Sub ApplyTextBoxTheme(textBox As TextBox)
        textBox.BorderStyle = BorderStyle.FixedSingle
        textBox.BackColor = SurfaceColor
        textBox.ForeColor = TextPrimaryColor
    End Sub

    ''' <summary>
    ''' 应用下拉框主题
    ''' </summary>
    Private Shared Sub ApplyComboBoxTheme(comboBox As ComboBox)
        comboBox.FlatStyle = FlatStyle.Flat
        comboBox.BackColor = SurfaceColor
        comboBox.ForeColor = TextPrimaryColor
    End Sub

    ''' <summary>
    ''' 应用标签主题
    ''' </summary>
    Private Shared Sub ApplyLabelTheme(label As Label)
        label.ForeColor = TextPrimaryColor
    End Sub

    ''' <summary>
    ''' 应用选项卡控件主题
    ''' </summary>
    Private Shared Sub ApplyTabControlTheme(tabControl As TabControl)
        tabControl.BackColor = BackgroundColor
        For Each tabPage As TabPage In tabControl.TabPages
            tabPage.BackColor = SurfaceColor
            tabPage.ForeColor = TextPrimaryColor
        Next
    End Sub

    ''' <summary>
    ''' 应用菜单栏主题
    ''' </summary>
    Private Shared Sub ApplyMenuStripTheme(menuStrip As MenuStrip)
        menuStrip.BackColor = SurfaceColor
        menuStrip.ForeColor = TextPrimaryColor
        menuStrip.Renderer = New ModernMenuStripRenderer()
    End Sub

    ''' <summary>
    ''' 应用工具栏主题
    ''' </summary>
    Private Shared Sub ApplyToolStripTheme(toolStrip As ToolStrip)
        toolStrip.BackColor = SurfaceColor
        toolStrip.ForeColor = TextPrimaryColor
        toolStrip.Renderer = New ModernToolStripRenderer()
    End Sub

    ''' <summary>
    ''' 获取图标
    ''' </summary>
    Public Shared Function GetIcon(iconName As String) As IconChar
        If IconMap.ContainsKey(iconName.ToLower()) Then
            Return IconMap(iconName.ToLower())
        Else
            Return IconChar.Question
        End If
    End Function

    ''' <summary>
    ''' 创建图标按钮
    ''' </summary>
    Public Shared Function CreateIconButton(text As String, iconName As String, Optional size As Size = Nothing) As IconButton
        Dim button As New IconButton()
        button.Text = text
        button.IconChar = GetIcon(iconName)
        button.IconSize = 16
        button.ImageAlign = ContentAlignment.MiddleLeft
        button.TextAlign = ContentAlignment.MiddleRight
        button.TextImageRelation = TextImageRelation.ImageBeforeText
        
        If size = Nothing Then
            button.Size = New Size(120, 35)
        Else
            button.Size = size
        End If

        ApplyPrimaryButtonTheme(button)
        Return button
    End Function

    ''' <summary>
    ''' 设置窗体自动缩放
    ''' </summary>
    Public Shared Sub SetAutoScale(form As Form)
        form.AutoScaleMode = AutoScaleMode.Dpi
        form.AutoScaleDimensions = New SizeF(96.0F, 96.0F)
        
        ' 启用高DPI支持
        If Environment.OSVersion.Version.Major >= 6 Then
            SetProcessDPIAware()
        End If
    End Sub

    <System.Runtime.InteropServices.DllImport("user32.dll")>
    Private Shared Function SetProcessDPIAware() As Boolean
    End Function

End Class

''' <summary>
''' 现代化菜单渲染器
''' </summary>
Public Class ModernMenuStripRenderer
    Inherits ToolStripProfessionalRenderer

    Protected Overrides Sub OnRenderMenuItemBackground(e As ToolStripItemRenderEventArgs)
        If e.Item.Selected Then
            e.Graphics.FillRectangle(New SolidBrush(UIThemeManager.LightColor), e.Item.Bounds)
        End If
    End Sub

End Class

''' <summary>
''' 现代化工具栏渲染器
''' </summary>
Public Class ModernToolStripRenderer
    Inherits ToolStripProfessionalRenderer

    Protected Overrides Sub OnRenderButtonBackground(e As ToolStripItemRenderEventArgs)
        If e.Item.Selected Then
            e.Graphics.FillRectangle(New SolidBrush(UIThemeManager.LightColor), e.Item.Bounds)
        End If
    End Sub

End Class
