# ERP库存管理系统 - PowerShell编译脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    ERP库存管理系统 - 编译脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 清理旧的编译文件
Write-Host "正在清理旧的编译文件..." -ForegroundColor Yellow
if (Test-Path "bin") { Remove-Item -Recurse -Force "bin" }
if (Test-Path "obj") { Remove-Item -Recurse -Force "obj" }
if (Test-Path "publish") { Remove-Item -Recurse -Force "publish" }

# 还原NuGet包
Write-Host ""
Write-Host "正在还原NuGet包..." -ForegroundColor Yellow
$result = dotnet restore InventorySystem.vbproj
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: NuGet包还原失败" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 编译Debug版本
Write-Host ""
Write-Host "正在编译Debug版本..." -ForegroundColor Yellow
$result = dotnet build InventorySystem.vbproj --configuration Debug
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: Debug编译失败" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 编译Release版本
Write-Host ""
Write-Host "正在编译Release版本..." -ForegroundColor Yellow
$result = dotnet build InventorySystem.vbproj --configuration Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: Release编译失败" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 发布单文件可执行程序
Write-Host ""
Write-Host "正在发布单文件可执行程序..." -ForegroundColor Yellow
$result = dotnet publish InventorySystem.vbproj `
    --configuration Release `
    --runtime win-x64 `
    --self-contained true `
    --output "publish\win-x64" `
    /p:PublishSingleFile=true `
    /p:PublishReadyToRun=true `
    /p:IncludeNativeLibrariesForSelfExtract=true

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 发布失败" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 发布Framework依赖版本
Write-Host ""
Write-Host "正在发布Framework依赖版本..." -ForegroundColor Yellow
$result = dotnet publish InventorySystem.vbproj `
    --configuration Release `
    --runtime win-x64 `
    --self-contained false `
    --output "publish\framework-dependent"

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: Framework依赖版本发布失败" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 创建部署包
Write-Host ""
Write-Host "正在创建部署包..." -ForegroundColor Yellow

# 创建配置文件
$configContent = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="DefaultConnection" connectionString="Server=127.0.0.1;Port=3306;Database=erpsystem;Uid=root;Pwd=******;CharSet=utf8mb4;" />
  </connectionStrings>
  <appSettings>
    <add key="SystemName" value="ERP库存管理系统" />
    <add key="Version" value="1.0.0" />
    <add key="Company" value="您的公司名称" />
  </appSettings>
</configuration>
"@

$configContent | Out-File -FilePath "publish\win-x64\InventorySystem.exe.config" -Encoding UTF8
$configContent | Out-File -FilePath "publish\framework-dependent\InventorySystem.exe.config" -Encoding UTF8

# 创建README文件
$readmeContent = @"
# ERP库存管理系统 v1.0.0

## 系统要求

### 单文件版本 (win-x64文件夹)
- Windows 10 或更高版本 (x64)
- 无需安装.NET Runtime

### Framework依赖版本 (framework-dependent文件夹)
- Windows 10 或更高版本
- 需要安装 .NET 6.0 Desktop Runtime
- 下载地址: https://dotnet.microsoft.com/download/dotnet/6.0

## 数据库配置

1. 安装MySQL 8.0或更高版本
2. 创建数据库: erpsystem
3. 修改配置文件中的数据库连接字符串
4. 首次运行程序会自动创建数据库表结构

## 默认登录账户

- 管理员: admin / admin123
- 普通用户: user / user123

## 功能特性

- 现代化UI界面设计
- 响应式布局支持
- FontAwesome图标库
- 完整的权限管理系统
- 基础数据管理
- 库存管理
- 订单管理
- 财务管理
- 报表分析
- 系统设置

## 技术支持

如有问题请联系技术支持团队。

© 2025 您的公司名称. 保留所有权利.
"@

$readmeContent | Out-File -FilePath "publish\README.txt" -Encoding UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "           编译完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "输出文件位置:" -ForegroundColor White
Write-Host "1. 单文件版本: publish\win-x64\InventorySystem.exe" -ForegroundColor Cyan
Write-Host "2. Framework依赖版本: publish\framework-dependent\InventorySystem.exe" -ForegroundColor Cyan
Write-Host ""
Write-Host "单文件版本包含所有依赖，可以在没有安装.NET的机器上运行" -ForegroundColor Yellow
Write-Host "Framework依赖版本需要目标机器安装.NET 6.0 Runtime" -ForegroundColor Yellow
Write-Host ""

# 显示文件大小信息
if (Test-Path "publish\win-x64\InventorySystem.exe") {
    $singleFileSize = (Get-Item "publish\win-x64\InventorySystem.exe").Length / 1MB
    Write-Host "单文件版本大小: $([math]::Round($singleFileSize, 2)) MB" -ForegroundColor Cyan
}

if (Test-Path "publish\framework-dependent\InventorySystem.exe") {
    $frameworkSize = (Get-Item "publish\framework-dependent\InventorySystem.exe").Length / 1MB
    Write-Host "Framework依赖版本大小: $([math]::Round($frameworkSize, 2)) MB" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "正在打开发布目录..." -ForegroundColor Yellow
Start-Process "publish"

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor White
Read-Host
