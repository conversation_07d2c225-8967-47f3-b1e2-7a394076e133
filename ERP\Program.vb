Imports System
Imports System.Windows.Forms

''' <summary>
''' 应用程序入口点
''' </summary>
Module Program
    ''' <summary>
    ''' 应用程序的主入口点
    ''' </summary>
    <STAThread>
    Sub Main()
        Application.EnableVisualStyles()
        Application.SetCompatibleTextRenderingDefault(False)
        
        ' 初始化数据库连接
        Try
            DatabaseManager.Initialize()
            Console.WriteLine("数据库连接初始化成功")
        Catch ex As Exception
            MessageBox.Show($"数据库连接失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End Try
        
        ' 启动登录窗体
        Application.Run(New LoginForm())
    End Sub
End Module
