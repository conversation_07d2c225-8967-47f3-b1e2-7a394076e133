Imports System.Windows.Forms
Imports System.Drawing

Public Class AccountsReceivableForm
    Inherits Form

    ' 控件声明
    Private dgvAccountsReceivable As DataGridView
    Private txtARNumber As TextBox
    Private cmbCustomer As ComboBox
    Private cmbOrder As ComboBox
    Private txtInvoiceNumber As TextBox
    Private dtpInvoiceDate As DateTimePicker
    Private dtpDueDate As DateTimePicker
    Private txtOriginalAmount As TextBox
    Private txtPaidAmount As TextBox
    Private txtOutstandingAmount As TextBox
    Private cmbCurrency As ComboBox
    Private txtExchangeRate As TextBox
    Private cmbStatus As ComboBox
    Private txtPaymentTerms As TextBox
    Private txtDescription As TextBox

    ' 按钮控件
    Private btnNew As Button
    Private btnEdit As Button
    Private btnSave As Button
    Private btnCancel As Button
    Private btnDelete As Button
    Private btnRefresh As Button
    Private btnPayment As Button

    Private isEditMode As Boolean = False
    Private currentARId As Integer = 0

    Public Sub New()
        InitializeComponent()
        LoadAccountsReceivable()
        LoadCustomers()
        ApplyPermissions()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "应收账款管理"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterParent

        ' 创建数据网格
        dgvAccountsReceivable = New DataGridView()
        dgvAccountsReceivable.Location = New Point(10, 10)
        dgvAccountsReceivable.Size = New Size(700, 400)
        dgvAccountsReceivable.ReadOnly = True
        dgvAccountsReceivable.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvAccountsReceivable.MultiSelect = False
        dgvAccountsReceivable.AllowUserToAddRows = False
        dgvAccountsReceivable.AllowUserToDeleteRows = False
        AddHandler dgvAccountsReceivable.SelectionChanged, AddressOf dgvAccountsReceivable_SelectionChanged
        Me.Controls.Add(dgvAccountsReceivable)

        ' 创建输入控件
        CreateInputControls()

        ' 创建按钮
        CreateButtons()

        ' 设置初始状态
        SetFormMode(False)
    End Sub

    Private Sub CreateInputControls()
        Dim startX As Integer = 730
        Dim startY As Integer = 30
        Dim labelWidth As Integer = 100
        Dim controlWidth As Integer = 200
        Dim rowHeight As Integer = 35

        ' 应收单号
        Dim lblARNumber As New Label()
        lblARNumber.Text = "应收单号:"
        lblARNumber.Location = New Point(startX, startY)
        lblARNumber.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblARNumber)

        txtARNumber = New TextBox()
        txtARNumber.Location = New Point(startX + labelWidth, startY)
        txtARNumber.Size = New Size(controlWidth, 20)
        txtARNumber.ReadOnly = True
        Me.Controls.Add(txtARNumber)

        ' 客户
        startY += rowHeight
        Dim lblCustomer As New Label()
        lblCustomer.Text = "客户:"
        lblCustomer.Location = New Point(startX, startY)
        lblCustomer.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblCustomer)

        cmbCustomer = New ComboBox()
        cmbCustomer.Location = New Point(startX + labelWidth, startY)
        cmbCustomer.Size = New Size(controlWidth, 20)
        cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList
        AddHandler cmbCustomer.SelectedIndexChanged, AddressOf cmbCustomer_SelectedIndexChanged
        Me.Controls.Add(cmbCustomer)

        ' 关联订单
        startY += rowHeight
        Dim lblOrder As New Label()
        lblOrder.Text = "关联订单:"
        lblOrder.Location = New Point(startX, startY)
        lblOrder.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblOrder)

        cmbOrder = New ComboBox()
        cmbOrder.Location = New Point(startX + labelWidth, startY)
        cmbOrder.Size = New Size(controlWidth, 20)
        cmbOrder.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbOrder)

        ' 发票号码
        startY += rowHeight
        Dim lblInvoiceNumber As New Label()
        lblInvoiceNumber.Text = "发票号码:"
        lblInvoiceNumber.Location = New Point(startX, startY)
        lblInvoiceNumber.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblInvoiceNumber)

        txtInvoiceNumber = New TextBox()
        txtInvoiceNumber.Location = New Point(startX + labelWidth, startY)
        txtInvoiceNumber.Size = New Size(controlWidth, 20)
        Me.Controls.Add(txtInvoiceNumber)

        ' 开票日期
        startY += rowHeight
        Dim lblInvoiceDate As New Label()
        lblInvoiceDate.Text = "开票日期:"
        lblInvoiceDate.Location = New Point(startX, startY)
        lblInvoiceDate.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblInvoiceDate)

        dtpInvoiceDate = New DateTimePicker()
        dtpInvoiceDate.Location = New Point(startX + labelWidth, startY)
        dtpInvoiceDate.Size = New Size(controlWidth, 20)
        dtpInvoiceDate.Format = DateTimePickerFormat.Short
        Me.Controls.Add(dtpInvoiceDate)

        ' 到期日期
        startY += rowHeight
        Dim lblDueDate As New Label()
        lblDueDate.Text = "到期日期:"
        lblDueDate.Location = New Point(startX, startY)
        lblDueDate.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblDueDate)

        dtpDueDate = New DateTimePicker()
        dtpDueDate.Location = New Point(startX + labelWidth, startY)
        dtpDueDate.Size = New Size(controlWidth, 20)
        dtpDueDate.Format = DateTimePickerFormat.Short
        Me.Controls.Add(dtpDueDate)

        ' 原始金额
        startY += rowHeight
        Dim lblOriginalAmount As New Label()
        lblOriginalAmount.Text = "原始金额:"
        lblOriginalAmount.Location = New Point(startX, startY)
        lblOriginalAmount.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblOriginalAmount)

        txtOriginalAmount = New TextBox()
        txtOriginalAmount.Location = New Point(startX + labelWidth, startY)
        txtOriginalAmount.Size = New Size(controlWidth, 20)
        AddHandler txtOriginalAmount.TextChanged, AddressOf CalculateOutstandingAmount
        Me.Controls.Add(txtOriginalAmount)

        ' 已付金额
        startY += rowHeight
        Dim lblPaidAmount As New Label()
        lblPaidAmount.Text = "已付金额:"
        lblPaidAmount.Location = New Point(startX, startY)
        lblPaidAmount.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblPaidAmount)

        txtPaidAmount = New TextBox()
        txtPaidAmount.Location = New Point(startX + labelWidth, startY)
        txtPaidAmount.Size = New Size(controlWidth, 20)
        txtPaidAmount.ReadOnly = True
        Me.Controls.Add(txtPaidAmount)

        ' 未付金额
        startY += rowHeight
        Dim lblOutstandingAmount As New Label()
        lblOutstandingAmount.Text = "未付金额:"
        lblOutstandingAmount.Location = New Point(startX, startY)
        lblOutstandingAmount.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblOutstandingAmount)

        txtOutstandingAmount = New TextBox()
        txtOutstandingAmount.Location = New Point(startX + labelWidth, startY)
        txtOutstandingAmount.Size = New Size(controlWidth, 20)
        txtOutstandingAmount.ReadOnly = True
        Me.Controls.Add(txtOutstandingAmount)

        ' 币种
        startY += rowHeight
        Dim lblCurrency As New Label()
        lblCurrency.Text = "币种:"
        lblCurrency.Location = New Point(startX, startY)
        lblCurrency.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblCurrency)

        cmbCurrency = New ComboBox()
        cmbCurrency.Location = New Point(startX + labelWidth, startY)
        cmbCurrency.Size = New Size(controlWidth, 20)
        cmbCurrency.Items.AddRange({"CNY", "USD", "EUR", "JPY"})
        cmbCurrency.SelectedIndex = 0
        Me.Controls.Add(cmbCurrency)

        ' 汇率
        startY += rowHeight
        Dim lblExchangeRate As New Label()
        lblExchangeRate.Text = "汇率:"
        lblExchangeRate.Location = New Point(startX, startY)
        lblExchangeRate.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblExchangeRate)

        txtExchangeRate = New TextBox()
        txtExchangeRate.Location = New Point(startX + labelWidth, startY)
        txtExchangeRate.Size = New Size(controlWidth, 20)
        txtExchangeRate.Text = "1.0000"
        Me.Controls.Add(txtExchangeRate)

        ' 状态
        startY += rowHeight
        Dim lblStatus As New Label()
        lblStatus.Text = "状态:"
        lblStatus.Location = New Point(startX, startY)
        lblStatus.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblStatus)

        cmbStatus = New ComboBox()
        cmbStatus.Location = New Point(startX + labelWidth, startY)
        cmbStatus.Size = New Size(controlWidth, 20)
        cmbStatus.Items.AddRange({"pending", "partial", "paid", "overdue", "cancelled"})
        cmbStatus.SelectedIndex = 0
        Me.Controls.Add(cmbStatus)

        ' 付款条件
        startY += rowHeight
        Dim lblPaymentTerms As New Label()
        lblPaymentTerms.Text = "付款条件:"
        lblPaymentTerms.Location = New Point(startX, startY)
        lblPaymentTerms.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblPaymentTerms)

        txtPaymentTerms = New TextBox()
        txtPaymentTerms.Location = New Point(startX + labelWidth, startY)
        txtPaymentTerms.Size = New Size(controlWidth, 20)
        Me.Controls.Add(txtPaymentTerms)

        ' 备注说明
        startY += rowHeight
        Dim lblDescription As New Label()
        lblDescription.Text = "备注说明:"
        lblDescription.Location = New Point(startX, startY)
        lblDescription.Size = New Size(labelWidth, 20)
        Me.Controls.Add(lblDescription)

        txtDescription = New TextBox()
        txtDescription.Location = New Point(startX + labelWidth, startY)
        txtDescription.Size = New Size(controlWidth, 60)
        txtDescription.Multiline = True
        Me.Controls.Add(txtDescription)
    End Sub

    Private Sub CreateButtons()
        Dim buttonY As Integer = 450
        Dim buttonWidth As Integer = 80
        Dim buttonHeight As Integer = 30
        Dim buttonSpacing As Integer = 90

        btnNew = New Button()
        btnNew.Text = "新增"
        btnNew.Location = New Point(10, buttonY)
        btnNew.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnNew.Click, AddressOf btnNew_Click
        Me.Controls.Add(btnNew)

        btnEdit = New Button()
        btnEdit.Text = "编辑"
        btnEdit.Location = New Point(10 + buttonSpacing, buttonY)
        btnEdit.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnEdit.Click, AddressOf btnEdit_Click
        Me.Controls.Add(btnEdit)

        btnSave = New Button()
        btnSave.Text = "保存"
        btnSave.Location = New Point(10 + buttonSpacing * 2, buttonY)
        btnSave.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnSave.Click, AddressOf btnSave_Click
        Me.Controls.Add(btnSave)

        btnCancel = New Button()
        btnCancel.Text = "取消"
        btnCancel.Location = New Point(10 + buttonSpacing * 3, buttonY)
        btnCancel.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnCancel.Click, AddressOf btnCancel_Click
        Me.Controls.Add(btnCancel)

        btnDelete = New Button()
        btnDelete.Text = "删除"
        btnDelete.Location = New Point(10 + buttonSpacing * 4, buttonY)
        btnDelete.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnDelete.Click, AddressOf btnDelete_Click
        Me.Controls.Add(btnDelete)

        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(10 + buttonSpacing * 5, buttonY)
        btnRefresh.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnRefresh.Click, AddressOf btnRefresh_Click
        Me.Controls.Add(btnRefresh)

        btnPayment = New Button()
        btnPayment.Text = "收款"
        btnPayment.Location = New Point(10 + buttonSpacing * 6, buttonY)
        btnPayment.Size = New Size(buttonWidth, buttonHeight)
        AddHandler btnPayment.Click, AddressOf btnPayment_Click
        Me.Controls.Add(btnPayment)
    End Sub

    Private Sub LoadAccountsReceivable()
        Try
            Dim sql = "SELECT ar.id, ar.ar_number, c.customer_name, o.order_number, ar.invoice_number, " &
                     "ar.invoice_date, ar.due_date, ar.original_amount, ar.paid_amount, ar.outstanding_amount, " &
                     "ar.currency, ar.status, ar.payment_terms, ar.description " &
                     "FROM accounts_receivable ar " &
                     "LEFT JOIN customers c ON ar.customer_id = c.id " &
                     "LEFT JOIN orders o ON ar.order_id = o.id " &
                     "ORDER BY ar.created_at DESC"

            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvAccountsReceivable.DataSource = dt

            ' 设置列标题
            If dgvAccountsReceivable.Columns.Count > 0 Then
                dgvAccountsReceivable.Columns("id").Visible = False
                dgvAccountsReceivable.Columns("ar_number").HeaderText = "应收单号"
                dgvAccountsReceivable.Columns("customer_name").HeaderText = "客户名称"
                dgvAccountsReceivable.Columns("order_number").HeaderText = "订单号"
                dgvAccountsReceivable.Columns("invoice_number").HeaderText = "发票号码"
                dgvAccountsReceivable.Columns("invoice_date").HeaderText = "开票日期"
                dgvAccountsReceivable.Columns("due_date").HeaderText = "到期日期"
                dgvAccountsReceivable.Columns("original_amount").HeaderText = "原始金额"
                dgvAccountsReceivable.Columns("paid_amount").HeaderText = "已付金额"
                dgvAccountsReceivable.Columns("outstanding_amount").HeaderText = "未付金额"
                dgvAccountsReceivable.Columns("currency").HeaderText = "币种"
                dgvAccountsReceivable.Columns("status").HeaderText = "状态"
                dgvAccountsReceivable.Columns("payment_terms").HeaderText = "付款条件"
                dgvAccountsReceivable.Columns("description").HeaderText = "备注说明"

                ' 设置列宽
                dgvAccountsReceivable.Columns("ar_number").Width = 120
                dgvAccountsReceivable.Columns("customer_name").Width = 100
                dgvAccountsReceivable.Columns("order_number").Width = 100
                dgvAccountsReceivable.Columns("invoice_number").Width = 100
                dgvAccountsReceivable.Columns("original_amount").Width = 80
                dgvAccountsReceivable.Columns("paid_amount").Width = 80
                dgvAccountsReceivable.Columns("outstanding_amount").Width = 80
                dgvAccountsReceivable.Columns("status").Width = 60
            End If
        Catch ex As Exception
            MessageBox.Show($"加载应收账款数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCustomers()
        Try
            Dim sql = "SELECT id, customer_name FROM customers WHERE is_active = 1 ORDER BY customer_name"
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)

            cmbCustomer.Items.Clear()
            cmbCustomer.Items.Add(New With {.Value = 0, .Text = "请选择客户"})

            For Each row As DataRow In dt.Rows
                cmbCustomer.Items.Add(New With {.Value = Convert.ToInt32(row("id")), .Text = row("customer_name").ToString()})
            Next

            cmbCustomer.DisplayMember = "Text"
            cmbCustomer.ValueMember = "Value"
            cmbCustomer.SelectedIndex = 0
        Catch ex As Exception
            MessageBox.Show($"加载客户数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadOrdersByCustomer(customerId As Integer)
        Try
            cmbOrder.Items.Clear()
            cmbOrder.Items.Add(New With {.Value = 0, .Text = "请选择订单"})

            If customerId > 0 Then
                Dim sql = "SELECT id, order_number FROM orders WHERE customer_id = @customer_id ORDER BY order_date DESC"
                Dim parameters As New Dictionary(Of String, Object) From {{"@customer_id", customerId}}
                Dim dt = DatabaseManager.Instance.ExecuteQuery(sql, parameters)

                For Each row As DataRow In dt.Rows
                    cmbOrder.Items.Add(New With {.Value = Convert.ToInt32(row("id")), .Text = row("order_number").ToString()})
                Next
            End If

            cmbOrder.DisplayMember = "Text"
            cmbOrder.ValueMember = "Value"
            cmbOrder.SelectedIndex = 0
        Catch ex As Exception
            MessageBox.Show($"加载订单数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub dgvAccountsReceivable_SelectionChanged(sender As Object, e As EventArgs)
        If dgvAccountsReceivable.SelectedRows.Count > 0 AndAlso Not isEditMode Then
            Dim selectedRow = dgvAccountsReceivable.SelectedRows(0)
            currentARId = Convert.ToInt32(selectedRow.Cells("id").Value)
            LoadARDetails()
        End If
    End Sub

    Private Sub LoadARDetails()
        Try
            Dim sql = "SELECT * FROM accounts_receivable WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentARId}}
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql, parameters)

            If dt.Rows.Count > 0 Then
                Dim row = dt.Rows(0)
                txtARNumber.Text = row("ar_number").ToString()

                ' 设置客户
                For i As Integer = 0 To cmbCustomer.Items.Count - 1
                    If DirectCast(cmbCustomer.Items(i), Object).Value = Convert.ToInt32(row("customer_id")) Then
                        cmbCustomer.SelectedIndex = i
                        Exit For
                    End If
                Next

                ' 加载该客户的订单并设置选中项
                LoadOrdersByCustomer(Convert.ToInt32(row("customer_id")))
                If Not IsDBNull(row("order_id")) Then
                    For i As Integer = 0 To cmbOrder.Items.Count - 1
                        If DirectCast(cmbOrder.Items(i), Object).Value = Convert.ToInt32(row("order_id")) Then
                            cmbOrder.SelectedIndex = i
                            Exit For
                        End If
                    Next
                End If

                txtInvoiceNumber.Text = If(IsDBNull(row("invoice_number")), "", row("invoice_number").ToString())
                If Not IsDBNull(row("invoice_date")) Then
                    dtpInvoiceDate.Value = Convert.ToDateTime(row("invoice_date"))
                End If
                dtpDueDate.Value = Convert.ToDateTime(row("due_date"))
                txtOriginalAmount.Text = Convert.ToDecimal(row("original_amount")).ToString("F2")
                txtPaidAmount.Text = Convert.ToDecimal(row("paid_amount")).ToString("F2")
                txtOutstandingAmount.Text = Convert.ToDecimal(row("outstanding_amount")).ToString("F2")
                cmbCurrency.Text = row("currency").ToString()
                txtExchangeRate.Text = Convert.ToDecimal(row("exchange_rate")).ToString("F4")
                cmbStatus.Text = row("status").ToString()
                txtPaymentTerms.Text = If(IsDBNull(row("payment_terms")), "", row("payment_terms").ToString())
                txtDescription.Text = If(IsDBNull(row("description")), "", row("description").ToString())
            End If
        Catch ex As Exception
            MessageBox.Show($"加载应收账款详情时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode

        ' 设置输入控件的启用状态
        cmbCustomer.Enabled = editMode
        cmbOrder.Enabled = editMode
        txtInvoiceNumber.Enabled = editMode
        dtpInvoiceDate.Enabled = editMode
        dtpDueDate.Enabled = editMode
        txtOriginalAmount.Enabled = editMode
        cmbCurrency.Enabled = editMode
        txtExchangeRate.Enabled = editMode
        cmbStatus.Enabled = editMode
        txtPaymentTerms.Enabled = editMode
        txtDescription.Enabled = editMode

        ' 设置按钮的启用状态（结合权限控制）
        btnNew.Enabled = Not editMode AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageFinancial)
        btnEdit.Enabled = Not editMode AndAlso currentARId > 0 AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageFinancial)
        btnDelete.Enabled = Not editMode AndAlso currentARId > 0 AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageFinancial)
        btnSave.Enabled = editMode
        btnCancel.Enabled = editMode
        btnRefresh.Enabled = Not editMode
        btnPayment.Enabled = Not editMode AndAlso currentARId > 0 AndAlso PermissionManager.HasPermission(PermissionManager.Permission.ManageFinancial)

        dgvAccountsReceivable.Enabled = Not editMode
    End Sub

    ''' <summary>
    ''' 应用权限控制
    ''' </summary>
    Private Sub ApplyPermissions()
        ' 检查基本查看权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewFinancial) Then
            MessageBox.Show("您没有权限访问财务管理功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 根据权限设置按钮状态
        SetFormMode(False)
    End Sub

    Private Sub ClearForm()
        txtARNumber.Clear()
        cmbCustomer.SelectedIndex = 0
        cmbOrder.SelectedIndex = 0
        txtInvoiceNumber.Clear()
        dtpInvoiceDate.Value = DateTime.Now
        dtpDueDate.Value = DateTime.Now.AddDays(30)
        txtOriginalAmount.Clear()
        txtPaidAmount.Text = "0.00"
        txtOutstandingAmount.Clear()
        cmbCurrency.SelectedIndex = 0
        txtExchangeRate.Text = "1.0000"
        cmbStatus.SelectedIndex = 0
        txtPaymentTerms.Clear()
        txtDescription.Clear()
        currentARId = 0
    End Sub

    Private Sub cmbCustomer_SelectedIndexChanged(sender As Object, e As EventArgs)
        If cmbCustomer.SelectedIndex > 0 Then
            Dim customerId = DirectCast(cmbCustomer.SelectedItem, Object).Value
            LoadOrdersByCustomer(customerId)
        Else
            cmbOrder.Items.Clear()
            cmbOrder.Items.Add(New With {.Value = 0, .Text = "请选择订单"})
            cmbOrder.SelectedIndex = 0
        End If
    End Sub

    Private Sub CalculateOutstandingAmount(sender As Object, e As EventArgs)
        Try
            Dim originalAmount As Decimal = 0
            Dim paidAmount As Decimal = 0

            If Decimal.TryParse(txtOriginalAmount.Text, originalAmount) AndAlso
               Decimal.TryParse(txtPaidAmount.Text, paidAmount) Then
                txtOutstandingAmount.Text = (originalAmount - paidAmount).ToString("F2")
            End If
        Catch ex As Exception
            ' 忽略计算错误
        End Try
    End Sub

    ' 按钮事件处理
    Private Sub btnNew_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageFinancial) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        ClearForm()
        GenerateARNumber()
        SetFormMode(True)
        cmbCustomer.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageFinancial) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If
        If currentARId <= 0 Then
            MessageBox.Show("请先选择要编辑的应收账款", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        SetFormMode(True)
        cmbCustomer.Focus()
    End Sub

    Private Sub GenerateARNumber()
        Try
            Dim today = DateTime.Now.ToString("yyyyMMdd")
            Dim sql = "SELECT COUNT(*) FROM accounts_receivable WHERE ar_number LIKE @pattern"
            Dim parameters As New Dictionary(Of String, Object) From {{"@pattern", $"AR{today}%"}}
            Dim count = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(sql, parameters))
            txtARNumber.Text = $"AR{today}{(count + 1).ToString("D3")}"
        Catch ex As Exception
            txtARNumber.Text = $"AR{DateTime.Now:yyyyMMdd}001"
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs)
        If Not ValidateForm() Then Return

        Try
            If currentARId = 0 Then
                ' 新增
                Dim sql = "INSERT INTO accounts_receivable (ar_number, customer_id, order_id, invoice_number, invoice_date, due_date, " &
                         "original_amount, paid_amount, outstanding_amount, currency, exchange_rate, status, payment_terms, description, created_by) " &
                         "VALUES (@ar_number, @customer_id, @order_id, @invoice_number, @invoice_date, @due_date, " &
                         "@original_amount, @paid_amount, @outstanding_amount, @currency, @exchange_rate, @status, @payment_terms, @description, @created_by)"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@ar_number", txtARNumber.Text.Trim()},
                    {"@customer_id", DirectCast(cmbCustomer.SelectedItem, Object).Value},
                    {"@order_id", If(cmbOrder.SelectedIndex > 0, DirectCast(cmbOrder.SelectedItem, Object).Value, DBNull.Value)},
                    {"@invoice_number", If(String.IsNullOrEmpty(txtInvoiceNumber.Text.Trim()), DBNull.Value, txtInvoiceNumber.Text.Trim())},
                    {"@invoice_date", If(String.IsNullOrEmpty(txtInvoiceNumber.Text.Trim()), DBNull.Value, dtpInvoiceDate.Value.Date)},
                    {"@due_date", dtpDueDate.Value.Date},
                    {"@original_amount", Convert.ToDecimal(txtOriginalAmount.Text)},
                    {"@paid_amount", Convert.ToDecimal(txtPaidAmount.Text)},
                    {"@outstanding_amount", Convert.ToDecimal(txtOutstandingAmount.Text)},
                    {"@currency", cmbCurrency.Text},
                    {"@exchange_rate", Convert.ToDecimal(txtExchangeRate.Text)},
                    {"@status", cmbStatus.Text},
                    {"@payment_terms", If(String.IsNullOrEmpty(txtPaymentTerms.Text.Trim()), DBNull.Value, txtPaymentTerms.Text.Trim())},
                    {"@description", If(String.IsNullOrEmpty(txtDescription.Text.Trim()), DBNull.Value, txtDescription.Text.Trim())},
                    {"@created_by", UserSession.CurrentUserId}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("应收账款信息保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' 更新
                Dim sql = "UPDATE accounts_receivable SET customer_id = @customer_id, order_id = @order_id, invoice_number = @invoice_number, " &
                         "invoice_date = @invoice_date, due_date = @due_date, original_amount = @original_amount, paid_amount = @paid_amount, " &
                         "outstanding_amount = @outstanding_amount, currency = @currency, exchange_rate = @exchange_rate, status = @status, " &
                         "payment_terms = @payment_terms, description = @description WHERE id = @id"

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@customer_id", DirectCast(cmbCustomer.SelectedItem, Object).Value},
                    {"@order_id", If(cmbOrder.SelectedIndex > 0, DirectCast(cmbOrder.SelectedItem, Object).Value, DBNull.Value)},
                    {"@invoice_number", If(String.IsNullOrEmpty(txtInvoiceNumber.Text.Trim()), DBNull.Value, txtInvoiceNumber.Text.Trim())},
                    {"@invoice_date", If(String.IsNullOrEmpty(txtInvoiceNumber.Text.Trim()), DBNull.Value, dtpInvoiceDate.Value.Date)},
                    {"@due_date", dtpDueDate.Value.Date},
                    {"@original_amount", Convert.ToDecimal(txtOriginalAmount.Text)},
                    {"@paid_amount", Convert.ToDecimal(txtPaidAmount.Text)},
                    {"@outstanding_amount", Convert.ToDecimal(txtOutstandingAmount.Text)},
                    {"@currency", cmbCurrency.Text},
                    {"@exchange_rate", Convert.ToDecimal(txtExchangeRate.Text)},
                    {"@status", cmbStatus.Text},
                    {"@payment_terms", If(String.IsNullOrEmpty(txtPaymentTerms.Text.Trim()), DBNull.Value, txtPaymentTerms.Text.Trim())},
                    {"@description", If(String.IsNullOrEmpty(txtDescription.Text.Trim()), DBNull.Value, txtDescription.Text.Trim())},
                    {"@id", currentARId}
                }

                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)
                MessageBox.Show("应收账款信息更新成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            SetFormMode(False)
            LoadAccountsReceivable()
        Catch ex As Exception
            MessageBox.Show($"保存应收账款信息时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        SetFormMode(False)
        If currentARId > 0 Then
            LoadARDetails()
        Else
            ClearForm()
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageFinancial) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        If currentARId <= 0 Then
            MessageBox.Show("请先选择要删除的应收账款", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If MessageBox.Show("确定要删除选中的应收账款吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim sql = "DELETE FROM accounts_receivable WHERE id = @id"
                Dim parameters As New Dictionary(Of String, Object) From {{"@id", currentARId}}
                DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)

                MessageBox.Show("应收账款删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                ClearForm()
                LoadAccountsReceivable()
            Catch ex As Exception
                MessageBox.Show($"删除应收账款时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs)
        LoadAccountsReceivable()
        LoadCustomers()
    End Sub

    Private Sub btnPayment_Click(sender As Object, e As EventArgs)
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ManageFinancial) Then
            PermissionManager.ShowPermissionDeniedMessage()
            Return
        End If

        If currentARId <= 0 Then
            MessageBox.Show("请先选择要收款的应收账款", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        ' 打开收款窗体
        Dim paymentForm As New PaymentRecordForm(currentARId, "receivable")
        If paymentForm.ShowDialog() = DialogResult.OK Then
            LoadAccountsReceivable()
            LoadARDetails()
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If cmbCustomer.SelectedIndex <= 0 Then
            MessageBox.Show("请选择客户", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbCustomer.Focus()
            Return False
        End If

        If String.IsNullOrEmpty(txtOriginalAmount.Text.Trim()) Then
            MessageBox.Show("请输入原始金额", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOriginalAmount.Focus()
            Return False
        End If

        Dim amount As Decimal
        If Not Decimal.TryParse(txtOriginalAmount.Text, amount) OrElse amount <= 0 Then
            MessageBox.Show("请输入有效的原始金额", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOriginalAmount.Focus()
            Return False
        End If

        Dim exchangeRate As Decimal
        If Not Decimal.TryParse(txtExchangeRate.Text, exchangeRate) OrElse exchangeRate <= 0 Then
            MessageBox.Show("请输入有效的汇率", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtExchangeRate.Focus()
            Return False
        End If

        Return True
    End Function
End Class
