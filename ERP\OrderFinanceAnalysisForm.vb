Imports System.Windows.Forms
Imports System.Data
Imports System.Drawing

''' <summary>
''' 订单财务分析窗体
''' </summary>
Public Class OrderFinanceAnalysisForm
    Inherits Form

    Private tabControl As TabControl
    Private tabOrderSummary As TabPage
    Private tabInvoiceTracking As TabPage
    Private tabDeliveryTracking As TabPage
    Private tabFinancialReport As TabPage

    ' 订单汇总控件
    Private dgvOrderSummary As DataGridView
    Private lblTotalOrders As Label
    Private lblTotalAmount As Label
    Private lblInvoicedAmount As Label
    Private lblOutstandingAmount As Label
    Private lblDeliveredAmount As Label
    Private lblPendingDelivery As Label

    ' 发票跟踪控件
    Private dgvInvoiceTracking As DataGridView
    Private dtpInvoiceFromDate As DateTimePicker
    Private dtpInvoiceToDate As DateTimePicker
    Private btnFilterInvoice As Button
    Private btnExportInvoice As Button

    ' 交货跟踪控件
    Private dgvDeliveryTracking As DataGridView
    Private dtpDeliveryFromDate As DateTimePicker
    Private dtpDeliveryToDate As DateTimePicker
    Private btnFilterDelivery As Button
    Private btnExportDelivery As Button

    ' 财务报表控件
    Private dgvFinancialReport As DataGridView
    Private cmbReportType As ComboBox
    Private dtpReportFromDate As DateTimePicker
    Private dtpReportToDate As DateTimePicker
    Private btnGenerateReport As Button
    Private btnExportReport As Button

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadData()
        ApplyPermissions()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "订单财务分析"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
    End Sub

    Private Sub SetupUI()
        ' 创建选项卡控件
        tabControl = New TabControl()
        tabControl.Dock = DockStyle.Fill
        Me.Controls.Add(tabControl)

        ' 订单汇总选项卡
        tabOrderSummary = New TabPage("订单汇总")
        tabControl.TabPages.Add(tabOrderSummary)
        SetupOrderSummaryTab()

        ' 发票跟踪选项卡
        tabInvoiceTracking = New TabPage("发票跟踪")
        tabControl.TabPages.Add(tabInvoiceTracking)
        SetupInvoiceTrackingTab()

        ' 交货跟踪选项卡
        tabDeliveryTracking = New TabPage("交货跟踪")
        tabControl.TabPages.Add(tabDeliveryTracking)
        SetupDeliveryTrackingTab()

        ' 财务报表选项卡
        tabFinancialReport = New TabPage("财务报表")
        tabControl.TabPages.Add(tabFinancialReport)
        SetupFinancialReportTab()
    End Sub

    Private Sub SetupOrderSummaryTab()
        ' 统计信息面板
        Dim pnlStats As New Panel()
        pnlStats.Height = 120
        pnlStats.Dock = DockStyle.Top
        pnlStats.BackColor = Color.LightGray
        tabOrderSummary.Controls.Add(pnlStats)

        ' 第一行统计
        lblTotalOrders = New Label()
        lblTotalOrders.Text = "订单总数: 0"
        lblTotalOrders.Location = New Point(20, 20)
        lblTotalOrders.Size = New Size(150, 20)
        lblTotalOrders.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        pnlStats.Controls.Add(lblTotalOrders)

        lblTotalAmount = New Label()
        lblTotalAmount.Text = "订单总金额: ¥0.00"
        lblTotalAmount.Location = New Point(200, 20)
        lblTotalAmount.Size = New Size(200, 20)
        lblTotalAmount.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblTotalAmount.ForeColor = Color.Blue
        pnlStats.Controls.Add(lblTotalAmount)

        lblInvoicedAmount = New Label()
        lblInvoicedAmount.Text = "已开票金额: ¥0.00"
        lblInvoicedAmount.Location = New Point(420, 20)
        lblInvoicedAmount.Size = New Size(200, 20)
        lblInvoicedAmount.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblInvoicedAmount.ForeColor = Color.Green
        pnlStats.Controls.Add(lblInvoicedAmount)

        ' 第二行统计
        lblOutstandingAmount = New Label()
        lblOutstandingAmount.Text = "未开票金额: ¥0.00"
        lblOutstandingAmount.Location = New Point(20, 50)
        lblOutstandingAmount.Size = New Size(200, 20)
        lblOutstandingAmount.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblOutstandingAmount.ForeColor = Color.Red
        pnlStats.Controls.Add(lblOutstandingAmount)

        lblDeliveredAmount = New Label()
        lblDeliveredAmount.Text = "已交货金额: ¥0.00"
        lblDeliveredAmount.Location = New Point(240, 50)
        lblDeliveredAmount.Size = New Size(200, 20)
        lblDeliveredAmount.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblDeliveredAmount.ForeColor = Color.DarkGreen
        pnlStats.Controls.Add(lblDeliveredAmount)

        lblPendingDelivery = New Label()
        lblPendingDelivery.Text = "待交货金额: ¥0.00"
        lblPendingDelivery.Location = New Point(460, 50)
        lblPendingDelivery.Size = New Size(200, 20)
        lblPendingDelivery.Font = New Font("微软雅黑", 10, FontStyle.Bold)
        lblPendingDelivery.ForeColor = Color.Orange
        pnlStats.Controls.Add(lblPendingDelivery)

        ' 订单汇总数据表格
        dgvOrderSummary = New DataGridView()
        dgvOrderSummary.Dock = DockStyle.Fill
        dgvOrderSummary.ReadOnly = True
        dgvOrderSummary.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvOrderSummary.AllowUserToAddRows = False
        dgvOrderSummary.AllowUserToDeleteRows = False
        dgvOrderSummary.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabOrderSummary.Controls.Add(dgvOrderSummary)
    End Sub

    Private Sub SetupInvoiceTrackingTab()
        ' 筛选面板
        Dim pnlFilter As New Panel()
        pnlFilter.Height = 50
        pnlFilter.Dock = DockStyle.Top
        tabInvoiceTracking.Controls.Add(pnlFilter)

        Dim lblFromDate As New Label()
        lblFromDate.Text = "开始日期:"
        lblFromDate.Location = New Point(20, 15)
        lblFromDate.Size = New Size(70, 20)
        pnlFilter.Controls.Add(lblFromDate)

        dtpInvoiceFromDate = New DateTimePicker()
        dtpInvoiceFromDate.Location = New Point(100, 13)
        dtpInvoiceFromDate.Size = New Size(120, 23)
        dtpInvoiceFromDate.Format = DateTimePickerFormat.Short
        dtpInvoiceFromDate.Value = DateTime.Now.AddMonths(-1)
        pnlFilter.Controls.Add(dtpInvoiceFromDate)

        Dim lblToDate As New Label()
        lblToDate.Text = "结束日期:"
        lblToDate.Location = New Point(240, 15)
        lblToDate.Size = New Size(70, 20)
        pnlFilter.Controls.Add(lblToDate)

        dtpInvoiceToDate = New DateTimePicker()
        dtpInvoiceToDate.Location = New Point(320, 13)
        dtpInvoiceToDate.Size = New Size(120, 23)
        dtpInvoiceToDate.Format = DateTimePickerFormat.Short
        pnlFilter.Controls.Add(dtpInvoiceToDate)

        btnFilterInvoice = New Button()
        btnFilterInvoice.Text = "筛选"
        btnFilterInvoice.Location = New Point(460, 13)
        btnFilterInvoice.Size = New Size(80, 25)
        pnlFilter.Controls.Add(btnFilterInvoice)

        btnExportInvoice = New Button()
        btnExportInvoice.Text = "导出"
        btnExportInvoice.Location = New Point(550, 13)
        btnExportInvoice.Size = New Size(80, 25)
        pnlFilter.Controls.Add(btnExportInvoice)

        ' 发票跟踪数据表格
        dgvInvoiceTracking = New DataGridView()
        dgvInvoiceTracking.Dock = DockStyle.Fill
        dgvInvoiceTracking.ReadOnly = True
        dgvInvoiceTracking.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvInvoiceTracking.AllowUserToAddRows = False
        dgvInvoiceTracking.AllowUserToDeleteRows = False
        dgvInvoiceTracking.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabInvoiceTracking.Controls.Add(dgvInvoiceTracking)

        ' 事件处理
        AddHandler btnFilterInvoice.Click, AddressOf BtnFilterInvoice_Click
        AddHandler btnExportInvoice.Click, AddressOf BtnExportInvoice_Click
    End Sub

    Private Sub SetupDeliveryTrackingTab()
        ' 筛选面板
        Dim pnlFilter As New Panel()
        pnlFilter.Height = 50
        pnlFilter.Dock = DockStyle.Top
        tabDeliveryTracking.Controls.Add(pnlFilter)

        Dim lblFromDate As New Label()
        lblFromDate.Text = "开始日期:"
        lblFromDate.Location = New Point(20, 15)
        lblFromDate.Size = New Size(70, 20)
        pnlFilter.Controls.Add(lblFromDate)

        dtpDeliveryFromDate = New DateTimePicker()
        dtpDeliveryFromDate.Location = New Point(100, 13)
        dtpDeliveryFromDate.Size = New Size(120, 23)
        dtpDeliveryFromDate.Format = DateTimePickerFormat.Short
        dtpDeliveryFromDate.Value = DateTime.Now.AddMonths(-1)
        pnlFilter.Controls.Add(dtpDeliveryFromDate)

        Dim lblToDate As New Label()
        lblToDate.Text = "结束日期:"
        lblToDate.Location = New Point(240, 15)
        lblToDate.Size = New Size(70, 20)
        pnlFilter.Controls.Add(lblToDate)

        dtpDeliveryToDate = New DateTimePicker()
        dtpDeliveryToDate.Location = New Point(320, 13)
        dtpDeliveryToDate.Size = New Size(120, 23)
        dtpDeliveryToDate.Format = DateTimePickerFormat.Short
        pnlFilter.Controls.Add(dtpDeliveryToDate)

        btnFilterDelivery = New Button()
        btnFilterDelivery.Text = "筛选"
        btnFilterDelivery.Location = New Point(460, 13)
        btnFilterDelivery.Size = New Size(80, 25)
        pnlFilter.Controls.Add(btnFilterDelivery)

        btnExportDelivery = New Button()
        btnExportDelivery.Text = "导出"
        btnExportDelivery.Location = New Point(550, 13)
        btnExportDelivery.Size = New Size(80, 25)
        pnlFilter.Controls.Add(btnExportDelivery)

        ' 交货跟踪数据表格
        dgvDeliveryTracking = New DataGridView()
        dgvDeliveryTracking.Dock = DockStyle.Fill
        dgvDeliveryTracking.ReadOnly = True
        dgvDeliveryTracking.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvDeliveryTracking.AllowUserToAddRows = False
        dgvDeliveryTracking.AllowUserToDeleteRows = False
        dgvDeliveryTracking.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabDeliveryTracking.Controls.Add(dgvDeliveryTracking)

        ' 事件处理
        AddHandler btnFilterDelivery.Click, AddressOf BtnFilterDelivery_Click
        AddHandler btnExportDelivery.Click, AddressOf BtnExportDelivery_Click
    End Sub

    Private Sub SetupFinancialReportTab()
        ' 报表参数面板
        Dim pnlParams As New Panel()
        pnlParams.Height = 80
        pnlParams.Dock = DockStyle.Top
        tabFinancialReport.Controls.Add(pnlParams)

        Dim lblReportType As New Label()
        lblReportType.Text = "报表类型:"
        lblReportType.Location = New Point(20, 15)
        lblReportType.Size = New Size(70, 20)
        pnlParams.Controls.Add(lblReportType)

        cmbReportType = New ComboBox()
        cmbReportType.Location = New Point(100, 13)
        cmbReportType.Size = New Size(150, 23)
        cmbReportType.DropDownStyle = ComboBoxStyle.DropDownList
        cmbReportType.Items.AddRange({"月度财务汇总", "客户应收账款", "订单执行情况", "发票开具统计", "交货完成统计"})
        cmbReportType.SelectedIndex = 0
        pnlParams.Controls.Add(cmbReportType)

        Dim lblFromDate As New Label()
        lblFromDate.Text = "开始日期:"
        lblFromDate.Location = New Point(270, 15)
        lblFromDate.Size = New Size(70, 20)
        pnlParams.Controls.Add(lblFromDate)

        dtpReportFromDate = New DateTimePicker()
        dtpReportFromDate.Location = New Point(350, 13)
        dtpReportFromDate.Size = New Size(120, 23)
        dtpReportFromDate.Format = DateTimePickerFormat.Short
        dtpReportFromDate.Value = DateTime.Now.AddMonths(-1)
        pnlParams.Controls.Add(dtpReportFromDate)

        Dim lblToDate As New Label()
        lblToDate.Text = "结束日期:"
        lblToDate.Location = New Point(490, 15)
        lblToDate.Size = New Size(70, 20)
        pnlParams.Controls.Add(lblToDate)

        dtpReportToDate = New DateTimePicker()
        dtpReportToDate.Location = New Point(570, 13)
        dtpReportToDate.Size = New Size(120, 23)
        dtpReportToDate.Format = DateTimePickerFormat.Short
        pnlParams.Controls.Add(dtpReportToDate)

        btnGenerateReport = New Button()
        btnGenerateReport.Text = "生成报表"
        btnGenerateReport.Location = New Point(20, 45)
        btnGenerateReport.Size = New Size(100, 25)
        pnlParams.Controls.Add(btnGenerateReport)

        btnExportReport = New Button()
        btnExportReport.Text = "导出报表"
        btnExportReport.Location = New Point(130, 45)
        btnExportReport.Size = New Size(100, 25)
        pnlParams.Controls.Add(btnExportReport)

        ' 财务报表数据表格
        dgvFinancialReport = New DataGridView()
        dgvFinancialReport.Dock = DockStyle.Fill
        dgvFinancialReport.ReadOnly = True
        dgvFinancialReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvFinancialReport.AllowUserToAddRows = False
        dgvFinancialReport.AllowUserToDeleteRows = False
        dgvFinancialReport.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabFinancialReport.Controls.Add(dgvFinancialReport)

        ' 事件处理
        AddHandler btnGenerateReport.Click, AddressOf BtnGenerateReport_Click
        AddHandler btnExportReport.Click, AddressOf BtnExportReport_Click
    End Sub

    Private Sub LoadData()
        LoadOrderSummary()
        LoadInvoiceTracking()
        LoadDeliveryTracking()
    End Sub

    Private Sub LoadOrderSummary()
        Try
            ' 加载订单汇总数据
            Dim query As String = "
                SELECT
                    o.order_number AS '订单编号',
                    c.customer_name AS '客户名称',
                    o.order_date AS '订单日期',
                    o.delivery_date AS '交货日期',
                    CASE o.order_status
                        WHEN 'pending' THEN '待处理'
                        WHEN 'confirmed' THEN '已确认'
                        WHEN 'in_production' THEN '生产中'
                        WHEN 'ready' THEN '待发货'
                        WHEN 'shipped' THEN '已发货'
                        WHEN 'delivered' THEN '已交货'
                        WHEN 'completed' THEN '已完成'
                        WHEN 'cancelled' THEN '已取消'
                        ELSE o.order_status
                    END AS '订单状态',
                    o.total_amount AS '订单总金额',
                    COALESCE(SUM(od.invoiced_amount), 0) AS '已开票金额',
                    (o.total_amount - COALESCE(SUM(od.invoiced_amount), 0)) AS '未开票金额',
                    COALESCE(SUM(od.delivered_amount), 0) AS '已交货金额',
                    (o.total_amount - COALESCE(SUM(od.delivered_amount), 0)) AS '待交货金额',
                    ROUND((COALESCE(SUM(od.invoiced_amount), 0) / o.total_amount * 100), 2) AS '开票进度%',
                    ROUND((COALESCE(SUM(od.delivered_amount), 0) / o.total_amount * 100), 2) AS '交货进度%'
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                LEFT JOIN order_details od ON o.id = od.order_id
                WHERE o.order_status NOT IN ('cancelled')
                GROUP BY o.id, o.order_number, c.customer_name, o.order_date, o.delivery_date, o.order_status, o.total_amount
                ORDER BY o.order_date DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)
            dgvOrderSummary.DataSource = dt

            ' 设置数字列格式
            For Each column As DataGridViewColumn In dgvOrderSummary.Columns
                If column.Name.Contains("金额") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                ElseIf column.Name.Contains("进度") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                End If
            Next

            ' 设置行颜色
            For Each row As DataGridViewRow In dgvOrderSummary.Rows
                Select Case row.Cells("订单状态").Value?.ToString()
                    Case "待处理", "已确认"
                        row.DefaultCellStyle.BackColor = Color.LightYellow
                    Case "生产中", "待发货"
                        row.DefaultCellStyle.BackColor = Color.LightBlue
                    Case "已交货", "已完成"
                        row.DefaultCellStyle.BackColor = Color.LightGreen
                End Select
            Next

            ' 更新统计信息
            UpdateSummaryStatistics(dt)

        Catch ex As Exception
            MessageBox.Show($"加载订单汇总数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateSummaryStatistics(dt As DataTable)
        Try
            Dim totalOrders As Integer = dt.Rows.Count
            Dim totalAmount As Decimal = 0
            Dim invoicedAmount As Decimal = 0
            Dim deliveredAmount As Decimal = 0

            For Each row As DataRow In dt.Rows
                totalAmount += Convert.ToDecimal(row("订单总金额"))
                invoicedAmount += Convert.ToDecimal(row("已开票金额"))
                deliveredAmount += Convert.ToDecimal(row("已交货金额"))
            Next

            Dim outstandingAmount As Decimal = totalAmount - invoicedAmount
            Dim pendingDelivery As Decimal = totalAmount - deliveredAmount

            lblTotalOrders.Text = $"订单总数: {totalOrders}"
            lblTotalAmount.Text = $"订单总金额: ¥{totalAmount:N2}"
            lblInvoicedAmount.Text = $"已开票金额: ¥{invoicedAmount:N2}"
            lblOutstandingAmount.Text = $"未开票金额: ¥{outstandingAmount:N2}"
            lblDeliveredAmount.Text = $"已交货金额: ¥{deliveredAmount:N2}"
            lblPendingDelivery.Text = $"待交货金额: ¥{pendingDelivery:N2}"

        Catch ex As Exception
            MessageBox.Show($"更新统计信息失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadInvoiceTracking()
        Try
            Dim query As String = "
                SELECT
                    o.order_number AS '订单编号',
                    c.customer_name AS '客户名称',
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    od.order_quantity AS '订单数量',
                    od.invoiced_quantity AS '已开票数量',
                    (od.order_quantity - od.invoiced_quantity) AS '未开票数量',
                    od.unit_price_with_tax AS '含税单价',
                    od.line_total AS '行总金额',
                    od.invoiced_amount AS '已开票金额',
                    (od.line_total - od.invoiced_amount) AS '未开票金额',
                    ROUND((od.invoiced_amount / od.line_total * 100), 2) AS '开票进度%',
                    o.order_date AS '订单日期'
                FROM order_details od
                INNER JOIN orders o ON od.order_id = o.id
                INNER JOIN customers c ON o.customer_id = c.id
                INNER JOIN materials m ON od.material_id = m.id
                WHERE o.order_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')
                ORDER BY o.order_date DESC, o.order_number, od.line_number"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"from_date", dtpInvoiceFromDate.Value.Date},
                {"to_date", dtpInvoiceToDate.Value.Date.AddDays(1).AddSeconds(-1)}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvInvoiceTracking.DataSource = dt

            ' 设置数字列格式
            For Each column As DataGridViewColumn In dgvInvoiceTracking.Columns
                If column.Name.Contains("金额") OrElse column.Name.Contains("单价") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                ElseIf column.Name.Contains("数量") Then
                    column.DefaultCellStyle.Format = "N3"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                ElseIf column.Name.Contains("进度") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                End If
            Next

            ' 设置行颜色（根据开票进度）
            For Each row As DataGridViewRow In dgvInvoiceTracking.Rows
                Dim progress As Decimal = Convert.ToDecimal(row.Cells("开票进度%").Value)
                If progress = 100 Then
                    row.DefaultCellStyle.BackColor = Color.LightGreen
                ElseIf progress >= 50 Then
                    row.DefaultCellStyle.BackColor = Color.LightYellow
                Else
                    row.DefaultCellStyle.BackColor = Color.LightPink
                End If
            Next

        Catch ex As Exception
            MessageBox.Show($"加载发票跟踪数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadDeliveryTracking()
        Try
            Dim query As String = "
                SELECT
                    o.order_number AS '订单编号',
                    c.customer_name AS '客户名称',
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    od.order_quantity AS '订单数量',
                    od.delivered_quantity AS '已交货数量',
                    (od.order_quantity - od.delivered_quantity) AS '待交货数量',
                    od.unit_price_with_tax AS '含税单价',
                    od.line_total AS '行总金额',
                    od.delivered_amount AS '已交货金额',
                    (od.line_total - od.delivered_amount) AS '待交货金额',
                    ROUND((od.delivered_amount / od.line_total * 100), 2) AS '交货进度%',
                    o.delivery_date AS '计划交货日期',
                    CASE
                        WHEN o.delivery_date < CURDATE() AND od.delivered_quantity < od.order_quantity THEN '逾期'
                        WHEN o.delivery_date = CURDATE() AND od.delivered_quantity < od.order_quantity THEN '今日到期'
                        WHEN od.delivered_quantity >= od.order_quantity THEN '已完成'
                        ELSE '正常'
                    END AS '交货状态'
                FROM order_details od
                INNER JOIN orders o ON od.order_id = o.id
                INNER JOIN customers c ON o.customer_id = c.id
                INNER JOIN materials m ON od.material_id = m.id
                WHERE o.delivery_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')
                ORDER BY o.delivery_date ASC, o.order_number, od.line_number"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"from_date", dtpDeliveryFromDate.Value.Date},
                {"to_date", dtpDeliveryToDate.Value.Date.AddDays(1).AddSeconds(-1)}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvDeliveryTracking.DataSource = dt

            ' 设置数字列格式
            For Each column As DataGridViewColumn In dgvDeliveryTracking.Columns
                If column.Name.Contains("金额") OrElse column.Name.Contains("单价") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                ElseIf column.Name.Contains("数量") Then
                    column.DefaultCellStyle.Format = "N3"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                ElseIf column.Name.Contains("进度") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                End If
            Next

            ' 设置行颜色（根据交货状态）
            For Each row As DataGridViewRow In dgvDeliveryTracking.Rows
                Select Case row.Cells("交货状态").Value?.ToString()
                    Case "逾期"
                        row.DefaultCellStyle.BackColor = Color.LightPink
                        row.DefaultCellStyle.ForeColor = Color.DarkRed
                    Case "今日到期"
                        row.DefaultCellStyle.BackColor = Color.LightYellow
                        row.DefaultCellStyle.ForeColor = Color.DarkOrange
                    Case "已完成"
                        row.DefaultCellStyle.BackColor = Color.LightGreen
                    Case "正常"
                        row.DefaultCellStyle.BackColor = Color.White
                End Select
            Next

        Catch ex As Exception
            MessageBox.Show($"加载交货跟踪数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 按钮事件处理
    Private Sub BtnFilterInvoice_Click(sender As Object, e As EventArgs)
        LoadInvoiceTracking()
    End Sub

    Private Sub BtnExportInvoice_Click(sender As Object, e As EventArgs)
        MessageBox.Show("发票数据导出功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BtnFilterDelivery_Click(sender As Object, e As EventArgs)
        LoadDeliveryTracking()
    End Sub

    Private Sub BtnExportDelivery_Click(sender As Object, e As EventArgs)
        MessageBox.Show("交货数据导出功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BtnGenerateReport_Click(sender As Object, e As EventArgs)
        Select Case cmbReportType.SelectedIndex
            Case 0 ' 月度财务汇总
                GenerateMonthlyFinancialSummary()
            Case 1 ' 客户应收账款
                GenerateCustomerReceivables()
            Case 2 ' 订单执行情况
                GenerateOrderExecutionReport()
            Case 3 ' 发票开具统计
                GenerateInvoiceStatistics()
            Case 4 ' 交货完成统计
                GenerateDeliveryStatistics()
            Case Else
                MessageBox.Show("请选择报表类型！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End Select
    End Sub

    Private Sub BtnExportReport_Click(sender As Object, e As EventArgs)
        MessageBox.Show("报表导出功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub GenerateMonthlyFinancialSummary()
        Try
            Dim query As String = "
                SELECT
                    DATE_FORMAT(o.order_date, '%Y-%m') AS '月份',
                    COUNT(DISTINCT o.id) AS '订单数量',
                    SUM(o.total_amount) AS '订单总金额',
                    SUM(COALESCE(od_sum.invoiced_amount, 0)) AS '已开票金额',
                    SUM(o.total_amount - COALESCE(od_sum.invoiced_amount, 0)) AS '未开票金额',
                    SUM(COALESCE(od_sum.delivered_amount, 0)) AS '已交货金额',
                    SUM(o.total_amount - COALESCE(od_sum.delivered_amount, 0)) AS '待交货金额',
                    ROUND(AVG(COALESCE(od_sum.invoiced_amount, 0) / o.total_amount * 100), 2) AS '平均开票进度%',
                    ROUND(AVG(COALESCE(od_sum.delivered_amount, 0) / o.total_amount * 100), 2) AS '平均交货进度%'
                FROM orders o
                LEFT JOIN (
                    SELECT
                        order_id,
                        SUM(invoiced_amount) as invoiced_amount,
                        SUM(delivered_amount) as delivered_amount
                    FROM order_details
                    GROUP BY order_id
                ) od_sum ON o.id = od_sum.order_id
                WHERE o.order_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')
                GROUP BY DATE_FORMAT(o.order_date, '%Y-%m')
                ORDER BY DATE_FORMAT(o.order_date, '%Y-%m') DESC"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"from_date", dtpReportFromDate.Value.Date},
                {"to_date", dtpReportToDate.Value.Date.AddDays(1).AddSeconds(-1)}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvFinancialReport.DataSource = dt

            ' 设置数字列格式
            For Each column As DataGridViewColumn In dgvFinancialReport.Columns
                If column.Name.Contains("金额") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                ElseIf column.Name.Contains("数量") Then
                    column.DefaultCellStyle.Format = "N0"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                ElseIf column.Name.Contains("进度") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                End If
            Next

        Catch ex As Exception
            MessageBox.Show($"生成月度财务汇总失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub GenerateCustomerReceivables()
        Try
            Dim query As String = "
                SELECT
                    c.customer_name AS '客户名称',
                    COUNT(DISTINCT o.id) AS '订单数量',
                    SUM(o.total_amount) AS '订单总金额',
                    SUM(COALESCE(od_sum.invoiced_amount, 0)) AS '已开票金额',
                    SUM(o.total_amount - COALESCE(od_sum.invoiced_amount, 0)) AS '应收账款',
                    ROUND(SUM(COALESCE(od_sum.invoiced_amount, 0)) / SUM(o.total_amount) * 100, 2) AS '开票比例%',
                    MIN(o.order_date) AS '最早订单日期',
                    MAX(o.order_date) AS '最新订单日期'
                FROM orders o
                INNER JOIN customers c ON o.customer_id = c.id
                LEFT JOIN (
                    SELECT
                        order_id,
                        SUM(invoiced_amount) as invoiced_amount
                    FROM order_details
                    GROUP BY order_id
                ) od_sum ON o.id = od_sum.order_id
                WHERE o.order_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')
                GROUP BY c.id, c.customer_name
                HAVING SUM(o.total_amount - COALESCE(od_sum.invoiced_amount, 0)) > 0
                ORDER BY SUM(o.total_amount - COALESCE(od_sum.invoiced_amount, 0)) DESC"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"from_date", dtpReportFromDate.Value.Date},
                {"to_date", dtpReportToDate.Value.Date.AddDays(1).AddSeconds(-1)}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvFinancialReport.DataSource = dt

            ' 设置数字列格式
            For Each column As DataGridViewColumn In dgvFinancialReport.Columns
                If column.Name.Contains("金额") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                ElseIf column.Name.Contains("数量") Then
                    column.DefaultCellStyle.Format = "N0"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                ElseIf column.Name.Contains("比例") Then
                    column.DefaultCellStyle.Format = "N2"
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                End If
            Next

        Catch ex As Exception
            MessageBox.Show($"生成客户应收账款报表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub GenerateOrderExecutionReport()
        Try
            ' 订单执行情况报表
            Dim query As String = "
                SELECT
                    o.order_number AS '订单编号',
                    c.customer_name AS '客户名称',
                    o.order_date AS '订单日期',
                    o.delivery_date AS '计划交货日期',
                    CASE o.order_status
                        WHEN 'pending' THEN '待处理'
                        WHEN 'confirmed' THEN '已确认'
                        WHEN 'in_production' THEN '生产中'
                        WHEN 'ready' THEN '待发货'
                        WHEN 'shipped' THEN '已发货'
                        WHEN 'delivered' THEN '已交货'
                        WHEN 'completed' THEN '已完成'
                        ELSE o.order_status
                    END AS '订单状态',
                    o.total_amount AS '订单金额',
                    COALESCE(od_sum.invoiced_amount, 0) AS '已开票金额',
                    COALESCE(od_sum.delivered_amount, 0) AS '已交货金额',
                    ROUND(COALESCE(od_sum.invoiced_amount, 0) / o.total_amount * 100, 2) AS '开票进度%',
                    ROUND(COALESCE(od_sum.delivered_amount, 0) / o.total_amount * 100, 2) AS '交货进度%',
                    DATEDIFF(CURDATE(), o.delivery_date) AS '逾期天数'
                FROM orders o
                INNER JOIN customers c ON o.customer_id = c.id
                LEFT JOIN (
                    SELECT
                        order_id,
                        SUM(invoiced_amount) as invoiced_amount,
                        SUM(delivered_amount) as delivered_amount
                    FROM order_details
                    GROUP BY order_id
                ) od_sum ON o.id = od_sum.order_id
                WHERE o.order_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')
                ORDER BY o.delivery_date ASC"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"from_date", dtpReportFromDate.Value.Date},
                {"to_date", dtpReportToDate.Value.Date.AddDays(1).AddSeconds(-1)}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvFinancialReport.DataSource = dt

            ' 设置行颜色（根据逾期情况）
            For Each row As DataGridViewRow In dgvFinancialReport.Rows
                Dim overdueDays As Integer = Convert.ToInt32(row.Cells("逾期天数").Value)
                If overdueDays > 0 Then
                    row.DefaultCellStyle.BackColor = Color.LightPink
                ElseIf overdueDays = 0 Then
                    row.DefaultCellStyle.BackColor = Color.LightYellow
                End If
            Next

        Catch ex As Exception
            MessageBox.Show($"生成订单执行情况报表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub GenerateInvoiceStatistics()
        Try
            ' 发票开具统计报表
            Dim query As String = "
                SELECT
                    DATE_FORMAT(o.order_date, '%Y-%m') AS '月份',
                    c.customer_name AS '客户名称',
                    COUNT(DISTINCT o.id) AS '订单数量',
                    SUM(od.order_quantity) AS '订单总数量',
                    SUM(od.invoiced_quantity) AS '已开票数量',
                    SUM(od.order_quantity - od.invoiced_quantity) AS '未开票数量',
                    SUM(od.line_total) AS '订单总金额',
                    SUM(od.invoiced_amount) AS '已开票金额',
                    SUM(od.line_total - od.invoiced_amount) AS '未开票金额',
                    ROUND(SUM(od.invoiced_amount) / SUM(od.line_total) * 100, 2) AS '开票比例%'
                FROM orders o
                INNER JOIN customers c ON o.customer_id = c.id
                INNER JOIN order_details od ON o.id = od.order_id
                WHERE o.order_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')
                GROUP BY DATE_FORMAT(o.order_date, '%Y-%m'), c.id, c.customer_name
                ORDER BY DATE_FORMAT(o.order_date, '%Y-%m') DESC, c.customer_name"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"from_date", dtpReportFromDate.Value.Date},
                {"to_date", dtpReportToDate.Value.Date.AddDays(1).AddSeconds(-1)}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvFinancialReport.DataSource = dt

        Catch ex As Exception
            MessageBox.Show($"生成发票开具统计失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub GenerateDeliveryStatistics()
        Try
            ' 交货完成统计报表
            Dim query As String = "
                SELECT
                    DATE_FORMAT(o.delivery_date, '%Y-%m') AS '交货月份',
                    c.customer_name AS '客户名称',
                    COUNT(DISTINCT o.id) AS '订单数量',
                    SUM(od.order_quantity) AS '订单总数量',
                    SUM(od.delivered_quantity) AS '已交货数量',
                    SUM(od.order_quantity - od.delivered_quantity) AS '待交货数量',
                    SUM(od.line_total) AS '订单总金额',
                    SUM(od.delivered_amount) AS '已交货金额',
                    SUM(od.line_total - od.delivered_amount) AS '待交货金额',
                    ROUND(SUM(od.delivered_amount) / SUM(od.line_total) * 100, 2) AS '交货比例%',
                    COUNT(CASE WHEN o.delivery_date < CURDATE() AND od.delivered_quantity < od.order_quantity THEN 1 END) AS '逾期订单数'
                FROM orders o
                INNER JOIN customers c ON o.customer_id = c.id
                INNER JOIN order_details od ON o.id = od.order_id
                WHERE o.delivery_date BETWEEN @from_date AND @to_date
                AND o.order_status NOT IN ('cancelled')
                GROUP BY DATE_FORMAT(o.delivery_date, '%Y-%m'), c.id, c.customer_name
                ORDER BY DATE_FORMAT(o.delivery_date, '%Y-%m') DESC, c.customer_name"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"from_date", dtpReportFromDate.Value.Date},
                {"to_date", dtpReportToDate.Value.Date.AddDays(1).AddSeconds(-1)}
            }

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvFinancialReport.DataSource = dt

        Catch ex As Exception
            MessageBox.Show($"生成交货完成统计失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ApplyPermissions()
        ' 检查基本查看权限
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewOrders) Then
            MessageBox.Show("您没有权限访问订单财务分析功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        ' 检查财务查看权限
        Dim canViewFinancial As Boolean = PermissionManager.HasPermission(PermissionManager.Permission.ViewFinancial)

        If Not canViewFinancial Then
            ' 如果没有财务权限，隐藏财务相关的选项卡和信息
            tabControl.TabPages.Remove(tabFinancialReport)

            ' 隐藏金额相关的统计信息
            lblTotalAmount.Visible = False
            lblInvoicedAmount.Visible = False
            lblOutstandingAmount.Visible = False
            lblDeliveredAmount.Visible = False
            lblPendingDelivery.Visible = False

            Me.Text = "订单分析 (无财务权限)"
        End If
    End Sub
End Class
