Imports System.Windows.Forms
Imports MySql.Data.MySqlClient

Public Class OrderDetailForm
    Inherits Form

    Private orderId As Integer
    Private dgvDetails As DataGridView
    Private txtLineNumber As TextBox
    Private txtWorkOrderNumber As TextBox
    Private cmbMaterial As ComboBox
    Private cmbLocation As ComboBox
    Private txtOrderQuantity As TextBox
    Private txtInvoicedQuantity As TextBox
    Private txtDeliveredQuantity As TextBox
    Private txtUnitPriceWithTax As TextBox
    Private txtUnitPriceWithoutTax As TextBox
    Private txtLineTotal As TextBox
    Private txtInvoicedAmount As TextBox
    Private txtDeliveredAmount As TextBox

    Private btnAdd As Button
    Private btnEdit As Button
    Private btnDelete As Button
    Private btnRefresh As Button
    Private btnClose As Button
    Private btnCalculate As Button

    Private lblOrderInfo As Label
    Private currentDetailId As Integer = 0

    Public Sub New(orderIdParam As Integer)
        orderId = orderIdParam
        InitializeComponent()
        SetupUI()
        LoadOrderInfo()
        LoadData()
        LoadMaterials()
        LoadLocations()
    End Sub

    Private Sub SetupUI()
        Me.Text = "订单明细管理"
        Me.Size = New Size(1400, 800)
        Me.StartPosition = FormStartPosition.CenterScreen

        ' 订单信息标签
        lblOrderInfo = New Label()
        lblOrderInfo.Location = New Point(12, 12)
        lblOrderInfo.Size = New Size(1360, 30)
        lblOrderInfo.Font = New Font("Microsoft YaHei", 10, FontStyle.Bold)
        lblOrderInfo.ForeColor = Color.Blue
        Me.Controls.Add(lblOrderInfo)

        ' 数据表格
        dgvDetails = New DataGridView()
        dgvDetails.Location = New Point(12, 50)
        dgvDetails.Size = New Size(1360, 350)
        dgvDetails.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        dgvDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvDetails.MultiSelect = False
        dgvDetails.ReadOnly = True
        dgvDetails.AllowUserToAddRows = False
        dgvDetails.AllowUserToDeleteRows = False
        Me.Controls.Add(dgvDetails)

        ' 第一行控件
        Dim lblLineNumber As New Label()
        lblLineNumber.Text = "行号:"
        lblLineNumber.Location = New Point(12, 420)
        lblLineNumber.Size = New Size(50, 23)
        Me.Controls.Add(lblLineNumber)

        txtLineNumber = New TextBox()
        txtLineNumber.Location = New Point(70, 418)
        txtLineNumber.Size = New Size(80, 23)
        Me.Controls.Add(txtLineNumber)

        Dim lblWorkOrder As New Label()
        lblWorkOrder.Text = "工单号:"
        lblWorkOrder.Location = New Point(170, 420)
        lblWorkOrder.Size = New Size(50, 23)
        Me.Controls.Add(lblWorkOrder)

        txtWorkOrderNumber = New TextBox()
        txtWorkOrderNumber.Location = New Point(230, 418)
        txtWorkOrderNumber.Size = New Size(120, 23)
        Me.Controls.Add(txtWorkOrderNumber)

        Dim lblMaterial As New Label()
        lblMaterial.Text = "物料:"
        lblMaterial.Location = New Point(370, 420)
        lblMaterial.Size = New Size(50, 23)
        Me.Controls.Add(lblMaterial)

        cmbMaterial = New ComboBox()
        cmbMaterial.Location = New Point(430, 418)
        cmbMaterial.Size = New Size(200, 23)
        cmbMaterial.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbMaterial)

        Dim lblLocation As New Label()
        lblLocation.Text = "库位:"
        lblLocation.Location = New Point(650, 420)
        lblLocation.Size = New Size(50, 23)
        Me.Controls.Add(lblLocation)

        cmbLocation = New ComboBox()
        cmbLocation.Location = New Point(710, 418)
        cmbLocation.Size = New Size(120, 23)
        cmbLocation.DropDownStyle = ComboBoxStyle.DropDownList
        Me.Controls.Add(cmbLocation)

        ' 第二行控件
        Dim lblOrderQuantity As New Label()
        lblOrderQuantity.Text = "订单数量:"
        lblOrderQuantity.Location = New Point(12, 460)
        lblOrderQuantity.Size = New Size(70, 23)
        Me.Controls.Add(lblOrderQuantity)

        txtOrderQuantity = New TextBox()
        txtOrderQuantity.Location = New Point(90, 458)
        txtOrderQuantity.Size = New Size(100, 23)
        Me.Controls.Add(txtOrderQuantity)

        Dim lblInvoicedQuantity As New Label()
        lblInvoicedQuantity.Text = "已开票数量:"
        lblInvoicedQuantity.Location = New Point(210, 460)
        lblInvoicedQuantity.Size = New Size(80, 23)
        Me.Controls.Add(lblInvoicedQuantity)

        txtInvoicedQuantity = New TextBox()
        txtInvoicedQuantity.Location = New Point(300, 458)
        txtInvoicedQuantity.Size = New Size(100, 23)
        txtInvoicedQuantity.Text = "0"
        Me.Controls.Add(txtInvoicedQuantity)

        Dim lblDeliveredQuantity As New Label()
        lblDeliveredQuantity.Text = "已交货数量:"
        lblDeliveredQuantity.Location = New Point(420, 460)
        lblDeliveredQuantity.Size = New Size(80, 23)
        Me.Controls.Add(lblDeliveredQuantity)

        txtDeliveredQuantity = New TextBox()
        txtDeliveredQuantity.Location = New Point(510, 458)
        txtDeliveredQuantity.Size = New Size(100, 23)
        txtDeliveredQuantity.Text = "0"
        Me.Controls.Add(txtDeliveredQuantity)

        ' 第三行控件
        Dim lblUnitPriceWithTax As New Label()
        lblUnitPriceWithTax.Text = "含税单价:"
        lblUnitPriceWithTax.Location = New Point(12, 500)
        lblUnitPriceWithTax.Size = New Size(70, 23)
        Me.Controls.Add(lblUnitPriceWithTax)

        txtUnitPriceWithTax = New TextBox()
        txtUnitPriceWithTax.Location = New Point(90, 498)
        txtUnitPriceWithTax.Size = New Size(100, 23)
        Me.Controls.Add(txtUnitPriceWithTax)

        Dim lblUnitPriceWithoutTax As New Label()
        lblUnitPriceWithoutTax.Text = "不含税单价:"
        lblUnitPriceWithoutTax.Location = New Point(210, 500)
        lblUnitPriceWithoutTax.Size = New Size(80, 23)
        Me.Controls.Add(lblUnitPriceWithoutTax)

        txtUnitPriceWithoutTax = New TextBox()
        txtUnitPriceWithoutTax.Location = New Point(300, 498)
        txtUnitPriceWithoutTax.Size = New Size(100, 23)
        txtUnitPriceWithoutTax.ReadOnly = True
        Me.Controls.Add(txtUnitPriceWithoutTax)

        Dim lblLineTotal As New Label()
        lblLineTotal.Text = "行总金额:"
        lblLineTotal.Location = New Point(420, 500)
        lblLineTotal.Size = New Size(70, 23)
        Me.Controls.Add(lblLineTotal)

        txtLineTotal = New TextBox()
        txtLineTotal.Location = New Point(500, 498)
        txtLineTotal.Size = New Size(100, 23)
        txtLineTotal.ReadOnly = True
        Me.Controls.Add(txtLineTotal)

        ' 第四行控件
        Dim lblInvoicedAmount As New Label()
        lblInvoicedAmount.Text = "已开票金额:"
        lblInvoicedAmount.Location = New Point(12, 540)
        lblInvoicedAmount.Size = New Size(80, 23)
        Me.Controls.Add(lblInvoicedAmount)

        txtInvoicedAmount = New TextBox()
        txtInvoicedAmount.Location = New Point(100, 538)
        txtInvoicedAmount.Size = New Size(100, 23)
        txtInvoicedAmount.Text = "0"
        Me.Controls.Add(txtInvoicedAmount)

        Dim lblDeliveredAmount As New Label()
        lblDeliveredAmount.Text = "已交货金额:"
        lblDeliveredAmount.Location = New Point(220, 540)
        lblDeliveredAmount.Size = New Size(80, 23)
        Me.Controls.Add(lblDeliveredAmount)

        txtDeliveredAmount = New TextBox()
        txtDeliveredAmount.Location = New Point(310, 538)
        txtDeliveredAmount.Size = New Size(100, 23)
        txtDeliveredAmount.Text = "0"
        Me.Controls.Add(txtDeliveredAmount)

        ' 按钮
        btnAdd = New Button()
        btnAdd.Text = "新增"
        btnAdd.Location = New Point(12, 580)
        btnAdd.Size = New Size(80, 30)
        Me.Controls.Add(btnAdd)

        btnEdit = New Button()
        btnEdit.Text = "修改"
        btnEdit.Location = New Point(102, 580)
        btnEdit.Size = New Size(80, 30)
        Me.Controls.Add(btnEdit)

        btnDelete = New Button()
        btnDelete.Text = "删除"
        btnDelete.Location = New Point(192, 580)
        btnDelete.Size = New Size(80, 30)
        Me.Controls.Add(btnDelete)

        btnRefresh = New Button()
        btnRefresh.Text = "刷新"
        btnRefresh.Location = New Point(282, 580)
        btnRefresh.Size = New Size(80, 30)
        Me.Controls.Add(btnRefresh)

        btnCalculate = New Button()
        btnCalculate.Text = "计算"
        btnCalculate.Location = New Point(372, 580)
        btnCalculate.Size = New Size(80, 30)
        Me.Controls.Add(btnCalculate)

        btnClose = New Button()
        btnClose.Text = "关闭"
        btnClose.Location = New Point(462, 580)
        btnClose.Size = New Size(80, 30)
        Me.Controls.Add(btnClose)

        ' 事件处理
        AddHandler dgvDetails.SelectionChanged, AddressOf DgvDetails_SelectionChanged
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        AddHandler btnCalculate.Click, AddressOf BtnCalculate_Click
        AddHandler btnClose.Click, AddressOf BtnClose_Click
        AddHandler txtUnitPriceWithTax.TextChanged, AddressOf CalculatePrices
        AddHandler txtOrderQuantity.TextChanged, AddressOf CalculatePrices
    End Sub

    Private Sub LoadOrderInfo()
        Try
            Dim query As String = "
                SELECT o.order_number, c.customer_name, o.order_date, o.delivery_date, o.order_status
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                WHERE o.id = @id"

            Dim parameters As New Dictionary(Of String, Object) From {{"id", orderId}}
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)

            If dt.Rows.Count > 0 Then
                Dim row As DataRow = dt.Rows(0)
                lblOrderInfo.Text = $"订单编号: {row("order_number")} | 客户: {row("customer_name")} | 订单日期: {Convert.ToDateTime(row("order_date")).ToString("yyyy-MM-dd")} | 交货日期: {Convert.ToDateTime(row("delivery_date")).ToString("yyyy-MM-dd")} | 状态: {GetStatusText(row("order_status").ToString())}"
            End If

        Catch ex As Exception
            MessageBox.Show($"加载订单信息失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadData()
        Try
            Dim query As String = "
                SELECT
                    od.id,
                    od.line_number AS '行号',
                    od.work_order_number AS '工单号',
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    l.location_code AS '库位',
                    od.order_quantity AS '订单数量',
                    od.invoiced_quantity AS '已开票数量',
                    od.delivered_quantity AS '已交货数量',
                    od.unit_price_with_tax AS '含税单价',
                    od.unit_price_without_tax AS '不含税单价',
                    od.line_total AS '行总金额',
                    od.invoiced_amount AS '已开票金额',
                    od.delivered_amount AS '已交货金额'
                FROM order_details od
                LEFT JOIN materials m ON od.material_id = m.id
                LEFT JOIN locations l ON od.location_id = l.id
                WHERE od.order_id = @order_id
                ORDER BY od.line_number"

            Dim parameters As New Dictionary(Of String, Object) From {{"order_id", orderId}}
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query, parameters)
            dgvDetails.DataSource = dt

            ' 隐藏ID列
            If dgvDetails.Columns.Contains("id") Then
                dgvDetails.Columns("id").Visible = False
            End If

            ' 设置列宽
            If dgvDetails.Columns.Count > 0 Then
                dgvDetails.Columns("行号").Width = 60
                dgvDetails.Columns("工单号").Width = 100
                dgvDetails.Columns("物料编码").Width = 120
                dgvDetails.Columns("物料名称").Width = 150
                dgvDetails.Columns("库位").Width = 80
                dgvDetails.Columns("订单数量").Width = 80
                dgvDetails.Columns("已开票数量").Width = 80
                dgvDetails.Columns("已交货数量").Width = 80
                dgvDetails.Columns("含税单价").Width = 80
                dgvDetails.Columns("不含税单价").Width = 80
                dgvDetails.Columns("行总金额").Width = 100
                dgvDetails.Columns("已开票金额").Width = 100
                dgvDetails.Columns("已交货金额").Width = 100
            End If

        Catch ex As Exception
            MessageBox.Show($"加载明细数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadMaterials()
        Try
            Dim query As String = "SELECT id, CONCAT(material_code, ' - ', material_name) AS display_name FROM materials WHERE is_active = TRUE ORDER BY material_code"
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)

            cmbMaterial.DisplayMember = "display_name"
            cmbMaterial.ValueMember = "id"
            cmbMaterial.DataSource = dt

        Catch ex As Exception
            MessageBox.Show($"加载物料数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadLocations()
        Try
            Dim query As String = "SELECT id, location_code FROM locations WHERE is_active = TRUE ORDER BY location_code"
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(query)

            cmbLocation.DisplayMember = "location_code"
            cmbLocation.ValueMember = "id"
            cmbLocation.DataSource = dt

        Catch ex As Exception
            MessageBox.Show($"加载库位数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function GetStatusText(status As String) As String
        Select Case status
            Case "pending" : Return "待处理"
            Case "confirmed" : Return "已确认"
            Case "in_production" : Return "生产中"
            Case "ready" : Return "待发货"
            Case "shipped" : Return "已发货"
            Case "delivered" : Return "已交货"
            Case "completed" : Return "已完成"
            Case "cancelled" : Return "已取消"
            Case Else : Return status
        End Select
    End Function

    Private Sub DgvDetails_SelectionChanged(sender As Object, e As EventArgs)
        If dgvDetails.SelectedRows.Count > 0 Then
            Dim row As DataGridViewRow = dgvDetails.SelectedRows(0)
            currentDetailId = Convert.ToInt32(row.Cells("id").Value)

            txtLineNumber.Text = row.Cells("行号").Value?.ToString()
            txtWorkOrderNumber.Text = row.Cells("工单号").Value?.ToString()

            ' 设置物料
            Dim materialCode As String = row.Cells("物料编码").Value?.ToString()
            For i As Integer = 0 To cmbMaterial.Items.Count - 1
                Dim item As DataRowView = CType(cmbMaterial.Items(i), DataRowView)
                If item("display_name").ToString().StartsWith(materialCode) Then
                    cmbMaterial.SelectedIndex = i
                    Exit For
                End If
            Next

            ' 设置库位
            Dim locationCode As String = row.Cells("库位").Value?.ToString()
            For i As Integer = 0 To cmbLocation.Items.Count - 1
                Dim item As DataRowView = CType(cmbLocation.Items(i), DataRowView)
                If item("location_code").ToString() = locationCode Then
                    cmbLocation.SelectedIndex = i
                    Exit For
                End If
            Next

            txtOrderQuantity.Text = row.Cells("订单数量").Value?.ToString()
            txtInvoicedQuantity.Text = row.Cells("已开票数量").Value?.ToString()
            txtDeliveredQuantity.Text = row.Cells("已交货数量").Value?.ToString()
            txtUnitPriceWithTax.Text = row.Cells("含税单价").Value?.ToString()
            txtUnitPriceWithoutTax.Text = row.Cells("不含税单价").Value?.ToString()
            txtLineTotal.Text = row.Cells("行总金额").Value?.ToString()
            txtInvoicedAmount.Text = row.Cells("已开票金额").Value?.ToString()
            txtDeliveredAmount.Text = row.Cells("已交货金额").Value?.ToString()
        End If
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        If ValidateInput() Then
            Try
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"order_id", orderId},
                    {"line_number", txtLineNumber.Text.Trim()},
                    {"work_order_number", txtWorkOrderNumber.Text.Trim()},
                    {"material_id", cmbMaterial.SelectedValue},
                    {"location_id", cmbLocation.SelectedValue},
                    {"order_quantity", Convert.ToDecimal(txtOrderQuantity.Text)},
                    {"invoiced_quantity", Convert.ToDecimal(txtInvoicedQuantity.Text)},
                    {"delivered_quantity", Convert.ToDecimal(txtDeliveredQuantity.Text)},
                    {"unit_price_with_tax", Convert.ToDecimal(txtUnitPriceWithTax.Text)},
                    {"unit_price_without_tax", Convert.ToDecimal(txtUnitPriceWithoutTax.Text)},
                    {"line_total", Convert.ToDecimal(txtLineTotal.Text)},
                    {"invoiced_amount", Convert.ToDecimal(txtInvoicedAmount.Text)},
                    {"delivered_amount", Convert.ToDecimal(txtDeliveredAmount.Text)}
                }

                Dim query As String = "
                    INSERT INTO order_details (order_id, line_number, work_order_number, material_id, location_id,
                                             order_quantity, invoiced_quantity, delivered_quantity, unit_price_with_tax,
                                             unit_price_without_tax, line_total, invoiced_amount, delivered_amount)
                    VALUES (@order_id, @line_number, @work_order_number, @material_id, @location_id,
                            @order_quantity, @invoiced_quantity, @delivered_quantity, @unit_price_with_tax,
                            @unit_price_without_tax, @line_total, @invoiced_amount, @delivered_amount)"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("订单明细添加成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadData()
                UpdateOrderTotal()
                ClearForm()

            Catch ex As Exception
                MessageBox.Show($"添加订单明细失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If currentDetailId = 0 Then
            MessageBox.Show("请先选择要修改的明细！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If ValidateInput() Then
            Try
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"id", currentDetailId},
                    {"line_number", txtLineNumber.Text.Trim()},
                    {"work_order_number", txtWorkOrderNumber.Text.Trim()},
                    {"material_id", cmbMaterial.SelectedValue},
                    {"location_id", cmbLocation.SelectedValue},
                    {"order_quantity", Convert.ToDecimal(txtOrderQuantity.Text)},
                    {"invoiced_quantity", Convert.ToDecimal(txtInvoicedQuantity.Text)},
                    {"delivered_quantity", Convert.ToDecimal(txtDeliveredQuantity.Text)},
                    {"unit_price_with_tax", Convert.ToDecimal(txtUnitPriceWithTax.Text)},
                    {"unit_price_without_tax", Convert.ToDecimal(txtUnitPriceWithoutTax.Text)},
                    {"line_total", Convert.ToDecimal(txtLineTotal.Text)},
                    {"invoiced_amount", Convert.ToDecimal(txtInvoicedAmount.Text)},
                    {"delivered_amount", Convert.ToDecimal(txtDeliveredAmount.Text)}
                }

                Dim query As String = "
                    UPDATE order_details
                    SET line_number = @line_number, work_order_number = @work_order_number,
                        material_id = @material_id, location_id = @location_id, order_quantity = @order_quantity,
                        invoiced_quantity = @invoiced_quantity, delivered_quantity = @delivered_quantity,
                        unit_price_with_tax = @unit_price_with_tax, unit_price_without_tax = @unit_price_without_tax,
                        line_total = @line_total, invoiced_amount = @invoiced_amount, delivered_amount = @delivered_amount
                    WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("订单明细修改成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadData()
                UpdateOrderTotal()

            Catch ex As Exception
                MessageBox.Show($"修改订单明细失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If currentDetailId = 0 Then
            MessageBox.Show("请先选择要删除的明细！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If MessageBox.Show("确定要删除选中的订单明细吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Dim parameters As New Dictionary(Of String, Object) From {{"id", currentDetailId}}
                Dim query As String = "DELETE FROM order_details WHERE id = @id"

                DatabaseManager.Instance.ExecuteNonQuery(query, parameters)
                MessageBox.Show("订单明细删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadData()
                UpdateOrderTotal()
                ClearForm()

            Catch ex As Exception
                MessageBox.Show($"删除订单明细失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadData()
        ClearForm()
    End Sub

    Private Sub BtnCalculate_Click(sender As Object, e As EventArgs)
        CalculatePrices(Nothing, Nothing)
    End Sub

    Private Sub BtnClose_Click(sender As Object, e As EventArgs)
        Me.Close()
    End Sub

    Private Sub CalculatePrices(sender As Object, e As EventArgs)
        Try
            ' 获取税率
            Dim taxRateQuery As String = "SELECT tax_rate FROM orders WHERE id = @id"
            Dim taxRateParams As New Dictionary(Of String, Object) From {{"id", orderId}}
            Dim taxRateObj As Object = DatabaseManager.Instance.ExecuteScalar(taxRateQuery, taxRateParams)
            Dim taxRate As Decimal = If(taxRateObj IsNot Nothing, Convert.ToDecimal(taxRateObj), 0.13D)

            If Not String.IsNullOrWhiteSpace(txtUnitPriceWithTax.Text) Then
                Dim unitPriceWithTax As Decimal
                If Decimal.TryParse(txtUnitPriceWithTax.Text, unitPriceWithTax) Then
                    ' 计算不含税单价
                    Dim unitPriceWithoutTax As Decimal = unitPriceWithTax / (1 + taxRate)
                    txtUnitPriceWithoutTax.Text = unitPriceWithoutTax.ToString("F4")

                    ' 计算行总金额
                    If Not String.IsNullOrWhiteSpace(txtOrderQuantity.Text) Then
                        Dim orderQuantity As Decimal
                        If Decimal.TryParse(txtOrderQuantity.Text, orderQuantity) Then
                            Dim lineTotal As Decimal = unitPriceWithTax * orderQuantity
                            txtLineTotal.Text = lineTotal.ToString("F2")
                        End If
                    End If
                End If
            End If

        Catch ex As Exception
            ' 忽略计算错误
        End Try
    End Sub

    Private Function ValidateInput() As Boolean
        If String.IsNullOrWhiteSpace(txtLineNumber.Text) Then
            MessageBox.Show("请输入行号！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtLineNumber.Focus()
            Return False
        End If

        If cmbMaterial.SelectedValue Is Nothing Then
            MessageBox.Show("请选择物料！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbMaterial.Focus()
            Return False
        End If

        If cmbLocation.SelectedValue Is Nothing Then
            MessageBox.Show("请选择库位！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbLocation.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtOrderQuantity.Text) Then
            MessageBox.Show("请输入订单数量！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOrderQuantity.Focus()
            Return False
        End If

        Dim orderQuantity As Decimal
        If Not Decimal.TryParse(txtOrderQuantity.Text, orderQuantity) OrElse orderQuantity <= 0 Then
            MessageBox.Show("订单数量必须是大于0的数字！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOrderQuantity.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtUnitPriceWithTax.Text) Then
            MessageBox.Show("请输入含税单价！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUnitPriceWithTax.Focus()
            Return False
        End If

        Dim unitPrice As Decimal
        If Not Decimal.TryParse(txtUnitPriceWithTax.Text, unitPrice) OrElse unitPrice < 0 Then
            MessageBox.Show("含税单价必须是非负数字！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUnitPriceWithTax.Focus()
            Return False
        End If

        ' 验证已开票数量和已交货数量
        Dim invoicedQuantity As Decimal
        If Not Decimal.TryParse(txtInvoicedQuantity.Text, invoicedQuantity) OrElse invoicedQuantity < 0 Then
            MessageBox.Show("已开票数量必须是非负数字！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtInvoicedQuantity.Focus()
            Return False
        End If

        Dim deliveredQuantity As Decimal
        If Not Decimal.TryParse(txtDeliveredQuantity.Text, deliveredQuantity) OrElse deliveredQuantity < 0 Then
            MessageBox.Show("已交货数量必须是非负数字！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtDeliveredQuantity.Focus()
            Return False
        End If

        If invoicedQuantity > orderQuantity Then
            MessageBox.Show("已开票数量不能超过订单数量！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtInvoicedQuantity.Focus()
            Return False
        End If

        If deliveredQuantity > orderQuantity Then
            MessageBox.Show("已交货数量不能超过订单数量！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtDeliveredQuantity.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub ClearForm()
        currentDetailId = 0
        txtLineNumber.Clear()
        txtWorkOrderNumber.Clear()
        If cmbMaterial.Items.Count > 0 Then cmbMaterial.SelectedIndex = 0
        If cmbLocation.Items.Count > 0 Then cmbLocation.SelectedIndex = 0
        txtOrderQuantity.Clear()
        txtInvoicedQuantity.Text = "0"
        txtDeliveredQuantity.Text = "0"
        txtUnitPriceWithTax.Clear()
        txtUnitPriceWithoutTax.Clear()
        txtLineTotal.Clear()
        txtInvoicedAmount.Text = "0"
        txtDeliveredAmount.Text = "0"
    End Sub

    Private Sub UpdateOrderTotal()
        Try
            ' 计算订单总金额
            Dim query As String = "SELECT SUM(line_total) FROM order_details WHERE order_id = @order_id"
            Dim parameters As New Dictionary(Of String, Object) From {{"order_id", orderId}}
            Dim totalObj As Object = DatabaseManager.Instance.ExecuteScalar(query, parameters)
            Dim totalAmount As Decimal = If(totalObj IsNot Nothing AndAlso Not IsDBNull(totalObj), Convert.ToDecimal(totalObj), 0)

            ' 更新订单表的总金额
            Dim updateQuery As String = "UPDATE orders SET total_amount = @total_amount WHERE id = @id"
            Dim updateParams As New Dictionary(Of String, Object) From {
                {"total_amount", totalAmount},
                {"id", orderId}
            }
            DatabaseManager.Instance.ExecuteNonQuery(updateQuery, updateParams)

        Catch ex As Exception
            MessageBox.Show($"更新订单总金额失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
