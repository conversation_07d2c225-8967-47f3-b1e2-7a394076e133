Imports System.Data
Imports MySql.Data.MySqlClient

''' <summary>
''' 应付账款管理窗体
''' </summary>
Public Class AccountsPayableForm
    Private isEditMode As Boolean = False
    Private currentApId As Integer = 0

    Public Sub New()
        InitializeComponent()
        ApplyPermissions()
        LoadAccountsPayable()
        LoadSuppliers()
        SetFormMode(False)
    End Sub

    ''' <summary>
    ''' 应用权限控制
    ''' </summary>
    Private Sub ApplyPermissions()
        If Not PermissionManager.HasPermission(PermissionManager.Permission.ViewFinancial) Then
            MessageBox.Show("您没有权限访问财务管理功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If
        SetFormMode(False)
    End Sub

    ''' <summary>
    ''' 设置窗体模式
    ''' </summary>
    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode
        
        ' 控制输入控件
        txtApNumber.ReadOnly = True ' 应付单号自动生成
        cmbSupplier.Enabled = editMode
        txtPurchaseOrderNumber.ReadOnly = Not editMode
        txtInvoiceNumber.ReadOnly = Not editMode
        dtpInvoiceDate.Enabled = editMode
        dtpDueDate.Enabled = editMode
        txtOriginalAmount.ReadOnly = Not editMode
        cmbCurrency.Enabled = editMode
        txtExchangeRate.ReadOnly = Not editMode
        cmbStatus.Enabled = editMode
        txtPaymentTerms.ReadOnly = Not editMode
        txtDescription.ReadOnly = Not editMode
        
        ' 控制按钮
        Dim canManage As Boolean = PermissionManager.HasPermission(PermissionManager.Permission.ManageFinancial)
        
        btnNew.Enabled = Not editMode AndAlso canManage
        btnEdit.Enabled = Not editMode AndAlso canManage AndAlso dgvAccountsPayable.SelectedRows.Count > 0
        btnDelete.Enabled = Not editMode AndAlso canManage AndAlso dgvAccountsPayable.SelectedRows.Count > 0
        btnSave.Enabled = editMode AndAlso canManage
        btnCancel.Enabled = editMode
        btnPayment.Enabled = Not editMode AndAlso canManage AndAlso dgvAccountsPayable.SelectedRows.Count > 0
        btnRefresh.Enabled = Not editMode
    End Sub

    ''' <summary>
    ''' 加载应付账款数据
    ''' </summary>
    Private Sub LoadAccountsPayable()
        Try
            Dim sql As String = "
                SELECT
                    ap.id,
                    ap.ap_number AS '应付单号',
                    s.supplier_name AS '供应商',
                    ap.purchase_order_number AS '采购订单号',
                    ap.invoice_number AS '发票号码',
                    ap.invoice_date AS '发票日期',
                    ap.due_date AS '到期日期',
                    ap.original_amount AS '原始金额',
                    ap.paid_amount AS '已付金额',
                    ap.outstanding_amount AS '未付金额',
                    ap.currency AS '币种',
                    ap.exchange_rate AS '汇率',
                    ap.status AS '状态',
                    ap.payment_terms AS '付款条件',
                    ap.description AS '备注',
                    ap.created_at AS '创建时间'
                FROM accounts_payable ap
                LEFT JOIN suppliers s ON ap.supplier_id = s.id
                ORDER BY ap.created_at DESC"
            
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvAccountsPayable.DataSource = dt
            
            ' 隐藏ID列
            If dgvAccountsPayable.Columns.Contains("id") Then
                dgvAccountsPayable.Columns("id").Visible = False
            End If
            
            ' 设置列宽
            dgvAccountsPayable.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            
        Catch ex As Exception
            MessageBox.Show($"加载应付账款数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 加载供应商数据
    ''' </summary>
    Private Sub LoadSuppliers()
        Try
            Dim sql As String = "SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name"
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(sql)

            cmbSupplier.DisplayMember = "supplier_name"
            cmbSupplier.ValueMember = "id"
            cmbSupplier.DataSource = dt
            cmbSupplier.SelectedIndex = -1
            
        Catch ex As Exception
            MessageBox.Show($"加载供应商数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 生成应付单号
    ''' </summary>
    Private Function GenerateApNumber() As String
        Try
            Dim sql As String = "SELECT COUNT(*) FROM accounts_payable WHERE DATE(created_at) = CURDATE()"
            Dim count As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(sql))
            Return $"AP{DateTime.Now:yyyyMMdd}{(count + 1):D3}"
        Catch ex As Exception
            Return $"AP{DateTime.Now:yyyyMMddHHmmss}"
        End Try
    End Function

    ''' <summary>
    ''' 计算未付金额
    ''' </summary>
    Private Sub CalculateOutstandingAmount()
        Try
            Dim originalAmount As Decimal = If(String.IsNullOrEmpty(txtOriginalAmount.Text), 0, Convert.ToDecimal(txtOriginalAmount.Text))
            Dim paidAmount As Decimal = If(String.IsNullOrEmpty(txtPaidAmount.Text), 0, Convert.ToDecimal(txtPaidAmount.Text))
            txtOutstandingAmount.Text = (originalAmount - paidAmount).ToString("F2")
        Catch ex As Exception
            txtOutstandingAmount.Text = "0.00"
        End Try
    End Sub

    ' 事件处理程序
    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        ClearForm()
        txtApNumber.Text = GenerateApNumber()
        dtpInvoiceDate.Value = DateTime.Now
        dtpDueDate.Value = DateTime.Now.AddDays(30)
        cmbCurrency.Text = "CNY"
        txtExchangeRate.Text = "1.0000"
        cmbStatus.Text = "pending"
        txtPaidAmount.Text = "0.00"
        SetFormMode(True)
        cmbSupplier.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If dgvAccountsPayable.SelectedRows.Count = 0 Then
            MessageBox.Show("请选择要编辑的应付账款记录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        
        LoadSelectedRecord()
        SetFormMode(True)
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If dgvAccountsPayable.SelectedRows.Count = 0 Then
            MessageBox.Show("请选择要删除的应付账款记录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        
        If MessageBox.Show("确定要删除选中的应付账款记录吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            DeleteSelectedRecord()
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If ValidateForm() Then
            SaveRecord()
        End If
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        SetFormMode(False)
        If dgvAccountsPayable.SelectedRows.Count > 0 Then
            LoadSelectedRecord()
        Else
            ClearForm()
        End If
    End Sub

    Private Sub btnPayment_Click(sender As Object, e As EventArgs) Handles btnPayment.Click
        If dgvAccountsPayable.SelectedRows.Count = 0 Then
            MessageBox.Show("请选择要付款的应付账款记录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        
        Dim apId As Integer = Convert.ToInt32(dgvAccountsPayable.SelectedRows(0).Cells("id").Value)
        Dim outstandingAmount As Decimal = Convert.ToDecimal(dgvAccountsPayable.SelectedRows(0).Cells("未付金额").Value)
        
        If outstandingAmount <= 0 Then
            MessageBox.Show("该应付账款已全部付清", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        
        Dim paymentForm As New PaymentRecordForm(apId, "payable")
        If paymentForm.ShowDialog() = DialogResult.OK Then
            LoadAccountsPayable()
        End If
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        LoadAccountsPayable()
        LoadSuppliers()
    End Sub

    Private Sub dgvAccountsPayable_SelectionChanged(sender As Object, e As EventArgs) Handles dgvAccountsPayable.SelectionChanged
        If Not isEditMode AndAlso dgvAccountsPayable.SelectedRows.Count > 0 Then
            LoadSelectedRecord()
        End If
        SetFormMode(isEditMode)
    End Sub

    Private Sub txtOriginalAmount_TextChanged(sender As Object, e As EventArgs) Handles txtOriginalAmount.TextChanged
        CalculateOutstandingAmount()
    End Sub

    Private Sub txtPaidAmount_TextChanged(sender As Object, e As EventArgs) Handles txtPaidAmount.TextChanged
        CalculateOutstandingAmount()
    End Sub

    ''' <summary>
    ''' 清空表单
    ''' </summary>
    Private Sub ClearForm()
        txtApNumber.Clear()
        cmbSupplier.SelectedIndex = -1
        txtPurchaseOrderNumber.Clear()
        txtInvoiceNumber.Clear()
        dtpInvoiceDate.Value = DateTime.Now
        dtpDueDate.Value = DateTime.Now.AddDays(30)
        txtOriginalAmount.Clear()
        txtPaidAmount.Text = "0.00"
        txtOutstandingAmount.Text = "0.00"
        cmbCurrency.Text = "CNY"
        txtExchangeRate.Text = "1.0000"
        cmbStatus.Text = "pending"
        txtPaymentTerms.Clear()
        txtDescription.Clear()
        currentApId = 0
    End Sub

    ''' <summary>
    ''' 加载选中记录
    ''' </summary>
    Private Sub LoadSelectedRecord()
        If dgvAccountsPayable.SelectedRows.Count = 0 Then Return
        
        Try
            currentApId = Convert.ToInt32(dgvAccountsPayable.SelectedRows(0).Cells("id").Value)
            
            Dim sql As String = "SELECT * FROM accounts_payable WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {
                {"@id", currentApId}
            }
            
            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(sql, parameters)
            If dt.Rows.Count > 0 Then
                Dim row As DataRow = dt.Rows(0)
                
                txtApNumber.Text = row("ap_number").ToString()
                cmbSupplier.SelectedValue = row("supplier_id")
                txtPurchaseOrderNumber.Text = row("purchase_order_number").ToString()
                txtInvoiceNumber.Text = row("invoice_number").ToString()
                
                If Not IsDBNull(row("invoice_date")) Then
                    dtpInvoiceDate.Value = Convert.ToDateTime(row("invoice_date"))
                End If
                
                dtpDueDate.Value = Convert.ToDateTime(row("due_date"))
                txtOriginalAmount.Text = Convert.ToDecimal(row("original_amount")).ToString("F2")
                txtPaidAmount.Text = Convert.ToDecimal(row("paid_amount")).ToString("F2")
                txtOutstandingAmount.Text = Convert.ToDecimal(row("outstanding_amount")).ToString("F2")
                cmbCurrency.Text = row("currency").ToString()
                txtExchangeRate.Text = Convert.ToDecimal(row("exchange_rate")).ToString("F4")
                cmbStatus.Text = row("status").ToString()
                txtPaymentTerms.Text = row("payment_terms").ToString()
                txtDescription.Text = row("description").ToString()
            End If
            
        Catch ex As Exception
            MessageBox.Show($"加载记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 验证表单数据
    ''' </summary>
    Private Function ValidateForm() As Boolean
        If cmbSupplier.SelectedValue Is Nothing Then
            MessageBox.Show("请选择供应商", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbSupplier.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtOriginalAmount.Text) Then
            MessageBox.Show("请输入原始金额", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOriginalAmount.Focus()
            Return False
        End If

        Try
            Dim amount As Decimal = Convert.ToDecimal(txtOriginalAmount.Text)
            If amount <= 0 Then
                MessageBox.Show("原始金额必须大于0", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtOriginalAmount.Focus()
                Return False
            End If
        Catch
            MessageBox.Show("原始金额格式不正确", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOriginalAmount.Focus()
            Return False
        End Try

        Try
            Dim exchangeRate As Decimal = Convert.ToDecimal(txtExchangeRate.Text)
            If exchangeRate <= 0 Then
                MessageBox.Show("汇率必须大于0", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtExchangeRate.Focus()
                Return False
            End If
        Catch
            MessageBox.Show("汇率格式不正确", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtExchangeRate.Focus()
            Return False
        End Try

        Return True
    End Function

    ''' <summary>
    ''' 保存记录
    ''' </summary>
    Private Sub SaveRecord()
        Try
            Dim sql As String
            Dim parameters As New Dictionary(Of String, Object)

            If currentApId = 0 Then
                ' 新增
                sql = "
                    INSERT INTO accounts_payable (
                        ap_number, supplier_id, purchase_order_number, invoice_number,
                        invoice_date, due_date, original_amount, paid_amount, outstanding_amount,
                        currency, exchange_rate, status, payment_terms, description, created_by
                    ) VALUES (
                        @ap_number, @supplier_id, @purchase_order_number, @invoice_number,
                        @invoice_date, @due_date, @original_amount, @paid_amount, @outstanding_amount,
                        @currency, @exchange_rate, @status, @payment_terms, @description, @created_by
                    )"
            Else
                ' 更新
                sql = "
                    UPDATE accounts_payable SET
                        supplier_id = @supplier_id,
                        purchase_order_number = @purchase_order_number,
                        invoice_number = @invoice_number,
                        invoice_date = @invoice_date,
                        due_date = @due_date,
                        original_amount = @original_amount,
                        currency = @currency,
                        exchange_rate = @exchange_rate,
                        status = @status,
                        payment_terms = @payment_terms,
                        description = @description,
                        outstanding_amount = @original_amount - @paid_amount
                    WHERE id = @id"
                parameters.Add("@id", currentApId)
            End If

            ' 添加参数
            parameters.Add("@ap_number", txtApNumber.Text.Trim())
            parameters.Add("@supplier_id", cmbSupplier.SelectedValue)
            parameters.Add("@purchase_order_number", If(String.IsNullOrWhiteSpace(txtPurchaseOrderNumber.Text), DBNull.Value, txtPurchaseOrderNumber.Text.Trim()))
            parameters.Add("@invoice_number", If(String.IsNullOrWhiteSpace(txtInvoiceNumber.Text), DBNull.Value, txtInvoiceNumber.Text.Trim()))
            parameters.Add("@invoice_date", If(dtpInvoiceDate.Checked, dtpInvoiceDate.Value.Date, DBNull.Value))
            parameters.Add("@due_date", dtpDueDate.Value.Date)
            parameters.Add("@original_amount", Convert.ToDecimal(txtOriginalAmount.Text))
            parameters.Add("@paid_amount", Convert.ToDecimal(txtPaidAmount.Text))
            parameters.Add("@outstanding_amount", Convert.ToDecimal(txtOutstandingAmount.Text))
            parameters.Add("@currency", cmbCurrency.Text)
            parameters.Add("@exchange_rate", Convert.ToDecimal(txtExchangeRate.Text))
            parameters.Add("@status", cmbStatus.Text)
            parameters.Add("@payment_terms", If(String.IsNullOrWhiteSpace(txtPaymentTerms.Text), DBNull.Value, txtPaymentTerms.Text.Trim()))
            parameters.Add("@description", If(String.IsNullOrWhiteSpace(txtDescription.Text), DBNull.Value, txtDescription.Text.Trim()))

            If currentApId = 0 Then
                parameters.Add("@created_by", UserSession.CurrentUserId)
            End If

            DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)

            MessageBox.Show("保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            SetFormMode(False)
            LoadAccountsPayable()

        Catch ex As Exception
            MessageBox.Show($"保存失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 删除选中记录
    ''' </summary>
    Private Sub DeleteSelectedRecord()
        Try
            Dim apId As Integer = Convert.ToInt32(dgvAccountsPayable.SelectedRows(0).Cells("id").Value)

            ' 检查是否有付款记录
            Dim checkSql As String = "SELECT COUNT(*) FROM payment_records WHERE payment_type = 'payable' AND reference_id = @id"
            Dim checkParams As New Dictionary(Of String, Object) From {{"@id", apId}}
            Dim paymentCount As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(checkSql, checkParams))

            If paymentCount > 0 Then
                MessageBox.Show("该应付账款已有付款记录，无法删除", "删除失败", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            Dim sql As String = "DELETE FROM accounts_payable WHERE id = @id"
            Dim parameters As New Dictionary(Of String, Object) From {{"@id", apId}}

            DatabaseManager.Instance.ExecuteNonQuery(sql, parameters)

            MessageBox.Show("删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadAccountsPayable()
            ClearForm()

        Catch ex As Exception
            MessageBox.Show($"删除失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
