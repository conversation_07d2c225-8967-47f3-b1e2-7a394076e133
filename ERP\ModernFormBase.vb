Imports System.Drawing
Imports System.Windows.Forms
Imports FontAwesome.Sharp

''' <summary>
''' 现代化窗体基类 - 提供统一的现代化界面风格
''' </summary>
Public Class ModernFormBase
    Inherits Form

    ' 标题栏控件
    Protected pnlTitleBar As Panel
    Protected lblTitle As Label
    Protected btnMinimize As IconButton
    Protected btnMaximize As IconButton
    Protected btnClose As IconButton
    Protected iconTitle As IconPictureBox

    ' 内容区域
    Protected pnlContent As Panel

    ' 是否可拖拽
    Private isDragging As Boolean = False
    Private dragOffset As Point

    Public Sub New()
        InitializeModernForm()
        SetupTitleBar()
        SetupContent()
        ApplyModernTheme()
    End Sub

    ''' <summary>
    ''' 初始化现代化窗体
    ''' </summary>
    Private Sub InitializeModernForm()
        Me.FormBorderStyle = FormBorderStyle.None
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = UIThemeManager.BackgroundColor
        Me.Font = UIThemeManager.DefaultFont
        
        ' 应用主题和自动缩放
        UIThemeManager.ApplyTheme(Me)
        UIThemeManager.SetAutoScale(Me)
        
        ' 设置最小尺寸
        Me.MinimumSize = New Size(800, 600)
    End Sub

    ''' <summary>
    ''' 设置标题栏
    ''' </summary>
    Private Sub SetupTitleBar()
        pnlTitleBar = New Panel()
        pnlTitleBar.Dock = DockStyle.Top
        pnlTitleBar.Height = 40
        pnlTitleBar.BackColor = UIThemeManager.PrimaryColor
        Me.Controls.Add(pnlTitleBar)

        ' 标题图标
        iconTitle = New IconPictureBox()
        iconTitle.IconChar = IconChar.WindowMaximize
        iconTitle.IconColor = Color.White
        iconTitle.IconSize = 20
        iconTitle.Size = New Size(20, 20)
        iconTitle.Location = New Point(10, 10)
        iconTitle.BackColor = Color.Transparent
        pnlTitleBar.Controls.Add(iconTitle)

        ' 标题文本
        lblTitle = New Label()
        lblTitle.Text = Me.Text
        lblTitle.Font = New Font("Microsoft YaHei", 10, FontStyle.Bold)
        lblTitle.ForeColor = Color.White
        lblTitle.BackColor = Color.Transparent
        lblTitle.Location = New Point(40, 12)
        lblTitle.AutoSize = True
        pnlTitleBar.Controls.Add(lblTitle)

        ' 关闭按钮
        btnClose = New IconButton()
        btnClose.IconChar = IconChar.Times
        btnClose.IconColor = Color.White
        btnClose.IconSize = 16
        btnClose.Size = New Size(40, 40)
        btnClose.Anchor = AnchorStyles.Top Or AnchorStyles.Right
        btnClose.Location = New Point(Me.Width - 40, 0)
        btnClose.FlatStyle = FlatStyle.Flat
        btnClose.FlatAppearance.BorderSize = 0
        btnClose.BackColor = Color.Transparent
        btnClose.Cursor = Cursors.Hand
        AddHandler btnClose.Click, AddressOf BtnClose_Click
        AddHandler btnClose.MouseEnter, Sub() btnClose.BackColor = UIThemeManager.DangerColor
        AddHandler btnClose.MouseLeave, Sub() btnClose.BackColor = Color.Transparent
        pnlTitleBar.Controls.Add(btnClose)

        ' 最大化按钮
        btnMaximize = New IconButton()
        btnMaximize.IconChar = IconChar.WindowMaximize
        btnMaximize.IconColor = Color.White
        btnMaximize.IconSize = 16
        btnMaximize.Size = New Size(40, 40)
        btnMaximize.Anchor = AnchorStyles.Top Or AnchorStyles.Right
        btnMaximize.Location = New Point(Me.Width - 80, 0)
        btnMaximize.FlatStyle = FlatStyle.Flat
        btnMaximize.FlatAppearance.BorderSize = 0
        btnMaximize.BackColor = Color.Transparent
        btnMaximize.Cursor = Cursors.Hand
        AddHandler btnMaximize.Click, AddressOf BtnMaximize_Click
        AddHandler btnMaximize.MouseEnter, Sub() btnMaximize.BackColor = Color.FromArgb(100, Color.White)
        AddHandler btnMaximize.MouseLeave, Sub() btnMaximize.BackColor = Color.Transparent
        pnlTitleBar.Controls.Add(btnMaximize)

        ' 最小化按钮
        btnMinimize = New IconButton()
        btnMinimize.IconChar = IconChar.WindowMinimize
        btnMinimize.IconColor = Color.White
        btnMinimize.IconSize = 16
        btnMinimize.Size = New Size(40, 40)
        btnMinimize.Anchor = AnchorStyles.Top Or AnchorStyles.Right
        btnMinimize.Location = New Point(Me.Width - 120, 0)
        btnMinimize.FlatStyle = FlatStyle.Flat
        btnMinimize.FlatAppearance.BorderSize = 0
        btnMinimize.BackColor = Color.Transparent
        btnMinimize.Cursor = Cursors.Hand
        AddHandler btnMinimize.Click, AddressOf BtnMinimize_Click
        AddHandler btnMinimize.MouseEnter, Sub() btnMinimize.BackColor = Color.FromArgb(100, Color.White)
        AddHandler btnMinimize.MouseLeave, Sub() btnMinimize.BackColor = Color.Transparent
        pnlTitleBar.Controls.Add(btnMinimize)

        ' 标题栏拖拽事件
        AddHandler pnlTitleBar.MouseDown, AddressOf TitleBar_MouseDown
        AddHandler pnlTitleBar.MouseMove, AddressOf TitleBar_MouseMove
        AddHandler pnlTitleBar.MouseUp, AddressOf TitleBar_MouseUp
        AddHandler lblTitle.MouseDown, AddressOf TitleBar_MouseDown
        AddHandler lblTitle.MouseMove, AddressOf TitleBar_MouseMove
        AddHandler lblTitle.MouseUp, AddressOf TitleBar_MouseUp
    End Sub

    ''' <summary>
    ''' 设置内容区域
    ''' </summary>
    Private Sub SetupContent()
        pnlContent = New Panel()
        pnlContent.Dock = DockStyle.Fill
        pnlContent.BackColor = UIThemeManager.BackgroundColor
        pnlContent.Padding = New Padding(1)
        Me.Controls.Add(pnlContent)
    End Sub

    ''' <summary>
    ''' 应用现代化主题
    ''' </summary>
    Private Sub ApplyModernTheme()
        ' 添加窗体阴影效果
        AddHandler Me.Paint, Sub(s, e)
                                 Dim rect As Rectangle = Me.ClientRectangle
                                 Using pen As New Pen(UIThemeManager.BorderColor, 1)
                                     e.Graphics.DrawRectangle(pen, 0, 0, rect.Width - 1, rect.Height - 1)
                                 End Using
                             End Sub
    End Sub

    ''' <summary>
    ''' 设置窗体标题和图标
    ''' </summary>
    Public Sub SetTitle(title As String, iconName As String)
        Me.Text = title
        lblTitle.Text = title
        iconTitle.IconChar = UIThemeManager.GetIcon(iconName)
    End Sub

    ''' <summary>
    ''' 获取内容面板
    ''' </summary>
    Public ReadOnly Property ContentPanel As Panel
        Get
            Return pnlContent
        End Get
    End Property

    ' 标题栏按钮事件
    Private Sub BtnClose_Click(sender As Object, e As EventArgs)
        Me.Close()
    End Sub

    Private Sub BtnMaximize_Click(sender As Object, e As EventArgs)
        If Me.WindowState = FormWindowState.Maximized Then
            Me.WindowState = FormWindowState.Normal
            btnMaximize.IconChar = IconChar.WindowMaximize
        Else
            Me.WindowState = FormWindowState.Maximized
            btnMaximize.IconChar = IconChar.WindowRestore
        End If
    End Sub

    Private Sub BtnMinimize_Click(sender As Object, e As EventArgs)
        Me.WindowState = FormWindowState.Minimized
    End Sub

    ' 标题栏拖拽事件
    Private Sub TitleBar_MouseDown(sender As Object, e As MouseEventArgs)
        If e.Button = MouseButtons.Left Then
            isDragging = True
            dragOffset = e.Location
        End If
    End Sub

    Private Sub TitleBar_MouseMove(sender As Object, e As MouseEventArgs)
        If isDragging Then
            Me.Location = New Point(Me.Location.X + e.X - dragOffset.X, Me.Location.Y + e.Y - dragOffset.Y)
        End If
    End Sub

    Private Sub TitleBar_MouseUp(sender As Object, e As MouseEventArgs)
        isDragging = False
    End Sub

    ''' <summary>
    ''' 创建现代化按钮
    ''' </summary>
    Protected Function CreateModernButton(text As String, iconName As String, buttonType As String) As IconButton
        Dim button As IconButton = UIThemeManager.CreateIconButton(text, iconName)
        
        Select Case buttonType.ToLower()
            Case "primary"
                UIThemeManager.ApplyPrimaryButtonTheme(button)
            Case "success"
                UIThemeManager.ApplySuccessButtonTheme(button)
            Case "danger"
                UIThemeManager.ApplyDangerButtonTheme(button)
            Case Else
                UIThemeManager.ApplyButtonTheme(button)
        End Select
        
        Return button
    End Function

    ''' <summary>
    ''' 创建现代化面板
    ''' </summary>
    Protected Function CreateModernPanel() As Panel
        Dim panel As New Panel()
        panel.BackColor = UIThemeManager.SurfaceColor
        panel.BorderStyle = BorderStyle.None
        
        ' 添加阴影效果
        AddHandler panel.Paint, Sub(s, e)
                                     Dim rect As Rectangle = panel.ClientRectangle
                                     Using pen As New Pen(UIThemeManager.BorderColor, 1)
                                         e.Graphics.DrawRectangle(pen, 0, 0, rect.Width - 1, rect.Height - 1)
                                     End Using
                                 End Sub
        
        Return panel
    End Function

    ''' <summary>
    ''' 显示现代化消息框
    ''' </summary>
    Protected Sub ShowModernMessage(message As String, title As String, messageType As String)
        Dim icon As MessageBoxIcon = MessageBoxIcon.Information
        
        Select Case messageType.ToLower()
            Case "error"
                icon = MessageBoxIcon.Error
            Case "warning"
                icon = MessageBoxIcon.Warning
            Case "question"
                icon = MessageBoxIcon.Question
        End Select
        
        MessageBox.Show(message, title, MessageBoxButtons.OK, icon)
    End Sub

End Class
