Imports System.Windows.Forms
Imports System.Drawing
Imports System.Drawing.Drawing2D

Public Class DashboardForm
    Inherits Form

    ' 控件声明
    Private pnlHeader As Panel
    Private lblWelcome As Label
    Private lblDateTime As Label
    Private lblUserInfo As Label

    ' 统计卡片
    Private pnlStatsContainer As Panel
    Private cardInventoryValue As Panel
    Private cardLowStock As Panel
    Private cardPendingOrders As Panel
    Private cardMonthlyRevenue As Panel
    Private cardActiveUsers As Panel
    Private cardSystemStatus As Panel

    ' 快捷操作区
    Private pnlQuickActions As Panel
    Private btnNewOrder As Button
    Private btnInbound As Button
    Private btnOutbound As Button
    Private btnStockCount As Button
    Private btnInventoryQuery As Button
    Private btnReports As Button

    ' 最近活动区
    Private pnlRecentActivity As Panel
    Private dgvRecentActivity As DataGridView

    ' 图表区域
    Private pnlCharts As Panel
    Private lblInventoryChart As Label
    Private lblOrderChart As Label

    ' 定时器
    Private WithEvents tmrRefresh As Timer

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadDashboardData()
        SetupTimer()
    End Sub

    Private Sub InitializeComponent()
        Me.SuspendLayout()
        Me.AutoScaleDimensions = New SizeF(7.0!, 17.0!)
        Me.AutoScaleMode = AutoScaleMode.Font
        Me.ClientSize = New Size(1400, 900)
        Me.Name = "DashboardForm"
        Me.Text = "ERP系统看板"
        Me.BackColor = Color.FromArgb(245, 247, 250)
        Me.Font = New Font("Microsoft YaHei", 9)
        Me.WindowState = FormWindowState.Maximized
        Me.ResumeLayout(False)
    End Sub

    Private Sub SetupUI()
        ' 创建头部区域
        CreateHeader()
        
        ' 创建统计卡片区域
        CreateStatsCards()
        
        ' 创建快捷操作区域
        CreateQuickActions()
        
        ' 创建最近活动区域
        CreateRecentActivity()
        
        ' 创建图表区域
        CreateCharts()
    End Sub

    Private Sub CreateHeader()
        pnlHeader = New Panel()
        pnlHeader.Dock = DockStyle.Top
        pnlHeader.Height = 80
        pnlHeader.BackColor = Color.FromArgb(52, 152, 219)
        Me.Controls.Add(pnlHeader)

        ' 欢迎标签
        lblWelcome = New Label()
        lblWelcome.Text = "欢迎使用ERP库存管理系统"
        lblWelcome.Font = New Font("Microsoft YaHei", 16, FontStyle.Bold)
        lblWelcome.ForeColor = Color.White
        lblWelcome.Location = New Point(30, 15)
        lblWelcome.Size = New Size(400, 30)
        pnlHeader.Controls.Add(lblWelcome)

        ' 日期时间标签
        lblDateTime = New Label()
        lblDateTime.Text = DateTime.Now.ToString("yyyy年MM月dd日 dddd HH:mm:ss")
        lblDateTime.Font = New Font("Microsoft YaHei", 10)
        lblDateTime.ForeColor = Color.White
        lblDateTime.Location = New Point(30, 45)
        lblDateTime.Size = New Size(300, 25)
        pnlHeader.Controls.Add(lblDateTime)

        ' 用户信息标签
        lblUserInfo = New Label()
        lblUserInfo.Text = $"当前用户: {UserSession.GetDisplayName()} | 角色: {UserSession.GetRoleName()}"
        lblUserInfo.Font = New Font("Microsoft YaHei", 10)
        lblUserInfo.ForeColor = Color.White
        lblUserInfo.Anchor = AnchorStyles.Top Or AnchorStyles.Right
        lblUserInfo.Location = New Point(1000, 25)
        lblUserInfo.Size = New Size(350, 25)
        lblUserInfo.TextAlign = ContentAlignment.MiddleRight
        pnlHeader.Controls.Add(lblUserInfo)
    End Sub

    Private Sub CreateStatsCards()
        pnlStatsContainer = New Panel()
        pnlStatsContainer.Location = New Point(20, 100)
        pnlStatsContainer.Size = New Size(1360, 150)
        pnlStatsContainer.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        Me.Controls.Add(pnlStatsContainer)

        ' 创建6个统计卡片
        cardInventoryValue = CreateStatsCard("库存总价值", "¥0.00", Color.FromArgb(46, 204, 113), 0)
        cardLowStock = CreateStatsCard("低库存预警", "0", Color.FromArgb(231, 76, 60), 1)
        cardPendingOrders = CreateStatsCard("待处理订单", "0", Color.FromArgb(241, 196, 15), 2)
        cardMonthlyRevenue = CreateStatsCard("本月营收", "¥0.00", Color.FromArgb(155, 89, 182), 3)
        cardActiveUsers = CreateStatsCard("在线用户", "0", Color.FromArgb(52, 152, 219), 4)
        cardSystemStatus = CreateStatsCard("系统状态", "正常", Color.FromArgb(26, 188, 156), 5)

        pnlStatsContainer.Controls.AddRange({cardInventoryValue, cardLowStock, cardPendingOrders, 
                                           cardMonthlyRevenue, cardActiveUsers, cardSystemStatus})
    End Sub

    Private Function CreateStatsCard(title As String, value As String, color As Color, index As Integer) As Panel
        Dim card As New Panel()
        card.Size = New Size(220, 130)
        card.Location = New Point(index * 230, 10)
        card.BackColor = Color.White
        card.BorderStyle = BorderStyle.None
        
        ' 添加阴影效果
        AddShadowEffect(card)

        ' 顶部彩色条
        Dim colorBar As New Panel()
        colorBar.Size = New Size(220, 5)
        colorBar.Location = New Point(0, 0)
        colorBar.BackColor = color
        card.Controls.Add(colorBar)

        ' 标题标签
        Dim lblTitle As New Label()
        lblTitle.Text = title
        lblTitle.Font = New Font("Microsoft YaHei", 10, FontStyle.Bold)
        lblTitle.ForeColor = Color.FromArgb(70, 70, 70)
        lblTitle.Location = New Point(15, 20)
        lblTitle.Size = New Size(190, 25)
        card.Controls.Add(lblTitle)

        ' 数值标签
        Dim lblValue As New Label()
        lblValue.Text = value
        lblValue.Font = New Font("Microsoft YaHei", 18, FontStyle.Bold)
        lblValue.ForeColor = color
        lblValue.Location = New Point(15, 50)
        lblValue.Size = New Size(190, 40)
        lblValue.Name = "value"
        card.Controls.Add(lblValue)

        ' 图标区域（简化为彩色圆点）
        Dim iconPanel As New Panel()
        iconPanel.Size = New Size(40, 40)
        iconPanel.Location = New Point(160, 80)
        iconPanel.BackColor = Color.FromArgb(50, color)
        AddHandler iconPanel.Paint, Sub(s, e)
                                         Dim g As Graphics = e.Graphics
                                         g.SmoothingMode = SmoothingMode.AntiAlias
                                         Using brush As New SolidBrush(color)
                                             g.FillEllipse(brush, 5, 5, 30, 30)
                                         End Using
                                     End Sub
        card.Controls.Add(iconPanel)

        Return card
    End Function

    Private Sub AddShadowEffect(control As Control)
        ' 简化的阴影效果
        AddHandler control.Paint, Sub(s, e)
                                      Dim rect As Rectangle = control.ClientRectangle
                                      rect.Inflate(-1, -1)
                                      Using pen As New Pen(Color.FromArgb(30, Color.Gray), 1)
                                          e.Graphics.DrawRectangle(pen, rect)
                                      End Using
                                  End Sub
    End Sub

    Private Sub CreateQuickActions()
        pnlQuickActions = New Panel()
        pnlQuickActions.Location = New Point(20, 270)
        pnlQuickActions.Size = New Size(1360, 100)
        pnlQuickActions.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        pnlQuickActions.BackColor = Color.White
        AddShadowEffect(pnlQuickActions)
        Me.Controls.Add(pnlQuickActions)

        ' 标题
        Dim lblQuickTitle As New Label()
        lblQuickTitle.Text = "快捷操作"
        lblQuickTitle.Font = New Font("Microsoft YaHei", 12, FontStyle.Bold)
        lblQuickTitle.ForeColor = Color.FromArgb(70, 70, 70)
        lblQuickTitle.Location = New Point(20, 15)
        lblQuickTitle.Size = New Size(200, 25)
        pnlQuickActions.Controls.Add(lblQuickTitle)

        ' 快捷按钮
        btnNewOrder = CreateQuickButton("新建订单", Color.FromArgb(52, 152, 219), 0)
        btnInbound = CreateQuickButton("入库管理", Color.FromArgb(46, 204, 113), 1)
        btnOutbound = CreateQuickButton("出库管理", Color.FromArgb(231, 76, 60), 2)
        btnStockCount = CreateQuickButton("库存盘点", Color.FromArgb(241, 196, 15), 3)
        btnInventoryQuery = CreateQuickButton("库存查询", Color.FromArgb(155, 89, 182), 4)
        btnReports = CreateQuickButton("报表中心", Color.FromArgb(26, 188, 156), 5)

        pnlQuickActions.Controls.AddRange({btnNewOrder, btnInbound, btnOutbound, 
                                         btnStockCount, btnInventoryQuery, btnReports})
    End Sub

    Private Function CreateQuickButton(text As String, color As Color, index As Integer) As Button
        Dim btn As New Button()
        btn.Text = text
        btn.Size = New Size(120, 40)
        btn.Location = New Point(50 + index * 140, 45)
        btn.BackColor = color
        btn.ForeColor = Color.White
        btn.Font = New Font("Microsoft YaHei", 9, FontStyle.Bold)
        btn.FlatStyle = FlatStyle.Flat
        btn.FlatAppearance.BorderSize = 0
        btn.Cursor = Cursors.Hand
        
        ' 添加悬停效果
        AddHandler btn.MouseEnter, Sub(s, e)
                                        btn.BackColor = Color.FromArgb(Math.Min(255, color.R + 20), 
                                                                     Math.Min(255, color.G + 20), 
                                                                     Math.Min(255, color.B + 20))
                                    End Sub
        AddHandler btn.MouseLeave, Sub(s, e) btn.BackColor = color

        Return btn
    End Function

    Private Sub CreateRecentActivity()
        pnlRecentActivity = New Panel()
        pnlRecentActivity.Location = New Point(20, 390)
        pnlRecentActivity.Size = New Size(670, 300)
        pnlRecentActivity.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Bottom
        pnlRecentActivity.BackColor = Color.White
        AddShadowEffect(pnlRecentActivity)
        Me.Controls.Add(pnlRecentActivity)

        ' 标题
        Dim lblActivityTitle As New Label()
        lblActivityTitle.Text = "最近活动"
        lblActivityTitle.Font = New Font("Microsoft YaHei", 12, FontStyle.Bold)
        lblActivityTitle.ForeColor = Color.FromArgb(70, 70, 70)
        lblActivityTitle.Location = New Point(20, 15)
        lblActivityTitle.Size = New Size(200, 25)
        pnlRecentActivity.Controls.Add(lblActivityTitle)

        ' 活动列表
        dgvRecentActivity = New DataGridView()
        dgvRecentActivity.Location = New Point(20, 50)
        dgvRecentActivity.Size = New Size(630, 230)
        dgvRecentActivity.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom
        dgvRecentActivity.ReadOnly = True
        dgvRecentActivity.AllowUserToAddRows = False
        dgvRecentActivity.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvRecentActivity.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        dgvRecentActivity.BorderStyle = BorderStyle.None
        dgvRecentActivity.BackgroundColor = Color.White
        dgvRecentActivity.GridColor = Color.FromArgb(230, 230, 230)
        pnlRecentActivity.Controls.Add(dgvRecentActivity)
    End Sub

    Private Sub CreateCharts()
        pnlCharts = New Panel()
        pnlCharts.Location = New Point(710, 390)
        pnlCharts.Size = New Size(670, 300)
        pnlCharts.Anchor = AnchorStyles.Top Or AnchorStyles.Right Or AnchorStyles.Bottom
        pnlCharts.BackColor = Color.White
        AddShadowEffect(pnlCharts)
        Me.Controls.Add(pnlCharts)

        ' 标题
        Dim lblChartsTitle As New Label()
        lblChartsTitle.Text = "数据概览"
        lblChartsTitle.Font = New Font("Microsoft YaHei", 12, FontStyle.Bold)
        lblChartsTitle.ForeColor = Color.FromArgb(70, 70, 70)
        lblChartsTitle.Location = New Point(20, 15)
        lblChartsTitle.Size = New Size(200, 25)
        pnlCharts.Controls.Add(lblChartsTitle)

        ' 库存分布图表占位符
        lblInventoryChart = New Label()
        lblInventoryChart.Text = "库存分布图表" & vbCrLf & "(图表功能开发中...)"
        lblInventoryChart.Font = New Font("Microsoft YaHei", 10)
        lblInventoryChart.ForeColor = Color.FromArgb(120, 120, 120)
        lblInventoryChart.Location = New Point(20, 60)
        lblInventoryChart.Size = New Size(300, 100)
        lblInventoryChart.TextAlign = ContentAlignment.MiddleCenter
        lblInventoryChart.BorderStyle = BorderStyle.FixedSingle
        lblInventoryChart.BackColor = Color.FromArgb(250, 250, 250)
        pnlCharts.Controls.Add(lblInventoryChart)

        ' 订单趋势图表占位符
        lblOrderChart = New Label()
        lblOrderChart.Text = "订单趋势图表" & vbCrLf & "(图表功能开发中...)"
        lblOrderChart.Font = New Font("Microsoft YaHei", 10)
        lblOrderChart.ForeColor = Color.FromArgb(120, 120, 120)
        lblOrderChart.Location = New Point(350, 60)
        lblOrderChart.Size = New Size(300, 100)
        lblOrderChart.TextAlign = ContentAlignment.MiddleCenter
        lblOrderChart.BorderStyle = BorderStyle.FixedSingle
        lblOrderChart.BackColor = Color.FromArgb(250, 250, 250)
        pnlCharts.Controls.Add(lblOrderChart)

        ' 系统信息区域
        CreateSystemInfo()
    End Sub

    Private Sub CreateSystemInfo()
        Dim pnlSystemInfo As New Panel()
        pnlSystemInfo.Location = New Point(20, 180)
        pnlSystemInfo.Size = New Size(630, 100)
        pnlSystemInfo.BackColor = Color.FromArgb(248, 249, 250)
        pnlCharts.Controls.Add(pnlSystemInfo)

        Dim lblSystemInfo As New Label()
        lblSystemInfo.Text = "系统信息" & vbCrLf & 
                            $"数据库连接: 正常" & vbCrLf & 
                            $"系统版本: ERP v1.0.0" & vbCrLf & 
                            $"最后备份: {DateTime.Now.AddDays(-1):yyyy-MM-dd}"
        lblSystemInfo.Font = New Font("Microsoft YaHei", 9)
        lblSystemInfo.ForeColor = Color.FromArgb(70, 70, 70)
        lblSystemInfo.Location = New Point(15, 10)
        lblSystemInfo.Size = New Size(600, 80)
        pnlSystemInfo.Controls.Add(lblSystemInfo)
    End Sub

    Private Sub SetupTimer()
        tmrRefresh = New Timer()
        tmrRefresh.Interval = 30000 ' 30秒刷新一次
        tmrRefresh.Enabled = True
    End Sub

    Private Sub LoadDashboardData()
        Try
            LoadStatsData()
            LoadRecentActivity()
            SetupQuickActionEvents()
        Catch ex As Exception
            MessageBox.Show($"加载看板数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadStatsData()
        Try
            ' 加载库存总价值
            Dim inventoryValueQuery As String = "
                SELECT COALESCE(SUM(s.current_stock * m.standard_price), 0) as total_value
                FROM stock s
                INNER JOIN materials m ON s.material_id = m.id
                WHERE s.current_stock > 0"

            Dim inventoryValue As Decimal = Convert.ToDecimal(DatabaseManager.Instance.ExecuteScalar(inventoryValueQuery))
            UpdateCardValue(cardInventoryValue, $"¥{inventoryValue:N2}")

            ' 加载低库存预警数量
            Dim lowStockQuery As String = "
                SELECT COUNT(*) as low_stock_count
                FROM stock s
                INNER JOIN materials m ON s.material_id = m.id
                WHERE s.current_stock <= m.safety_stock AND m.safety_stock > 0"

            Dim lowStockCount As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(lowStockQuery))
            UpdateCardValue(cardLowStock, lowStockCount.ToString())

            ' 加载待处理订单数量
            Dim pendingOrdersQuery As String = "
                SELECT COUNT(*) as pending_count
                FROM orders
                WHERE order_status IN ('pending', 'confirmed')"

            Dim pendingOrders As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(pendingOrdersQuery))
            UpdateCardValue(cardPendingOrders, pendingOrders.ToString())

            ' 加载本月营收
            Dim monthlyRevenueQuery As String = "
                SELECT COALESCE(SUM(total_amount), 0) as monthly_revenue
                FROM orders
                WHERE order_status = 'completed'
                AND YEAR(order_date) = YEAR(CURDATE())
                AND MONTH(order_date) = MONTH(CURDATE())"

            Dim monthlyRevenue As Decimal = Convert.ToDecimal(DatabaseManager.Instance.ExecuteScalar(monthlyRevenueQuery))
            UpdateCardValue(cardMonthlyRevenue, $"¥{monthlyRevenue:N2}")

            ' 加载在线用户数（简化为总用户数）
            Dim activeUsersQuery As String = "SELECT COUNT(*) as user_count FROM users WHERE is_active = 1"
            Dim activeUsers As Integer = Convert.ToInt32(DatabaseManager.Instance.ExecuteScalar(activeUsersQuery))
            UpdateCardValue(cardActiveUsers, activeUsers.ToString())

            ' 系统状态（固定为正常）
            UpdateCardValue(cardSystemStatus, "正常")

        Catch ex As Exception
            MessageBox.Show($"加载统计数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateCardValue(card As Panel, value As String)
        For Each control As Control In card.Controls
            If control.Name = "value" AndAlso TypeOf control Is Label Then
                DirectCast(control, Label).Text = value
                Exit For
            End If
        Next
    End Sub

    Private Sub LoadRecentActivity()
        Try
            Dim activityQuery As String = "
                SELECT
                    '入库' as activity_type,
                    io.inbound_number as document_number,
                    io.inbound_date as activity_date,
                    CONCAT('供应商: ', COALESCE(s.supplier_name, '未知')) as description,
                    io.created_at
                FROM inbound_orders io
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                WHERE io.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

                UNION ALL

                SELECT
                    '出库' as activity_type,
                    oo.outbound_number as document_number,
                    oo.outbound_date as activity_date,
                    CONCAT('客户: ', COALESCE(c.customer_name, '未知')) as description,
                    oo.created_at
                FROM outbound_orders oo
                LEFT JOIN customers c ON oo.customer_id = c.id
                WHERE oo.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

                UNION ALL

                SELECT
                    '订单' as activity_type,
                    o.order_number as document_number,
                    o.order_date as activity_date,
                    CONCAT('客户: ', COALESCE(c.customer_name, '未知')) as description,
                    o.created_at
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

                ORDER BY created_at DESC
                LIMIT 10"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(activityQuery)

            ' 重命名列标题
            If dt.Columns.Contains("activity_type") Then dt.Columns("activity_type").ColumnName = "活动类型"
            If dt.Columns.Contains("document_number") Then dt.Columns("document_number").ColumnName = "单据号"
            If dt.Columns.Contains("activity_date") Then dt.Columns("activity_date").ColumnName = "日期"
            If dt.Columns.Contains("description") Then dt.Columns("description").ColumnName = "描述"
            If dt.Columns.Contains("created_at") Then dt.Columns("created_at").ColumnName = "创建时间"

            dgvRecentActivity.DataSource = dt

            ' 隐藏创建时间列
            If dgvRecentActivity.Columns.Contains("创建时间") Then
                dgvRecentActivity.Columns("创建时间").Visible = False
            End If

        Catch ex As Exception
            MessageBox.Show($"加载最近活动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetupQuickActionEvents()
        ' 新建订单
        AddHandler btnNewOrder.Click, Sub(s, e)
                                          Try
                                              Dim mainForm As MainForm = DirectCast(Me.ParentForm, MainForm)
                                              mainForm.OpenChildForm(New OrderDetailForm(0), "订单管理")
                                          Catch ex As Exception
                                              MessageBox.Show("打开订单管理失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                          End Try
                                      End Sub

        ' 入库管理
        AddHandler btnInbound.Click, Sub(s, e)
                                         Try
                                             Dim mainForm As MainForm = DirectCast(Me.ParentForm, MainForm)
                                             mainForm.OpenChildForm(New InboundForm(), "入库管理")
                                         Catch ex As Exception
                                             MessageBox.Show("打开入库管理失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                         End Try
                                     End Sub

        ' 出库管理
        AddHandler btnOutbound.Click, Sub(s, e)
                                          Try
                                              Dim mainForm As MainForm = DirectCast(Me.ParentForm, MainForm)
                                              mainForm.OpenChildForm(New OutboundForm(), "出库管理")
                                          Catch ex As Exception
                                              MessageBox.Show("打开出库管理失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                          End Try
                                      End Sub

        ' 库存盘点
        AddHandler btnStockCount.Click, Sub(s, e)
                                            Try
                                                Dim mainForm As MainForm = DirectCast(Me.ParentForm, MainForm)
                                                mainForm.OpenChildForm(New StockCountForm(), "库存盘点")
                                            Catch ex As Exception
                                                MessageBox.Show("打开库存盘点失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                            End Try
                                        End Sub

        ' 库存查询
        AddHandler btnInventoryQuery.Click, Sub(s, e)
                                                Try
                                                    Dim mainForm As MainForm = DirectCast(Me.ParentForm, MainForm)
                                                    mainForm.OpenChildForm(New InventoryForm(), "库存查询")
                                                Catch ex As Exception
                                                    MessageBox.Show("打开库存查询失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                                End Try
                                            End Sub

        ' 报表中心
        AddHandler btnReports.Click, Sub(s, e)
                                         Try
                                             Dim mainForm As MainForm = DirectCast(Me.ParentForm, MainForm)
                                             mainForm.OpenChildForm(New InOutReportForm(), "出入库报表")
                                         Catch ex As Exception
                                             MessageBox.Show("打开报表中心失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                         End Try
                                     End Sub
    End Sub

    Private Sub tmrRefresh_Tick(sender As Object, e As EventArgs) Handles tmrRefresh.Tick
        ' 更新时间显示
        lblDateTime.Text = DateTime.Now.ToString("yyyy年MM月dd日 dddd HH:mm:ss")

        ' 每5分钟刷新一次数据
        Static refreshCounter As Integer = 0
        refreshCounter += 1
        If refreshCounter >= 10 Then ' 30秒 * 10 = 5分钟
            LoadStatsData()
            LoadRecentActivity()
            refreshCounter = 0
        End If
    End Sub

    Protected Overrides Sub OnFormClosed(e As FormClosedEventArgs)
        If tmrRefresh IsNot Nothing Then
            tmrRefresh.Dispose()
        End If
        MyBase.OnFormClosed(e)
    End Sub
End Class
