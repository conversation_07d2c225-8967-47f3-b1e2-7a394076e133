Imports System.Windows.Forms
Imports MySql.Data.MySqlClient
Imports System.Windows.Forms.DataVisualization.Charting

Public Class OrderAnalysisForm
    Inherits Form

    ' 控件声明
    Private tabControl As TabControl
    Private tabOverview As TabPage
    Private tabTrend As TabPage
    Private tabCustomer As TabPage
    Private tabMaterial As TabPage
    Private tabStatus As TabPage

    ' 概览页面控件
    Private lblTotalOrders As Label
    Private lblTotalAmount As Label
    Private lblAvgOrderAmount As Label
    Private lblPendingOrders As Label
    Private lblCompletedOrders As Label
    Private lblCancelledOrders As Label
    Private dgvRecentOrders As DataGridView

    ' 趋势分析页面控件
    Private chartOrderTrend As Chart
    Private chartAmountTrend As Chart
    Private dtpTrendStart As DateTimePicker
    Private dtpTrendEnd As DateTimePicker
    Private btnRefreshTrend As Button
    Private cmbTrendPeriod As ComboBox

    ' 客户分析页面控件
    Private chartCustomerOrders As Chart
    Private chartCustomerAmount As Chart
    Private dgvCustomerAnalysis As DataGridView
    Private txtTopCustomers As TextBox

    ' 物料分析页面控件
    Private chartMaterialOrders As Chart
    Private chartMaterialQuantity As Chart
    Private dgvMaterialAnalysis As DataGridView
    Private txtTopMaterials As TextBox

    ' 状态分析页面控件
    Private chartOrderStatus As Chart
    Private dgvStatusAnalysis As DataGridView
    Private chartStatusTrend As Chart

    ' 工具栏
    Private toolStrip As ToolStrip
    Private btnRefresh As ToolStripButton
    Private btnExport As ToolStripButton
    Private btnPrint As ToolStripButton

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadData()
    End Sub

    Private Sub InitializeComponent()
        Me.SuspendLayout()
        Me.AutoScaleDimensions = New SizeF(7.0!, 17.0!)
        Me.AutoScaleMode = AutoScaleMode.Font
        Me.ClientSize = New Size(1400, 900)
        Me.Name = "OrderAnalysisForm"
        Me.Text = "订单分析"
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
        Me.ResumeLayout(False)
    End Sub

    Private Sub SetupUI()
        ' 工具栏
        SetupToolStrip()

        ' 主选项卡控件
        tabControl = New TabControl()
        tabControl.Dock = DockStyle.Fill
        tabControl.Font = New Font("Microsoft YaHei", 9)
        Me.Controls.Add(tabControl)

        ' 创建各个选项卡页面
        CreateOverviewTab()
        CreateTrendTab()
        CreateCustomerTab()
        CreateMaterialTab()
        CreateStatusTab()
    End Sub

    Private Sub SetupToolStrip()
        toolStrip = New ToolStrip()
        toolStrip.Font = New Font("Microsoft YaHei", 9)

        btnRefresh = New ToolStripButton("刷新数据")
        btnRefresh.Image = My.Resources.refresh_icon ' 需要添加图标资源
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click

        btnExport = New ToolStripButton("导出报表")
        btnExport.Image = My.Resources.export_icon ' 需要添加图标资源
        AddHandler btnExport.Click, AddressOf BtnExport_Click

        btnPrint = New ToolStripButton("打印报表")
        btnPrint.Image = My.Resources.print_icon ' 需要添加图标资源
        AddHandler btnPrint.Click, AddressOf BtnPrint_Click

        toolStrip.Items.AddRange({btnRefresh, New ToolStripSeparator(), btnExport, btnPrint})
        Me.Controls.Add(toolStrip)
    End Sub

    Private Sub CreateOverviewTab()
        tabOverview = New TabPage("概览统计")
        tabControl.TabPages.Add(tabOverview)

        ' 统计卡片区域
        Dim pnlStats As New Panel()
        pnlStats.Dock = DockStyle.Top
        pnlStats.Height = 120
        pnlStats.BackColor = Color.FromArgb(240, 240, 240)
        tabOverview.Controls.Add(pnlStats)

        ' 创建统计卡片
        CreateStatCard(pnlStats, "订单总数", "0", Color.FromArgb(52, 152, 219), New Point(20, 20), lblTotalOrders)
        CreateStatCard(pnlStats, "订单总金额", "¥0.00", Color.FromArgb(46, 204, 113), New Point(240, 20), lblTotalAmount)
        CreateStatCard(pnlStats, "平均订单金额", "¥0.00", Color.FromArgb(155, 89, 182), New Point(460, 20), lblAvgOrderAmount)
        CreateStatCard(pnlStats, "待处理订单", "0", Color.FromArgb(241, 196, 15), New Point(680, 20), lblPendingOrders)
        CreateStatCard(pnlStats, "已完成订单", "0", Color.FromArgb(26, 188, 156), New Point(900, 20), lblCompletedOrders)
        CreateStatCard(pnlStats, "已取消订单", "0", Color.FromArgb(231, 76, 60), New Point(1120, 20), lblCancelledOrders)

        ' 最近订单列表
        Dim lblRecentTitle As New Label()
        lblRecentTitle.Text = "最近订单"
        lblRecentTitle.Font = New Font("Microsoft YaHei", 12, FontStyle.Bold)
        lblRecentTitle.Location = New Point(20, 140)
        lblRecentTitle.Size = New Size(200, 30)
        tabOverview.Controls.Add(lblRecentTitle)

        dgvRecentOrders = New DataGridView()
        dgvRecentOrders.Location = New Point(20, 180)
        dgvRecentOrders.Size = New Size(1340, 400)
        dgvRecentOrders.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom
        dgvRecentOrders.ReadOnly = True
        dgvRecentOrders.AllowUserToAddRows = False
        dgvRecentOrders.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvRecentOrders.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabOverview.Controls.Add(dgvRecentOrders)
    End Sub

    Private Sub CreateStatCard(parent As Panel, title As String, value As String, color As Color, location As Point, ByRef valueLabel As Label)
        Dim card As New Panel()
        card.Size = New Size(200, 80)
        card.Location = location
        card.BackColor = Color.White
        card.BorderStyle = BorderStyle.FixedSingle

        Dim titleLabel As New Label()
        titleLabel.Text = title
        titleLabel.Font = New Font("Microsoft YaHei", 9)
        titleLabel.ForeColor = Color.Gray
        titleLabel.Location = New Point(10, 10)
        titleLabel.Size = New Size(180, 20)
        card.Controls.Add(titleLabel)

        valueLabel = New Label()
        valueLabel.Text = value
        valueLabel.Font = New Font("Microsoft YaHei", 16, FontStyle.Bold)
        valueLabel.ForeColor = color
        valueLabel.Location = New Point(10, 35)
        valueLabel.Size = New Size(180, 35)
        card.Controls.Add(valueLabel)

        parent.Controls.Add(card)
    End Sub

    Private Sub CreateTrendTab()
        tabTrend = New TabPage("趋势分析")
        tabControl.TabPages.Add(tabTrend)

        ' 控制面板
        Dim pnlTrendControl As New Panel()
        pnlTrendControl.Dock = DockStyle.Top
        pnlTrendControl.Height = 60
        pnlTrendControl.BackColor = Color.FromArgb(248, 249, 250)
        tabTrend.Controls.Add(pnlTrendControl)

        ' 时间范围选择
        Dim lblDateRange As New Label()
        lblDateRange.Text = "时间范围:"
        lblDateRange.Location = New Point(20, 20)
        lblDateRange.Size = New Size(80, 20)
        pnlTrendControl.Controls.Add(lblDateRange)

        dtpTrendStart = New DateTimePicker()
        dtpTrendStart.Location = New Point(100, 18)
        dtpTrendStart.Size = New Size(120, 25)
        dtpTrendStart.Value = DateTime.Now.AddMonths(-6)
        pnlTrendControl.Controls.Add(dtpTrendStart)

        Dim lblTo As New Label()
        lblTo.Text = "至"
        lblTo.Location = New Point(230, 20)
        lblTo.Size = New Size(20, 20)
        pnlTrendControl.Controls.Add(lblTo)

        dtpTrendEnd = New DateTimePicker()
        dtpTrendEnd.Location = New Point(250, 18)
        dtpTrendEnd.Size = New Size(120, 25)
        dtpTrendEnd.Value = DateTime.Now
        pnlTrendControl.Controls.Add(dtpTrendEnd)

        ' 周期选择
        Dim lblPeriod As New Label()
        lblPeriod.Text = "统计周期:"
        lblPeriod.Location = New Point(390, 20)
        lblPeriod.Size = New Size(80, 20)
        pnlTrendControl.Controls.Add(lblPeriod)

        cmbTrendPeriod = New ComboBox()
        cmbTrendPeriod.Location = New Point(470, 18)
        cmbTrendPeriod.Size = New Size(100, 25)
        cmbTrendPeriod.DropDownStyle = ComboBoxStyle.DropDownList
        cmbTrendPeriod.Items.AddRange({"按日", "按周", "按月", "按季度"})
        cmbTrendPeriod.SelectedIndex = 2 ' 默认按月
        pnlTrendControl.Controls.Add(cmbTrendPeriod)

        btnRefreshTrend = New Button()
        btnRefreshTrend.Text = "刷新"
        btnRefreshTrend.Location = New Point(590, 17)
        btnRefreshTrend.Size = New Size(80, 27)
        AddHandler btnRefreshTrend.Click, AddressOf BtnRefreshTrend_Click
        pnlTrendControl.Controls.Add(btnRefreshTrend)

        ' 图表容器
        Dim pnlCharts As New Panel()
        pnlCharts.Dock = DockStyle.Fill
        tabTrend.Controls.Add(pnlCharts)

        ' 订单数量趋势图
        chartOrderTrend = New Chart()
        chartOrderTrend.Size = New Size(680, 400)
        chartOrderTrend.Location = New Point(10, 10)
        chartOrderTrend.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        SetupChart(chartOrderTrend, "订单数量趋势", "时间", "订单数量")
        pnlCharts.Controls.Add(chartOrderTrend)

        ' 订单金额趋势图
        chartAmountTrend = New Chart()
        chartAmountTrend.Size = New Size(680, 400)
        chartAmountTrend.Location = New Point(700, 10)
        chartAmountTrend.Anchor = AnchorStyles.Top Or AnchorStyles.Right
        SetupChart(chartAmountTrend, "订单金额趋势", "时间", "金额")
        pnlCharts.Controls.Add(chartAmountTrend)
    End Sub

    Private Sub SetupChart(chart As Chart, title As String, xAxisTitle As String, yAxisTitle As String)
        chart.BackColor = Color.White
        chart.BorderlineColor = Color.Gray
        chart.BorderlineWidth = 1
        chart.BorderlineDashStyle = ChartDashStyle.Solid

        ' 图表区域
        Dim chartArea As New ChartArea()
        chartArea.Name = "MainArea"
        chartArea.BackColor = Color.White
        chartArea.BorderColor = Color.Gray
        chartArea.BorderWidth = 1
        chartArea.AxisX.Title = xAxisTitle
        chartArea.AxisY.Title = yAxisTitle
        chartArea.AxisX.MajorGrid.LineColor = Color.LightGray
        chartArea.AxisY.MajorGrid.LineColor = Color.LightGray
        chart.ChartAreas.Add(chartArea)

        ' 标题
        Dim chartTitle As New Title()
        chartTitle.Text = title
        chartTitle.Font = New Font("Microsoft YaHei", 12, FontStyle.Bold)
        chart.Titles.Add(chartTitle)

        ' 图例
        Dim legend As New Legend()
        legend.Name = "MainLegend"
        legend.Docking = Docking.Bottom
        chart.Legends.Add(legend)
    End Sub
