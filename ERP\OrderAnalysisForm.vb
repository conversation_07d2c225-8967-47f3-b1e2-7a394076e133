Imports System.Windows.Forms
Imports MySql.Data.MySqlClient

Public Class OrderAnalysisForm
    Inherits Form

    ' 控件声明
    Private tabControl As TabControl
    Private tabOverview As TabPage
    Private tabTrend As TabPage
    Private tabCustomer As TabPage
    Private tabMaterial As TabPage
    Private tabStatus As TabPage

    ' 概览页面控件
    Private lblTotalOrders As Label
    Private lblTotalAmount As Label
    Private lblAvgOrderAmount As Label
    Private lblPendingOrders As Label
    Private lblCompletedOrders As Label
    Private lblCancelledOrders As Label
    Private dgvRecentOrders As DataGridView

    ' 趋势分析页面控件
    Private dgvTrendData As DataGridView
    Private dtpTrendStart As DateTimePicker
    Private dtpTrendEnd As DateTimePicker
    Private btnRefreshTrend As Button
    Private cmbTrendPeriod As ComboBox

    ' 客户分析页面控件
    Private dgvCustomerAnalysis As DataGridView
    Private txtTopCustomers As TextBox

    ' 物料分析页面控件
    Private dgvMaterialAnalysis As DataGridView
    Private txtTopMaterials As TextBox

    ' 状态分析页面控件
    Private dgvStatusAnalysis As DataGridView

    ' 工具栏
    Private toolStrip As ToolStrip
    Private btnRefresh As ToolStripButton
    Private btnExport As ToolStripButton
    Private btnPrint As ToolStripButton

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadData()
    End Sub

    Private Sub InitializeComponent()
        Me.SuspendLayout()
        Me.AutoScaleDimensions = New SizeF(7.0!, 17.0!)
        Me.AutoScaleMode = AutoScaleMode.Font
        Me.ClientSize = New Size(1400, 900)
        Me.Name = "OrderAnalysisForm"
        Me.Text = "订单分析"
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
        Me.ResumeLayout(False)
    End Sub

    Private Sub SetupUI()
        ' 工具栏
        SetupToolStrip()

        ' 主选项卡控件
        tabControl = New TabControl()
        tabControl.Dock = DockStyle.Fill
        tabControl.Font = New Font("Microsoft YaHei", 9)
        Me.Controls.Add(tabControl)

        ' 创建各个选项卡页面
        CreateOverviewTab()
        CreateTrendTab()
        CreateCustomerTab()
        CreateMaterialTab()
        CreateStatusTab()
    End Sub

    Private Sub SetupToolStrip()
        toolStrip = New ToolStrip()
        toolStrip.Font = New Font("Microsoft YaHei", 9)

        btnRefresh = New ToolStripButton("刷新数据")
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click

        btnExport = New ToolStripButton("导出报表")
        AddHandler btnExport.Click, AddressOf BtnExport_Click

        btnPrint = New ToolStripButton("打印报表")
        AddHandler btnPrint.Click, AddressOf BtnPrint_Click

        toolStrip.Items.AddRange({btnRefresh, New ToolStripSeparator(), btnExport, btnPrint})
        Me.Controls.Add(toolStrip)
    End Sub

    Private Sub CreateOverviewTab()
        tabOverview = New TabPage("概览统计")
        tabControl.TabPages.Add(tabOverview)

        ' 统计卡片区域
        Dim pnlStats As New Panel()
        pnlStats.Dock = DockStyle.Top
        pnlStats.Height = 120
        pnlStats.BackColor = Color.FromArgb(240, 240, 240)
        tabOverview.Controls.Add(pnlStats)

        ' 创建统计卡片
        CreateStatCard(pnlStats, "订单总数", "0", Color.FromArgb(52, 152, 219), New Point(20, 20), lblTotalOrders)
        CreateStatCard(pnlStats, "订单总金额", "¥0.00", Color.FromArgb(46, 204, 113), New Point(240, 20), lblTotalAmount)
        CreateStatCard(pnlStats, "平均订单金额", "¥0.00", Color.FromArgb(155, 89, 182), New Point(460, 20), lblAvgOrderAmount)
        CreateStatCard(pnlStats, "待处理订单", "0", Color.FromArgb(241, 196, 15), New Point(680, 20), lblPendingOrders)
        CreateStatCard(pnlStats, "已完成订单", "0", Color.FromArgb(26, 188, 156), New Point(900, 20), lblCompletedOrders)
        CreateStatCard(pnlStats, "已取消订单", "0", Color.FromArgb(231, 76, 60), New Point(1120, 20), lblCancelledOrders)

        ' 最近订单列表
        Dim lblRecentTitle As New Label()
        lblRecentTitle.Text = "最近订单"
        lblRecentTitle.Font = New Font("Microsoft YaHei", 12, FontStyle.Bold)
        lblRecentTitle.Location = New Point(20, 140)
        lblRecentTitle.Size = New Size(200, 30)
        tabOverview.Controls.Add(lblRecentTitle)

        dgvRecentOrders = New DataGridView()
        dgvRecentOrders.Location = New Point(20, 180)
        dgvRecentOrders.Size = New Size(1340, 400)
        dgvRecentOrders.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom
        dgvRecentOrders.ReadOnly = True
        dgvRecentOrders.AllowUserToAddRows = False
        dgvRecentOrders.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvRecentOrders.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabOverview.Controls.Add(dgvRecentOrders)
    End Sub

    Private Sub CreateStatCard(parent As Panel, title As String, value As String, color As Color, location As Point, ByRef valueLabel As Label)
        Dim card As New Panel()
        card.Size = New Size(200, 80)
        card.Location = location
        card.BackColor = Color.White
        card.BorderStyle = BorderStyle.FixedSingle

        Dim titleLabel As New Label()
        titleLabel.Text = title
        titleLabel.Font = New Font("Microsoft YaHei", 9)
        titleLabel.ForeColor = Color.Gray
        titleLabel.Location = New Point(10, 10)
        titleLabel.Size = New Size(180, 20)
        card.Controls.Add(titleLabel)

        valueLabel = New Label()
        valueLabel.Text = value
        valueLabel.Font = New Font("Microsoft YaHei", 16, FontStyle.Bold)
        valueLabel.ForeColor = color
        valueLabel.Location = New Point(10, 35)
        valueLabel.Size = New Size(180, 35)
        card.Controls.Add(valueLabel)

        parent.Controls.Add(card)
    End Sub

    Private Sub CreateTrendTab()
        tabTrend = New TabPage("趋势分析")
        tabControl.TabPages.Add(tabTrend)

        ' 控制面板
        Dim pnlTrendControl As New Panel()
        pnlTrendControl.Dock = DockStyle.Top
        pnlTrendControl.Height = 60
        pnlTrendControl.BackColor = Color.FromArgb(248, 249, 250)
        tabTrend.Controls.Add(pnlTrendControl)

        ' 时间范围选择
        Dim lblDateRange As New Label()
        lblDateRange.Text = "时间范围:"
        lblDateRange.Location = New Point(20, 20)
        lblDateRange.Size = New Size(80, 20)
        pnlTrendControl.Controls.Add(lblDateRange)

        dtpTrendStart = New DateTimePicker()
        dtpTrendStart.Location = New Point(100, 18)
        dtpTrendStart.Size = New Size(120, 25)
        dtpTrendStart.Value = DateTime.Now.AddMonths(-6)
        pnlTrendControl.Controls.Add(dtpTrendStart)

        Dim lblTo As New Label()
        lblTo.Text = "至"
        lblTo.Location = New Point(230, 20)
        lblTo.Size = New Size(20, 20)
        pnlTrendControl.Controls.Add(lblTo)

        dtpTrendEnd = New DateTimePicker()
        dtpTrendEnd.Location = New Point(250, 18)
        dtpTrendEnd.Size = New Size(120, 25)
        dtpTrendEnd.Value = DateTime.Now
        pnlTrendControl.Controls.Add(dtpTrendEnd)

        ' 周期选择
        Dim lblPeriod As New Label()
        lblPeriod.Text = "统计周期:"
        lblPeriod.Location = New Point(390, 20)
        lblPeriod.Size = New Size(80, 20)
        pnlTrendControl.Controls.Add(lblPeriod)

        cmbTrendPeriod = New ComboBox()
        cmbTrendPeriod.Location = New Point(470, 18)
        cmbTrendPeriod.Size = New Size(100, 25)
        cmbTrendPeriod.DropDownStyle = ComboBoxStyle.DropDownList
        cmbTrendPeriod.Items.AddRange({"按日", "按周", "按月", "按季度"})
        cmbTrendPeriod.SelectedIndex = 2 ' 默认按月
        pnlTrendControl.Controls.Add(cmbTrendPeriod)

        btnRefreshTrend = New Button()
        btnRefreshTrend.Text = "刷新"
        btnRefreshTrend.Location = New Point(590, 17)
        btnRefreshTrend.Size = New Size(80, 27)
        AddHandler btnRefreshTrend.Click, AddressOf BtnRefreshTrend_Click
        pnlTrendControl.Controls.Add(btnRefreshTrend)

        ' 趋势数据表格
        dgvTrendData = New DataGridView()
        dgvTrendData.Dock = DockStyle.Fill
        dgvTrendData.ReadOnly = True
        dgvTrendData.AllowUserToAddRows = False
        dgvTrendData.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvTrendData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabTrend.Controls.Add(dgvTrendData)
    End Sub

    Private Sub CreateCustomerTab()
        tabCustomer = New TabPage("客户分析")
        tabControl.TabPages.Add(tabCustomer)

        ' 控制面板
        Dim pnlCustomerControl As New Panel()
        pnlCustomerControl.Dock = DockStyle.Top
        pnlCustomerControl.Height = 50
        pnlCustomerControl.BackColor = Color.FromArgb(248, 249, 250)
        tabCustomer.Controls.Add(pnlCustomerControl)

        Dim lblTopCustomers As New Label()
        lblTopCustomers.Text = "显示前"
        lblTopCustomers.Location = New Point(20, 15)
        lblTopCustomers.Size = New Size(60, 20)
        pnlCustomerControl.Controls.Add(lblTopCustomers)

        txtTopCustomers = New TextBox()
        txtTopCustomers.Location = New Point(80, 13)
        txtTopCustomers.Size = New Size(50, 25)
        txtTopCustomers.Text = "10"
        pnlCustomerControl.Controls.Add(txtTopCustomers)

        Dim lblCustomersUnit As New Label()
        lblCustomersUnit.Text = "名客户"
        lblCustomersUnit.Location = New Point(140, 15)
        lblCustomersUnit.Size = New Size(60, 20)
        pnlCustomerControl.Controls.Add(lblCustomersUnit)

        ' 客户分析数据表格
        dgvCustomerAnalysis = New DataGridView()
        dgvCustomerAnalysis.Dock = DockStyle.Fill
        dgvCustomerAnalysis.ReadOnly = True
        dgvCustomerAnalysis.AllowUserToAddRows = False
        dgvCustomerAnalysis.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvCustomerAnalysis.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabCustomer.Controls.Add(dgvCustomerAnalysis)
    End Sub

    Private Sub CreateMaterialTab()
        tabMaterial = New TabPage("物料分析")
        tabControl.TabPages.Add(tabMaterial)

        ' 控制面板
        Dim pnlMaterialControl As New Panel()
        pnlMaterialControl.Dock = DockStyle.Top
        pnlMaterialControl.Height = 50
        pnlMaterialControl.BackColor = Color.FromArgb(248, 249, 250)
        tabMaterial.Controls.Add(pnlMaterialControl)

        Dim lblTopMaterials As New Label()
        lblTopMaterials.Text = "显示前"
        lblTopMaterials.Location = New Point(20, 15)
        lblTopMaterials.Size = New Size(60, 20)
        pnlMaterialControl.Controls.Add(lblTopMaterials)

        txtTopMaterials = New TextBox()
        txtTopMaterials.Location = New Point(80, 13)
        txtTopMaterials.Size = New Size(50, 25)
        txtTopMaterials.Text = "15"
        pnlMaterialControl.Controls.Add(txtTopMaterials)

        Dim lblMaterialsUnit As New Label()
        lblMaterialsUnit.Text = "种物料"
        lblMaterialsUnit.Location = New Point(140, 15)
        lblMaterialsUnit.Size = New Size(60, 20)
        pnlMaterialControl.Controls.Add(lblMaterialsUnit)

        ' 物料分析数据表格
        dgvMaterialAnalysis = New DataGridView()
        dgvMaterialAnalysis.Dock = DockStyle.Fill
        dgvMaterialAnalysis.ReadOnly = True
        dgvMaterialAnalysis.AllowUserToAddRows = False
        dgvMaterialAnalysis.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvMaterialAnalysis.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabMaterial.Controls.Add(dgvMaterialAnalysis)
    End Sub

    Private Sub CreateStatusTab()
        tabStatus = New TabPage("状态分析")
        tabControl.TabPages.Add(tabStatus)

        ' 状态统计表格
        dgvStatusAnalysis = New DataGridView()
        dgvStatusAnalysis.Dock = DockStyle.Fill
        dgvStatusAnalysis.ReadOnly = True
        dgvStatusAnalysis.AllowUserToAddRows = False
        dgvStatusAnalysis.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvStatusAnalysis.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        tabStatus.Controls.Add(dgvStatusAnalysis)
    End Sub

    ' 数据加载方法
    Private Sub LoadData()
        Try
            LoadOverviewData()
            LoadTrendData()
            LoadCustomerData()
            LoadMaterialData()
            LoadStatusData()
        Catch ex As Exception
            MessageBox.Show($"加载数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadOverviewData()
        Try
            ' 加载统计数据
            Dim statsQuery As String = "
                SELECT
                    COUNT(*) as total_orders,
                    COALESCE(SUM(total_amount), 0) as total_amount,
                    COALESCE(AVG(total_amount), 0) as avg_amount,
                    SUM(CASE WHEN order_status IN ('pending', 'confirmed') THEN 1 ELSE 0 END) as pending_orders,
                    SUM(CASE WHEN order_status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                    SUM(CASE WHEN order_status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders
                FROM orders"

            Dim statsResult As DataTable = DatabaseManager.Instance.ExecuteQuery(statsQuery)
            If statsResult.Rows.Count > 0 Then
                Dim row As DataRow = statsResult.Rows(0)
                lblTotalOrders.Text = row("total_orders").ToString()
                lblTotalAmount.Text = "¥" + Convert.ToDecimal(row("total_amount")).ToString("N2")
                lblAvgOrderAmount.Text = "¥" + Convert.ToDecimal(row("avg_amount")).ToString("N2")
                lblPendingOrders.Text = row("pending_orders").ToString()
                lblCompletedOrders.Text = row("completed_orders").ToString()
                lblCancelledOrders.Text = row("cancelled_orders").ToString()
            End If

            ' 加载最近订单
            Dim recentQuery As String = "
                SELECT
                    o.order_number AS '订单编号',
                    c.customer_name AS '客户名称',
                    o.order_date AS '订单日期',
                    o.delivery_date AS '交货日期',
                    CASE o.order_status
                        WHEN 'pending' THEN '待处理'
                        WHEN 'confirmed' THEN '已确认'
                        WHEN 'in_production' THEN '生产中'
                        WHEN 'ready' THEN '待发货'
                        WHEN 'shipped' THEN '已发货'
                        WHEN 'delivered' THEN '已交货'
                        WHEN 'completed' THEN '已完成'
                        WHEN 'cancelled' THEN '已取消'
                        ELSE o.order_status
                    END AS '订单状态',
                    o.total_amount AS '总金额'
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                ORDER BY o.created_at DESC
                LIMIT 20"

            dgvRecentOrders.DataSource = DatabaseManager.Instance.ExecuteQuery(recentQuery)

        Catch ex As Exception
            MessageBox.Show($"加载概览数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadTrendData()
        Try
            Dim startDate As DateTime = dtpTrendStart.Value.Date
            Dim endDate As DateTime = dtpTrendEnd.Value.Date
            Dim period As String = GetPeriodFormat()

            ' 订单趋势分析
            Dim trendQuery As String = $"
                SELECT
                    {period} as '时间周期',
                    COUNT(*) as '订单数量',
                    COALESCE(SUM(total_amount), 0) as '订单金额',
                    COALESCE(AVG(total_amount), 0) as '平均金额'
                FROM orders
                WHERE order_date BETWEEN @start_date AND @end_date
                GROUP BY {period}
                ORDER BY {period}"

            Dim trendParams As New Dictionary(Of String, Object) From {
                {"@start_date", startDate},
                {"@end_date", endDate}
            }

            dgvTrendData.DataSource = DatabaseManager.Instance.ExecuteQuery(trendQuery, trendParams)

        Catch ex As Exception
            MessageBox.Show($"加载趋势数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function GetPeriodFormat() As String
        Select Case cmbTrendPeriod.SelectedIndex
            Case 0 ' 按日
                Return "DATE(order_date)"
            Case 1 ' 按周
                Return "YEARWEEK(order_date, 1)"
            Case 2 ' 按月
                Return "DATE_FORMAT(order_date, '%Y-%m')"
            Case 3 ' 按季度
                Return "CONCAT(YEAR(order_date), '-Q', QUARTER(order_date))"
            Case Else
                Return "DATE_FORMAT(order_date, '%Y-%m')"
        End Select
    End Function

    Private Sub LoadCustomerData()
        Try
            Dim topCount As Integer = If(Integer.TryParse(txtTopCustomers.Text, topCount), topCount, 10)

            ' 客户订单分析
            Dim customerQuery As String = "
                SELECT
                    c.customer_name AS '客户名称',
                    COUNT(o.id) AS '订单数量',
                    COALESCE(SUM(o.total_amount), 0) AS '订单总金额',
                    COALESCE(AVG(o.total_amount), 0) AS '平均订单金额',
                    MAX(o.order_date) AS '最近订单日期',
                    ROUND(COUNT(o.id) * 100.0 / (SELECT COUNT(*) FROM orders), 2) AS '订单占比(%)'
                FROM customers c
                LEFT JOIN orders o ON c.id = o.customer_id
                GROUP BY c.id, c.customer_name
                HAVING COUNT(o.id) > 0
                ORDER BY COUNT(o.id) DESC
                LIMIT @top_count"

            Dim customerParams As New Dictionary(Of String, Object) From {
                {"@top_count", topCount}
            }

            dgvCustomerAnalysis.DataSource = DatabaseManager.Instance.ExecuteQuery(customerQuery, customerParams)

        Catch ex As Exception
            MessageBox.Show($"加载客户数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadMaterialData()
        Try
            Dim topCount As Integer = If(Integer.TryParse(txtTopMaterials.Text, topCount), topCount, 15)

            ' 物料订单分析
            Dim materialQuery As String = "
                SELECT
                    m.material_code AS '物料编码',
                    m.material_name AS '物料名称',
                    COUNT(DISTINCT od.order_id) AS '订单次数',
                    COALESCE(SUM(od.order_quantity), 0) AS '订单总数量',
                    COALESCE(SUM(od.line_total), 0) AS '订单总金额',
                    COALESCE(AVG(od.unit_price_with_tax), 0) AS '平均单价'
                FROM materials m
                LEFT JOIN order_details od ON m.id = od.material_id
                LEFT JOIN orders o ON od.order_id = o.id
                GROUP BY m.id, m.material_code, m.material_name
                HAVING COUNT(DISTINCT od.order_id) > 0
                ORDER BY COUNT(DISTINCT od.order_id) DESC
                LIMIT @top_count"

            Dim materialParams As New Dictionary(Of String, Object) From {
                {"@top_count", topCount}
            }

            dgvMaterialAnalysis.DataSource = DatabaseManager.Instance.ExecuteQuery(materialQuery, materialParams)

        Catch ex As Exception
            MessageBox.Show($"加载物料数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadStatusData()
        Try
            ' 订单状态分析
            Dim statusQuery As String = "
                SELECT
                    CASE order_status
                        WHEN 'pending' THEN '待处理'
                        WHEN 'confirmed' THEN '已确认'
                        WHEN 'in_production' THEN '生产中'
                        WHEN 'ready' THEN '待发货'
                        WHEN 'shipped' THEN '已发货'
                        WHEN 'delivered' THEN '已交货'
                        WHEN 'completed' THEN '已完成'
                        WHEN 'cancelled' THEN '已取消'
                        ELSE order_status
                    END AS '订单状态',
                    COUNT(*) AS '订单数量',
                    COALESCE(SUM(total_amount), 0) AS '订单金额',
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM orders), 2) AS '占比(%)',
                    COALESCE(AVG(total_amount), 0) AS '平均金额'
                FROM orders
                GROUP BY order_status
                ORDER BY COUNT(*) DESC"

            dgvStatusAnalysis.DataSource = DatabaseManager.Instance.ExecuteQuery(statusQuery)

        Catch ex As Exception
            MessageBox.Show($"加载状态数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 事件处理程序
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadData()
        MessageBox.Show("数据已刷新", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BtnRefreshTrend_Click(sender As Object, e As EventArgs)
        LoadTrendData()
    End Sub

    Private Sub BtnExport_Click(sender As Object, e As EventArgs)
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "文本文件|*.txt|CSV文件|*.csv"
            saveDialog.Title = "导出订单分析报表"
            saveDialog.FileName = $"订单分析报表_{DateTime.Now:yyyyMMdd_HHmmss}"

            If saveDialog.ShowDialog() = DialogResult.OK Then
                ' 根据当前选中的选项卡导出相应数据
                Select Case tabControl.SelectedIndex
                    Case 0 ' 概览
                        ExportOverviewData(saveDialog.FileName)
                    Case 1 ' 趋势
                        ExportDataGridView(dgvTrendData, saveDialog.FileName, "趋势分析")
                    Case 2 ' 客户
                        ExportDataGridView(dgvCustomerAnalysis, saveDialog.FileName, "客户分析")
                    Case 3 ' 物料
                        ExportDataGridView(dgvMaterialAnalysis, saveDialog.FileName, "物料分析")
                    Case 4 ' 状态
                        ExportDataGridView(dgvStatusAnalysis, saveDialog.FileName, "状态分析")
                End Select

                MessageBox.Show("导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnPrint_Click(sender As Object, e As EventArgs)
        MessageBox.Show("打印功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub ExportOverviewData(fileName As String)
        ' 导出概览统计数据
        Dim sb As New System.Text.StringBuilder()
        sb.AppendLine("订单概览统计报表")
        sb.AppendLine($"生成时间: {DateTime.Now}")
        sb.AppendLine()
        sb.AppendLine($"订单总数: {lblTotalOrders.Text}")
        sb.AppendLine($"订单总金额: {lblTotalAmount.Text}")
        sb.AppendLine($"平均订单金额: {lblAvgOrderAmount.Text}")
        sb.AppendLine($"待处理订单: {lblPendingOrders.Text}")
        sb.AppendLine($"已完成订单: {lblCompletedOrders.Text}")
        sb.AppendLine($"已取消订单: {lblCancelledOrders.Text}")
        sb.AppendLine()
        sb.AppendLine("最近订单列表:")

        ' 添加表格数据
        For Each column As DataGridViewColumn In dgvRecentOrders.Columns
            If column.Visible Then
                sb.Append(column.HeaderText + vbTab)
            End If
        Next
        sb.AppendLine()

        For Each row As DataGridViewRow In dgvRecentOrders.Rows
            If Not row.IsNewRow Then
                For Each cell As DataGridViewCell In row.Cells
                    If dgvRecentOrders.Columns(cell.ColumnIndex).Visible Then
                        sb.Append(cell.Value?.ToString() + vbTab)
                    End If
                Next
                sb.AppendLine()
            End If
        Next

        System.IO.File.WriteAllText(fileName, sb.ToString(), System.Text.Encoding.UTF8)
    End Sub

    Private Sub ExportDataGridView(dgv As DataGridView, fileName As String, sheetName As String)
        Dim sb As New System.Text.StringBuilder()
        sb.AppendLine($"{sheetName}报表")
        sb.AppendLine($"生成时间: {DateTime.Now}")
        sb.AppendLine()

        ' 添加表头
        For Each column As DataGridViewColumn In dgv.Columns
            If column.Visible Then
                sb.Append(column.HeaderText + vbTab)
            End If
        Next
        sb.AppendLine()

        ' 添加数据行
        For Each row As DataGridViewRow In dgv.Rows
            If Not row.IsNewRow Then
                For Each cell As DataGridViewCell In row.Cells
                    If dgv.Columns(cell.ColumnIndex).Visible Then
                        sb.Append(cell.Value?.ToString() + vbTab)
                    End If
                Next
                sb.AppendLine()
            End If
        Next

        System.IO.File.WriteAllText(fileName, sb.ToString(), System.Text.Encoding.UTF8)
    End Sub
End Class
