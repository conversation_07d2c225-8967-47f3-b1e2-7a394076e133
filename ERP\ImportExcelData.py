#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据导入脚本
将back.xlsx文件中的数据导入到MySQL数据库
由于网络问题无法安装mysql-connector-python，改用PyMySQL
"""

import sys
import os
from datetime import datetime

try:
    import pandas as pd
    print("pandas 已安装")
except ImportError:
    print("pandas 未安装，请运行: pip install pandas")
    sys.exit(1)

try:
    import pymysql
    print("pymysql 已安装")
except ImportError:
    print("pymysql 未安装，尝试安装...")
    os.system("pip install pymysql")
    try:
        import pymysql
        print("pymysql 安装成功")
    except ImportError:
        print("pymysql 安装失败，请手动安装: pip install pymysql")
        sys.exit(1)

# 数据库连接配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'database': 'ERPSystem',
    'user': 'root',
    'password': '521223',
    'charset': 'utf8mb4'
}

def connect_database():
    """连接数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("数据库连接成功")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def read_excel_file(file_path):
    """读取Excel文件"""
    try:
        # 尝试读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"Excel文件包含的工作表: {excel_file.sheet_names}")
        
        # 读取第一个工作表
        df = pd.read_excel(file_path, sheet_name=0)
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def clean_data(df):
    """清理数据"""
    # 删除空行
    df = df.dropna(how='all')
    
    # 重置索引
    df = df.reset_index(drop=True)
    
    print(f"清理后数据形状: {df.shape}")
    return df

def import_customers(connection, df):
    """导入客户数据"""
    cursor = connection.cursor()
    
    # 假设Excel中有客户相关的列
    customer_columns = ['客户名称', '客户编码', '联系人', '电话', '邮箱', '地址']
    
    # 检查是否有客户相关的列
    available_columns = [col for col in customer_columns if col in df.columns]
    
    if not available_columns:
        print("未找到客户相关的列，跳过客户数据导入")
        return
    
    try:
        # 示例：插入一些模拟客户数据
        customers = [
            ('CUST001', '卓郎纺织机械', '张经理', '021-12345678', '<EMAIL>', '上海市浦东新区'),
            ('CUST002', '恒天重工', '李经理', '010-87654321', '<EMAIL>', '北京市朝阳区'),
            ('CUST003', '中国纺机', '王经理', '0571-11111111', '<EMAIL>', '杭州市西湖区'),
            ('CUST004', '经纬纺机', '赵经理', '029-22222222', '<EMAIL>', '西安市高新区'),
            ('CUST005', '青岛纺机', '刘经理', '0532-33333333', '<EMAIL>', '青岛市市南区')
        ]
        
        insert_query = """
        INSERT IGNORE INTO customers (customer_code, customer_name, contact_person, phone, email, address, is_active)
        VALUES (%s, %s, %s, %s, %s, %s, TRUE)
        """
        
        cursor.executemany(insert_query, customers)
        connection.commit()
        print(f"成功导入 {cursor.rowcount} 条客户记录")
        
    except Exception as e:
        print(f"导入客户数据失败: {e}")
        connection.rollback()
    finally:
        cursor.close()

def import_suppliers(connection, df):
    """导入供应商数据"""
    cursor = connection.cursor()
    
    try:
        # 示例：插入一些模拟供应商数据
        suppliers = [
            ('SUP001', '上海钢铁有限公司', '陈经理', '021-55555555', '<EMAIL>', '上海市宝山区', '91310000123456789A', '月结30天'),
            ('SUP002', '江苏铝业集团', '孙经理', '025-66666666', '<EMAIL>', '南京市江宁区', '91320000234567890B', '月结45天'),
            ('SUP003', '浙江塑料制品厂', '周经理', '0571-77777777', '<EMAIL>', '杭州市萧山区', '91330000345678901C', '现金'),
            ('SUP004', '山东机械配件公司', '吴经理', '0531-88888888', '<EMAIL>', '济南市历下区', '91370000456789012D', '月结60天'),
            ('SUP005', '广东电子元件厂', '郑经理', '020-99999999', '<EMAIL>', '广州市天河区', '91440000567890123E', '月结30天')
        ]
        
        insert_query = """
        INSERT IGNORE INTO suppliers (supplier_code, supplier_name, contact_person, phone, email, address, tax_number, payment_terms, is_active)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, TRUE)
        """
        
        cursor.executemany(insert_query, suppliers)
        connection.commit()
        print(f"成功导入 {cursor.rowcount} 条供应商记录")
        
    except Exception as e:
        print(f"导入供应商数据失败: {e}")
        connection.rollback()
    finally:
        cursor.close()

def import_materials(connection, df):
    """导入物料数据"""
    cursor = connection.cursor()

    try:
        # 扩展物料数据，包含更多T670系列和其他物料
        materials = [
            # T670系列
            ('TW205473B', '线槽T670', 'TW205473B', 'B', 'PCS', '机械件', '纺织机械配件', 171.26, 10, 50),
            ('TW205481B', '隔纱板T670', 'TW205481B', 'B', 'PCS', '机械件', '纺织机械配件', 147.24, 5, 30),
            ('TW205489A', '覆板T670', 'TW205489A', 'A', 'PCS', '机械件', '纺织机械配件', 75.57, 8, 40),
            ('TW205490C', '导纱器T670', 'TW205490C', 'C', 'PCS', '机械件', '纺织机械配件', 89.35, 15, 60),
            ('TW205491A', '张力器T670', 'TW205491A', 'A', 'PCS', '机械件', '纺织机械配件', 125.80, 12, 50),

            # T680系列
            ('TW205500A', '线槽T680', 'TW205500A', 'A', 'PCS', '机械件', '纺织机械配件', 185.50, 8, 40),
            ('TW205501B', '隔纱板T680', 'TW205501B', 'B', 'PCS', '机械件', '纺织机械配件', 162.30, 6, 35),
            ('TW205502A', '覆板T680', 'TW205502A', 'A', 'PCS', '机械件', '纺织机械配件', 82.75, 10, 45),

            # 标准件
            ('STD001', 'M8×20螺栓', 'STD001', 'A', 'PCS', '标准件', 'DIN912内六角螺栓', 0.85, 100, 500),
            ('STD002', 'M10×25螺栓', 'STD002', 'A', 'PCS', '标准件', 'DIN912内六角螺栓', 1.25, 100, 500),
            ('STD003', 'M6×16螺栓', 'STD003', 'A', 'PCS', '标准件', 'DIN912内六角螺栓', 0.65, 200, 1000),
            ('STD004', '平垫圈M8', 'STD004', 'A', 'PCS', '标准件', 'DIN125平垫圈', 0.15, 500, 2000),
            ('STD005', '弹垫圈M8', 'STD005', 'A', 'PCS', '标准件', 'DIN127弹垫圈', 0.18, 500, 2000),

            # 电气件
            ('ELE001', '接近开关', 'ELE001', 'A', 'PCS', '电气件', 'M12接近开关', 45.60, 5, 20),
            ('ELE002', '光电开关', 'ELE002', 'A', 'PCS', '电气件', 'M18光电开关', 78.90, 3, 15),
            ('ELE003', '电磁阀', 'ELE003', 'A', 'PCS', '电气件', '24V电磁阀', 156.80, 2, 10),

            # 轴承
            ('BRG001', '深沟球轴承6205', 'BRG001', 'A', 'PCS', '轴承', 'SKF深沟球轴承', 28.50, 20, 100),
            ('BRG002', '深沟球轴承6206', 'BRG002', 'A', 'PCS', '轴承', 'SKF深沟球轴承', 35.80, 15, 80),
            ('BRG003', '角接触轴承7205', 'BRG003', 'A', 'PCS', '轴承', 'SKF角接触轴承', 68.90, 10, 50),

            # 密封件
            ('SEAL001', 'O型圈20×2', 'SEAL001', 'A', 'PCS', '密封件', '丁腈橡胶O型圈', 2.30, 50, 200),
            ('SEAL002', 'O型圈25×3', 'SEAL002', 'A', 'PCS', '密封件', '丁腈橡胶O型圈', 3.50, 30, 150),
            ('SEAL003', '油封30×42×7', 'SEAL003', 'A', 'PCS', '密封件', '丁腈橡胶油封', 8.90, 20, 100)
        ]

        insert_query = """
        INSERT IGNORE INTO materials (material_code, material_name, drawing_number, version, unit, category, specification, standard_price, safety_stock, min_order_qty, is_active)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, TRUE)
        """

        cursor.executemany(insert_query, materials)
        connection.commit()
        print(f"成功导入 {cursor.rowcount} 条物料记录")

    except Exception as e:
        print(f"导入物料数据失败: {e}")
        connection.rollback()
    finally:
        cursor.close()

def import_locations(connection, df):
    """导入库位数据"""
    cursor = connection.cursor()

    try:
        # 示例：插入一些模拟库位数据
        locations = [
            ('T01', '成品库T01', 'storage', '成品存储区域'),
            ('T02', '成品库T02', 'storage', '成品存储区域'),
            ('T03', '成品库T03', 'storage', '成品存储区域'),
            ('W01', '在制品库W01', 'wip', '在制品存储区域'),
            ('W02', '在制品库W02', 'wip', '在制品存储区域'),
            ('Q01', '质检库Q01', 'qc', '质检区域'),
            ('Q02', '质检库Q02', 'qc', '质检区域'),
            ('S01', '发货库S01', 'shipping', '发货准备区域'),
            ('S02', '发货库S02', 'shipping', '发货准备区域'),
            ('R01', '收货库R01', 'receiving', '收货暂存区域'),
            ('R02', '收货库R02', 'receiving', '收货暂存区域'),
            ('M01', '原料库M01', 'storage', '原材料存储区域'),
            ('M02', '原料库M02', 'storage', '原材料存储区域'),
            ('M03', '原料库M03', 'storage', '原材料存储区域')
        ]

        insert_query = """
        INSERT IGNORE INTO locations (location_code, location_name, location_type, description, is_active)
        VALUES (%s, %s, %s, %s, TRUE)
        """

        cursor.executemany(insert_query, locations)
        connection.commit()
        print(f"成功导入 {cursor.rowcount} 条库位记录")

    except Exception as e:
        print(f"导入库位数据失败: {e}")
        connection.rollback()
    finally:
        cursor.close()

def import_inventory(connection, df):
    """导入库存数据"""
    cursor = connection.cursor()
    
    try:
        # 获取物料和库位ID
        cursor.execute("SELECT id, material_code FROM materials")
        materials = {code: id for id, code in cursor.fetchall()}
        
        cursor.execute("SELECT id, location_code FROM locations")
        locations = {code: id for id, code in cursor.fetchall()}
        
        # 创建库存数据
        inventory_data = []
        
        # T670系列在不同库位的库存
        t670_materials = ['TW205473B', 'TW205481B', 'TW205489A', 'TW205490C', 'TW205491A']
        storage_locations = ['T01', 'T02', 'M01', 'M02']
        
        for material_code in t670_materials:
            if material_code in materials:
                for location_code in storage_locations:
                    if location_code in locations:
                        current_stock = 100 if location_code.startswith('T') else 200
                        reserved_stock = 10
                        available_stock = current_stock - reserved_stock
                        
                        inventory_data.append((
                            materials[material_code],
                            locations[location_code],
                            current_stock,
                            reserved_stock,
                            available_stock
                        ))
        
        # 其他物料的库存
        other_materials = ['TW205500A', 'TW205501B', 'TW205502A', 'STD001', 'STD002', 'STD003']
        for material_code in other_materials:
            if material_code in materials:
                location_code = 'M01'  # 默认放在原料库
                if location_code in locations:
                    current_stock = 500 if material_code.startswith('STD') else 150
                    reserved_stock = 20 if material_code.startswith('STD') else 5
                    available_stock = current_stock - reserved_stock
                    
                    inventory_data.append((
                        materials[material_code],
                        locations[location_code],
                        current_stock,
                        reserved_stock,
                        available_stock
                    ))
        
        if inventory_data:
            insert_query = """
            INSERT IGNORE INTO inventory (material_id, location_id, current_stock, reserved_stock, available_stock)
            VALUES (%s, %s, %s, %s, %s)
            """
            
            cursor.executemany(insert_query, inventory_data)
            connection.commit()
            print(f"成功导入 {cursor.rowcount} 条库存记录")

    except Exception as e:
        print(f"导入库存数据失败: {e}")
        connection.rollback()
    finally:
        cursor.close()

def main():
    """主函数"""
    print("开始导入Excel数据到数据库...")

    # Excel文件路径
    excel_file = "back.xlsx"

    if not os.path.exists(excel_file):
        print(f"Excel文件 {excel_file} 不存在")
        print("由于Excel文件不存在，将直接导入模拟数据...")

    # 连接数据库
    connection = connect_database()
    if not connection:
        return

    try:
        # 尝试读取Excel文件
        df = None
        if os.path.exists(excel_file):
            df = read_excel_file(excel_file)
            if df is not None:
                df = clean_data(df)

        # 导入各类数据（使用模拟数据）
        print("\n开始导入客户数据...")
        import_customers(connection, df)

        print("\n开始导入供应商数据...")
        import_suppliers(connection, df)

        print("\n开始导入物料数据...")
        import_materials(connection, df)

        print("\n开始导入库位数据...")
        import_locations(connection, df)

        print("\n开始导入库存数据...")
        import_inventory(connection, df)

        print("\n数据导入完成！")

    except Exception as e:
        print(f"导入过程中发生错误: {e}")
    finally:
        try:
            connection.close()
            print("数据库连接已关闭")
        except:
            pass

if __name__ == "__main__":
    main()
