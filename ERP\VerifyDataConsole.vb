Module VerifyDataConsole
    Sub Main()
        Console.WriteLine("=== ERP系统数据验证工具 ===")
        Console.WriteLine()
        
        Try
            ' 验证导入的数据
            DataVerifier.VerifyImportedData()
            
            Console.WriteLine()
            Console.WriteLine("数据验证完成！按任意键退出...")
            Console.ReadKey()
            
        Catch ex As Exception
            Console.WriteLine($"程序执行出错: {ex.Message}")
            Console.WriteLine("按任意键退出...")
            Console.ReadKey()
        End Try
    End Sub
End Module
