Imports MySql.Data.MySqlClient
Imports System.IO

Public Class DataImporter
    Private Shared ReadOnly connectionString As String = "Server=127.0.0.1;Port=3306;Database=ERPSystem;Uid=root;Pwd=******;CharSet=utf8mb4;"

    Public Shared Sub ImportSimulationData()
        Try
            Console.WriteLine("开始导入模拟数据...")
            
            Using connection As New MySqlConnection(connectionString)
                connection.Open()
                Console.WriteLine("数据库连接成功")
                
                ' 导入客户数据
                ImportCustomers(connection)
                
                ' 导入供应商数据
                ImportSuppliers(connection)
                
                ' 导入物料数据
                ImportMaterials(connection)
                
                ' 导入库位数据
                ImportLocations(connection)
                
                ' 导入库存数据
                ImportInventory(connection)
                
                Console.WriteLine("模拟数据导入完成！")
            End Using
            
        Catch ex As Exception
            Console.WriteLine($"导入数据时发生错误: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub ImportCustomers(connection As MySqlConnection)
        Try
            Console.WriteLine("导入客户数据...")
            
            Dim customers As New List(Of Object())
            customers.Add(New Object() {"CUST001", "卓郎纺织机械", "张经理", "021-12345678", "<EMAIL>", "上海市浦东新区"})
            customers.Add(New Object() {"CUST002", "恒天重工", "李经理", "010-87654321", "<EMAIL>", "北京市朝阳区"})
            customers.Add(New Object() {"CUST003", "中国纺机", "王经理", "0571-11111111", "<EMAIL>", "杭州市西湖区"})
            customers.Add(New Object() {"CUST004", "经纬纺机", "赵经理", "029-22222222", "<EMAIL>", "西安市高新区"})
            customers.Add(New Object() {"CUST005", "青岛纺机", "刘经理", "0532-33333333", "<EMAIL>", "青岛市市南区"})
            
            Dim query As String = "INSERT IGNORE INTO customers (customer_code, customer_name, contact_person, phone, email, address, is_active) VALUES (@code, @name, @contact, @phone, @email, @address, TRUE)"
            
            For Each customer In customers
                Using cmd As New MySqlCommand(query, connection)
                    cmd.Parameters.AddWithValue("@code", customer(0))
                    cmd.Parameters.AddWithValue("@name", customer(1))
                    cmd.Parameters.AddWithValue("@contact", customer(2))
                    cmd.Parameters.AddWithValue("@phone", customer(3))
                    cmd.Parameters.AddWithValue("@email", customer(4))
                    cmd.Parameters.AddWithValue("@address", customer(5))
                    cmd.ExecuteNonQuery()
                End Using
            Next
            
            Console.WriteLine($"成功导入 {customers.Count} 条客户记录")
            
        Catch ex As Exception
            Console.WriteLine($"导入客户数据失败: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub ImportSuppliers(connection As MySqlConnection)
        Try
            Console.WriteLine("导入供应商数据...")
            
            Dim suppliers As New List(Of Object())
            suppliers.Add(New Object() {"SUP001", "上海钢铁有限公司", "陈经理", "021-55555555", "<EMAIL>", "上海市宝山区", "91310000123456789A", "月结30天"})
            suppliers.Add(New Object() {"SUP002", "江苏铝业集团", "孙经理", "025-66666666", "<EMAIL>", "南京市江宁区", "91320000234567890B", "月结45天"})
            suppliers.Add(New Object() {"SUP003", "浙江塑料制品厂", "周经理", "0571-77777777", "<EMAIL>", "杭州市萧山区", "91330000345678901C", "现金"})
            suppliers.Add(New Object() {"SUP004", "山东机械配件公司", "吴经理", "0531-88888888", "<EMAIL>", "济南市历下区", "91370000456789012D", "月结60天"})
            suppliers.Add(New Object() {"SUP005", "广东电子元件厂", "郑经理", "020-99999999", "<EMAIL>", "广州市天河区", "91440000567890123E", "月结30天"})
            
            Dim query As String = "INSERT IGNORE INTO suppliers (supplier_code, supplier_name, contact_person, phone, email, address, tax_number, payment_terms, is_active) VALUES (@code, @name, @contact, @phone, @email, @address, @tax, @payment, TRUE)"
            
            For Each supplier In suppliers
                Using cmd As New MySqlCommand(query, connection)
                    cmd.Parameters.AddWithValue("@code", supplier(0))
                    cmd.Parameters.AddWithValue("@name", supplier(1))
                    cmd.Parameters.AddWithValue("@contact", supplier(2))
                    cmd.Parameters.AddWithValue("@phone", supplier(3))
                    cmd.Parameters.AddWithValue("@email", supplier(4))
                    cmd.Parameters.AddWithValue("@address", supplier(5))
                    cmd.Parameters.AddWithValue("@tax", supplier(6))
                    cmd.Parameters.AddWithValue("@payment", supplier(7))
                    cmd.ExecuteNonQuery()
                End Using
            Next
            
            Console.WriteLine($"成功导入 {suppliers.Count} 条供应商记录")
            
        Catch ex As Exception
            Console.WriteLine($"导入供应商数据失败: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub ImportMaterials(connection As MySqlConnection)
        Try
            Console.WriteLine("导入物料数据...")
            
            Dim materials As New List(Of Object())

            ' T670系列
            materials.Add(New Object() {"*********", "线槽T670", "*********", "B", "PCS", "机械件", "纺织机械配件", 171.26, 10, 50})
            materials.Add(New Object() {"*********", "隔纱板T670", "*********", "B", "PCS", "机械件", "纺织机械配件", 147.24, 5, 30})
            materials.Add(New Object() {"*********", "覆板T670", "*********", "A", "PCS", "机械件", "纺织机械配件", 75.57, 8, 40})
            materials.Add(New Object() {"*********", "导纱器T670", "*********", "C", "PCS", "机械件", "纺织机械配件", 89.35, 15, 60})
            materials.Add(New Object() {"*********", "张力器T670", "*********", "A", "PCS", "机械件", "纺织机械配件", 125.80, 12, 50})

            ' T680系列
            materials.Add(New Object() {"*********", "线槽T680", "*********", "A", "PCS", "机械件", "纺织机械配件", 185.50, 8, 40})
            materials.Add(New Object() {"*********", "隔纱板T680", "*********", "B", "PCS", "机械件", "纺织机械配件", 162.30, 6, 35})
            materials.Add(New Object() {"*********", "覆板T680", "*********", "A", "PCS", "机械件", "纺织机械配件", 82.75, 10, 45})

            ' 标准件
            materials.Add(New Object() {"STD001", "M8×20螺栓", "STD001", "A", "PCS", "标准件", "DIN912内六角螺栓", 0.85, 100, 500})
            materials.Add(New Object() {"STD002", "M10×25螺栓", "STD002", "A", "PCS", "标准件", "DIN912内六角螺栓", 1.25, 100, 500})
            materials.Add(New Object() {"STD003", "M6×16螺栓", "STD003", "A", "PCS", "标准件", "DIN912内六角螺栓", 0.65, 200, 1000})
            materials.Add(New Object() {"STD004", "平垫圈M8", "STD004", "A", "PCS", "标准件", "DIN125平垫圈", 0.15, 500, 2000})
            materials.Add(New Object() {"STD005", "弹垫圈M8", "STD005", "A", "PCS", "标准件", "DIN127弹垫圈", 0.18, 500, 2000})

            ' 电气件
            materials.Add(New Object() {"ELE001", "接近开关", "ELE001", "A", "PCS", "电气件", "M12接近开关", 45.60, 5, 20})
            materials.Add(New Object() {"ELE002", "光电开关", "ELE002", "A", "PCS", "电气件", "M18光电开关", 78.90, 3, 15})
            materials.Add(New Object() {"ELE003", "电磁阀", "ELE003", "A", "PCS", "电气件", "24V电磁阀", 156.80, 2, 10})

            ' 轴承
            materials.Add(New Object() {"BRG001", "深沟球轴承6205", "BRG001", "A", "PCS", "轴承", "SKF深沟球轴承", 28.50, 20, 100})
            materials.Add(New Object() {"BRG002", "深沟球轴承6206", "BRG002", "A", "PCS", "轴承", "SKF深沟球轴承", 35.80, 15, 80})
            materials.Add(New Object() {"BRG003", "角接触轴承7205", "BRG003", "A", "PCS", "轴承", "SKF角接触轴承", 68.90, 10, 50})

            ' 密封件
            materials.Add(New Object() {"SEAL001", "O型圈20×2", "SEAL001", "A", "PCS", "密封件", "丁腈橡胶O型圈", 2.30, 50, 200})
            materials.Add(New Object() {"SEAL002", "O型圈25×3", "SEAL002", "A", "PCS", "密封件", "丁腈橡胶O型圈", 3.50, 30, 150})
            materials.Add(New Object() {"SEAL003", "油封30×42×7", "SEAL003", "A", "PCS", "密封件", "丁腈橡胶油封", 8.90, 20, 100})
            
            Dim query As String = "INSERT IGNORE INTO materials (material_code, material_name, drawing_number, version, unit, category, specification, standard_price, safety_stock, min_order_qty, is_active) VALUES (@code, @name, @drawing, @version, @unit, @category, @spec, @price, @safety, @min_order, TRUE)"
            
            For Each material In materials
                Using cmd As New MySqlCommand(query, connection)
                    cmd.Parameters.AddWithValue("@code", material(0))
                    cmd.Parameters.AddWithValue("@name", material(1))
                    cmd.Parameters.AddWithValue("@drawing", material(2))
                    cmd.Parameters.AddWithValue("@version", material(3))
                    cmd.Parameters.AddWithValue("@unit", material(4))
                    cmd.Parameters.AddWithValue("@category", material(5))
                    cmd.Parameters.AddWithValue("@spec", material(6))
                    cmd.Parameters.AddWithValue("@price", material(7))
                    cmd.Parameters.AddWithValue("@safety", material(8))
                    cmd.Parameters.AddWithValue("@min_order", material(9))
                    cmd.ExecuteNonQuery()
                End Using
            Next
            
            Console.WriteLine($"成功导入 {materials.Count} 条物料记录")
            
        Catch ex As Exception
            Console.WriteLine($"导入物料数据失败: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub ImportLocations(connection As MySqlConnection)
        Try
            Console.WriteLine("导入库位数据...")
            
            Dim locations As New List(Of Object())
            locations.Add(New Object() {"T01", "成品库T01", "storage", "成品存储区域"})
            locations.Add(New Object() {"T02", "成品库T02", "storage", "成品存储区域"})
            locations.Add(New Object() {"T03", "成品库T03", "storage", "成品存储区域"})
            locations.Add(New Object() {"W01", "在制品库W01", "wip", "在制品存储区域"})
            locations.Add(New Object() {"W02", "在制品库W02", "wip", "在制品存储区域"})
            locations.Add(New Object() {"Q01", "质检库Q01", "qc", "质检区域"})
            locations.Add(New Object() {"Q02", "质检库Q02", "qc", "质检区域"})
            locations.Add(New Object() {"S01", "发货库S01", "shipping", "发货准备区域"})
            locations.Add(New Object() {"S02", "发货库S02", "shipping", "发货准备区域"})
            locations.Add(New Object() {"R01", "收货库R01", "receiving", "收货暂存区域"})
            locations.Add(New Object() {"R02", "收货库R02", "receiving", "收货暂存区域"})
            locations.Add(New Object() {"M01", "原料库M01", "storage", "原材料存储区域"})
            locations.Add(New Object() {"M02", "原料库M02", "storage", "原材料存储区域"})
            locations.Add(New Object() {"M03", "原料库M03", "storage", "原材料存储区域"})
            
            Dim query As String = "INSERT IGNORE INTO locations (location_code, location_name, location_type, description, is_active) VALUES (@code, @name, @type, @desc, TRUE)"
            
            For Each location In locations
                Using cmd As New MySqlCommand(query, connection)
                    cmd.Parameters.AddWithValue("@code", location(0))
                    cmd.Parameters.AddWithValue("@name", location(1))
                    cmd.Parameters.AddWithValue("@type", location(2))
                    cmd.Parameters.AddWithValue("@desc", location(3))
                    cmd.ExecuteNonQuery()
                End Using
            Next
            
            Console.WriteLine($"成功导入 {locations.Count} 条库位记录")
            
        Catch ex As Exception
            Console.WriteLine($"导入库位数据失败: {ex.Message}")
        End Try
    End Sub

    Private Shared Sub ImportInventory(connection As MySqlConnection)
        Try
            Console.WriteLine("导入库存数据...")
            
            ' 获取物料和库位ID
            Dim materialIds As New Dictionary(Of String, Integer)
            Dim locationIds As New Dictionary(Of String, Integer)
            
            Using cmd As New MySqlCommand("SELECT id, material_code FROM materials", connection)
                Using reader As MySqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        materialIds(reader("material_code").ToString()) = Convert.ToInt32(reader("id"))
                    End While
                End Using
            End Using
            
            Using cmd As New MySqlCommand("SELECT id, location_code FROM locations", connection)
                Using reader As MySqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        locationIds(reader("location_code").ToString()) = Convert.ToInt32(reader("id"))
                    End While
                End Using
            End Using
            
            ' 创建库存数据
            Dim inventoryCount As Integer = 0
            Dim query As String = "INSERT IGNORE INTO inventory (material_id, location_id, current_stock, reserved_stock, available_stock) VALUES (@material_id, @location_id, @current, @reserved, @available)"
            
            ' T670系列在不同库位的库存
            Dim t670Materials As String() = {"*********", "*********", "*********", "*********", "*********"}
            Dim storageLocations As String() = {"T01", "T02", "M01", "M02"}
            
            For Each materialCode In t670Materials
                If materialIds.ContainsKey(materialCode) Then
                    For Each locationCode In storageLocations
                        If locationIds.ContainsKey(locationCode) Then
                            Dim currentStock As Integer = If(locationCode.StartsWith("T"), 100, 200)
                            Dim reservedStock As Integer = 10
                            Dim availableStock As Integer = currentStock - reservedStock
                            
                            Using cmd As New MySqlCommand(query, connection)
                                cmd.Parameters.AddWithValue("@material_id", materialIds(materialCode))
                                cmd.Parameters.AddWithValue("@location_id", locationIds(locationCode))
                                cmd.Parameters.AddWithValue("@current", currentStock)
                                cmd.Parameters.AddWithValue("@reserved", reservedStock)
                                cmd.Parameters.AddWithValue("@available", availableStock)
                                cmd.ExecuteNonQuery()
                                inventoryCount += 1
                            End Using
                        End If
                    Next
                End If
            Next
            
            ' 其他物料的库存
            Dim otherMaterials As String() = {"*********", "*********", "*********", "STD001", "STD002", "STD003"}
            For Each materialCode In otherMaterials
                If materialIds.ContainsKey(materialCode) AndAlso locationIds.ContainsKey("M01") Then
                    Dim currentStock As Integer = If(materialCode.StartsWith("STD"), 500, 150)
                    Dim reservedStock As Integer = If(materialCode.StartsWith("STD"), 20, 5)
                    Dim availableStock As Integer = currentStock - reservedStock
                    
                    Using cmd As New MySqlCommand(query, connection)
                        cmd.Parameters.AddWithValue("@material_id", materialIds(materialCode))
                        cmd.Parameters.AddWithValue("@location_id", locationIds("M01"))
                        cmd.Parameters.AddWithValue("@current", currentStock)
                        cmd.Parameters.AddWithValue("@reserved", reservedStock)
                        cmd.Parameters.AddWithValue("@available", availableStock)
                        cmd.ExecuteNonQuery()
                        inventoryCount += 1
                    End Using
                End If
            Next
            
            Console.WriteLine($"成功导入 {inventoryCount} 条库存记录")
            
        Catch ex As Exception
            Console.WriteLine($"导入库存数据失败: {ex.Message}")
        End Try
    End Sub
End Class
