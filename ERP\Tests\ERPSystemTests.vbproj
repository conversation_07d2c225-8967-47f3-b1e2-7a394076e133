<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <IsPackable>false</IsPackable>
    <RootNamespace>ERPSystemTests</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.3.2" />
    <PackageReference Include="MSTest.TestAdapter" Version="2.2.10" />
    <PackageReference Include="MSTest.TestFramework" Version="2.2.10" />
    <PackageReference Include="coverlet.collector" Version="3.1.2" />
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="FluentAssertions" Version="6.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\InventorySystem.vbproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="UnitTests\" />
    <Folder Include="IntegrationTests\" />
    <Folder Include="TestData\" />
  </ItemGroup>

</Project>
