Imports System.Windows.Forms
Imports System.Data
Imports System.Drawing

''' <summary>
''' 收付款记录管理窗体
''' </summary>
Public Class PaymentRecordsManagementForm
    Inherits Form

    ' 控件声明
    Private tabControl As TabControl
    Private tabPaymentRecords As TabPage
    Private tabReceivableRecords As TabPage
    Private tabPayableRecords As TabPage
    Private tabStatistics As TabPage

    ' 收付款记录查询控件
    Private pnlQueryConditions As Panel
    Private dtpStartDate As DateTimePicker
    Private dtpEndDate As DateTimePicker
    Private cmbPaymentType As ComboBox
    Private cmbPaymentMethod As ComboBox
    Private txtPaymentNumber As TextBox
    Private txtReferenceNumber As TextBox
    Private btnQuery As Button
    Private btnReset As Button
    Private btnExport As Button

    ' 数据显示控件
    Private dgvPaymentRecords As DataGridView
    Private dgvReceivableRecords As DataGridView
    Private dgvPayableRecords As DataGridView

    ' 统计信息控件
    Private lblTotalRecords As Label
    Private lblTotalAmount As Label
    Private lblReceivableAmount As Label
    Private lblPayableAmount As Label

    ' 统计图表控件
    Private pnlStatistics As Panel
    Private dgvMonthlyStats As DataGridView
    Private dgvMethodStats As DataGridView

    Public Sub New()
        InitializeComponent()
        SetupUI()
        LoadInitialData()
    End Sub

    Private Sub InitializeComponent()
        Me.SuspendLayout()
        Me.AutoScaleDimensions = New SizeF(7.0!, 17.0!)
        Me.AutoScaleMode = AutoScaleMode.Font
        Me.ClientSize = New Size(1400, 900)
        Me.Name = "PaymentRecordsManagementForm"
        Me.Text = "收付款记录管理"
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
        Me.Font = New Font("Microsoft YaHei", 9)
        Me.ResumeLayout(False)
    End Sub

    Private Sub SetupUI()
        ' 创建选项卡控件
        tabControl = New TabControl()
        tabControl.Dock = DockStyle.Fill
        tabControl.Font = New Font("Microsoft YaHei", 9)
        Me.Controls.Add(tabControl)

        ' 创建各个选项卡
        CreatePaymentRecordsTab()
        CreateReceivableRecordsTab()
        CreatePayableRecordsTab()
        CreateStatisticsTab()
    End Sub

    Private Sub CreatePaymentRecordsTab()
        tabPaymentRecords = New TabPage("所有收付款记录")
        tabControl.TabPages.Add(tabPaymentRecords)

        ' 查询条件面板
        CreateQueryPanel(tabPaymentRecords)

        ' 数据表格
        dgvPaymentRecords = New DataGridView()
        dgvPaymentRecords.Location = New Point(10, 120)
        dgvPaymentRecords.Size = New Size(1360, 600)
        dgvPaymentRecords.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom
        dgvPaymentRecords.ReadOnly = True
        dgvPaymentRecords.AllowUserToAddRows = False
        dgvPaymentRecords.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvPaymentRecords.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        dgvPaymentRecords.BackgroundColor = Color.White
        dgvPaymentRecords.GridColor = Color.FromArgb(230, 230, 230)
        tabPaymentRecords.Controls.Add(dgvPaymentRecords)

        ' 状态栏
        CreateStatusBar(tabPaymentRecords)
    End Sub

    Private Sub CreateQueryPanel(parentTab As TabPage)
        pnlQueryConditions = New Panel()
        pnlQueryConditions.Dock = DockStyle.Top
        pnlQueryConditions.Height = 100
        pnlQueryConditions.BackColor = Color.FromArgb(248, 249, 250)
        pnlQueryConditions.BorderStyle = BorderStyle.FixedSingle
        parentTab.Controls.Add(pnlQueryConditions)

        ' 第一行查询条件
        Dim lblDateRange As New Label()
        lblDateRange.Text = "日期范围:"
        lblDateRange.Location = New Point(20, 15)
        lblDateRange.Size = New Size(80, 20)
        pnlQueryConditions.Controls.Add(lblDateRange)

        dtpStartDate = New DateTimePicker()
        dtpStartDate.Location = New Point(100, 13)
        dtpStartDate.Size = New Size(120, 25)
        dtpStartDate.Value = DateTime.Now.AddMonths(-1)
        pnlQueryConditions.Controls.Add(dtpStartDate)

        Dim lblTo As New Label()
        lblTo.Text = "至"
        lblTo.Location = New Point(230, 15)
        lblTo.Size = New Size(20, 20)
        pnlQueryConditions.Controls.Add(lblTo)

        dtpEndDate = New DateTimePicker()
        dtpEndDate.Location = New Point(250, 13)
        dtpEndDate.Size = New Size(120, 25)
        dtpEndDate.Value = DateTime.Now
        pnlQueryConditions.Controls.Add(dtpEndDate)

        Dim lblPaymentType As New Label()
        lblPaymentType.Text = "类型:"
        lblPaymentType.Location = New Point(390, 15)
        lblPaymentType.Size = New Size(50, 20)
        pnlQueryConditions.Controls.Add(lblPaymentType)

        cmbPaymentType = New ComboBox()
        cmbPaymentType.Location = New Point(440, 13)
        cmbPaymentType.Size = New Size(100, 25)
        cmbPaymentType.DropDownStyle = ComboBoxStyle.DropDownList
        cmbPaymentType.Items.AddRange({"全部", "应收款", "应付款"})
        cmbPaymentType.SelectedIndex = 0
        pnlQueryConditions.Controls.Add(cmbPaymentType)

        Dim lblPaymentMethod As New Label()
        lblPaymentMethod.Text = "方式:"
        lblPaymentMethod.Location = New Point(560, 15)
        lblPaymentMethod.Size = New Size(50, 20)
        pnlQueryConditions.Controls.Add(lblPaymentMethod)

        cmbPaymentMethod = New ComboBox()
        cmbPaymentMethod.Location = New Point(610, 13)
        cmbPaymentMethod.Size = New Size(100, 25)
        cmbPaymentMethod.DropDownStyle = ComboBoxStyle.DropDownList
        cmbPaymentMethod.Items.AddRange({"全部", "现金", "银行转账", "支票", "承兑汇票", "其他"})
        cmbPaymentMethod.SelectedIndex = 0
        pnlQueryConditions.Controls.Add(cmbPaymentMethod)

        ' 第二行查询条件
        Dim lblPaymentNumber As New Label()
        lblPaymentNumber.Text = "收付款单号:"
        lblPaymentNumber.Location = New Point(20, 45)
        lblPaymentNumber.Size = New Size(80, 20)
        pnlQueryConditions.Controls.Add(lblPaymentNumber)

        txtPaymentNumber = New TextBox()
        txtPaymentNumber.Location = New Point(100, 43)
        txtPaymentNumber.Size = New Size(120, 25)
        pnlQueryConditions.Controls.Add(txtPaymentNumber)

        Dim lblReferenceNumber As New Label()
        lblReferenceNumber.Text = "参考号:"
        lblReferenceNumber.Location = New Point(240, 45)
        lblReferenceNumber.Size = New Size(60, 20)
        pnlQueryConditions.Controls.Add(lblReferenceNumber)

        txtReferenceNumber = New TextBox()
        txtReferenceNumber.Location = New Point(300, 43)
        txtReferenceNumber.Size = New Size(120, 25)
        pnlQueryConditions.Controls.Add(txtReferenceNumber)

        ' 查询按钮
        btnQuery = New Button()
        btnQuery.Text = "查询"
        btnQuery.Location = New Point(440, 42)
        btnQuery.Size = New Size(80, 27)
        btnQuery.BackColor = Color.FromArgb(52, 152, 219)
        btnQuery.ForeColor = Color.White
        btnQuery.FlatStyle = FlatStyle.Flat
        btnQuery.FlatAppearance.BorderSize = 0
        AddHandler btnQuery.Click, AddressOf BtnQuery_Click
        pnlQueryConditions.Controls.Add(btnQuery)

        btnReset = New Button()
        btnReset.Text = "重置"
        btnReset.Location = New Point(530, 42)
        btnReset.Size = New Size(80, 27)
        btnReset.BackColor = Color.FromArgb(149, 165, 166)
        btnReset.ForeColor = Color.White
        btnReset.FlatStyle = FlatStyle.Flat
        btnReset.FlatAppearance.BorderSize = 0
        AddHandler btnReset.Click, AddressOf BtnReset_Click
        pnlQueryConditions.Controls.Add(btnReset)

        btnExport = New Button()
        btnExport.Text = "导出Excel"
        btnExport.Location = New Point(620, 42)
        btnExport.Size = New Size(90, 27)
        btnExport.BackColor = Color.FromArgb(46, 204, 113)
        btnExport.ForeColor = Color.White
        btnExport.FlatStyle = FlatStyle.Flat
        btnExport.FlatAppearance.BorderSize = 0
        AddHandler btnExport.Click, AddressOf BtnExport_Click
        pnlQueryConditions.Controls.Add(btnExport)
    End Sub

    Private Sub CreateStatusBar(parentTab As TabPage)
        Dim pnlStatus As New Panel()
        pnlStatus.Dock = DockStyle.Bottom
        pnlStatus.Height = 40
        pnlStatus.BackColor = Color.FromArgb(248, 249, 250)
        pnlStatus.BorderStyle = BorderStyle.FixedSingle
        parentTab.Controls.Add(pnlStatus)

        lblTotalRecords = New Label()
        lblTotalRecords.Text = "总记录数: 0"
        lblTotalRecords.Location = New Point(20, 10)
        lblTotalRecords.Size = New Size(150, 20)
        lblTotalRecords.Font = New Font("Microsoft YaHei", 9, FontStyle.Bold)
        pnlStatus.Controls.Add(lblTotalRecords)

        lblTotalAmount = New Label()
        lblTotalAmount.Text = "总金额: ¥0.00"
        lblTotalAmount.Location = New Point(200, 10)
        lblTotalAmount.Size = New Size(200, 20)
        lblTotalAmount.Font = New Font("Microsoft YaHei", 9, FontStyle.Bold)
        lblTotalAmount.ForeColor = Color.FromArgb(52, 152, 219)
        pnlStatus.Controls.Add(lblTotalAmount)

        lblReceivableAmount = New Label()
        lblReceivableAmount.Text = "收款金额: ¥0.00"
        lblReceivableAmount.Location = New Point(420, 10)
        lblReceivableAmount.Size = New Size(200, 20)
        lblReceivableAmount.Font = New Font("Microsoft YaHei", 9, FontStyle.Bold)
        lblReceivableAmount.ForeColor = Color.FromArgb(46, 204, 113)
        pnlStatus.Controls.Add(lblReceivableAmount)

        lblPayableAmount = New Label()
        lblPayableAmount.Text = "付款金额: ¥0.00"
        lblPayableAmount.Location = New Point(640, 10)
        lblPayableAmount.Size = New Size(200, 20)
        lblPayableAmount.Font = New Font("Microsoft YaHei", 9, FontStyle.Bold)
        lblPayableAmount.ForeColor = Color.FromArgb(231, 76, 60)
        pnlStatus.Controls.Add(lblPayableAmount)
    End Sub

    Private Sub CreateReceivableRecordsTab()
        tabReceivableRecords = New TabPage("应收款记录")
        tabControl.TabPages.Add(tabReceivableRecords)

        dgvReceivableRecords = New DataGridView()
        dgvReceivableRecords.Dock = DockStyle.Fill
        dgvReceivableRecords.ReadOnly = True
        dgvReceivableRecords.AllowUserToAddRows = False
        dgvReceivableRecords.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvReceivableRecords.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        dgvReceivableRecords.BackgroundColor = Color.White
        dgvReceivableRecords.GridColor = Color.FromArgb(230, 230, 230)
        tabReceivableRecords.Controls.Add(dgvReceivableRecords)
    End Sub

    Private Sub CreatePayableRecordsTab()
        tabPayableRecords = New TabPage("应付款记录")
        tabControl.TabPages.Add(tabPayableRecords)

        dgvPayableRecords = New DataGridView()
        dgvPayableRecords.Dock = DockStyle.Fill
        dgvPayableRecords.ReadOnly = True
        dgvPayableRecords.AllowUserToAddRows = False
        dgvPayableRecords.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgvPayableRecords.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        dgvPayableRecords.BackgroundColor = Color.White
        dgvPayableRecords.GridColor = Color.FromArgb(230, 230, 230)
        tabPayableRecords.Controls.Add(dgvPayableRecords)
    End Sub

    Private Sub CreateStatisticsTab()
        tabStatistics = New TabPage("统计分析")
        tabControl.TabPages.Add(tabStatistics)

        pnlStatistics = New Panel()
        pnlStatistics.Dock = DockStyle.Fill
        pnlStatistics.BackColor = Color.White
        tabStatistics.Controls.Add(pnlStatistics)

        ' 月度统计表格
        Dim lblMonthlyStats As New Label()
        lblMonthlyStats.Text = "月度收付款统计"
        lblMonthlyStats.Location = New Point(20, 20)
        lblMonthlyStats.Size = New Size(200, 25)
        lblMonthlyStats.Font = New Font("Microsoft YaHei", 12, FontStyle.Bold)
        pnlStatistics.Controls.Add(lblMonthlyStats)

        dgvMonthlyStats = New DataGridView()
        dgvMonthlyStats.Location = New Point(20, 50)
        dgvMonthlyStats.Size = New Size(650, 300)
        dgvMonthlyStats.ReadOnly = True
        dgvMonthlyStats.AllowUserToAddRows = False
        dgvMonthlyStats.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        pnlStatistics.Controls.Add(dgvMonthlyStats)

        ' 付款方式统计表格
        Dim lblMethodStats As New Label()
        lblMethodStats.Text = "付款方式统计"
        lblMethodStats.Location = New Point(700, 20)
        lblMethodStats.Size = New Size(200, 25)
        lblMethodStats.Font = New Font("Microsoft YaHei", 12, FontStyle.Bold)
        pnlStatistics.Controls.Add(lblMethodStats)

        dgvMethodStats = New DataGridView()
        dgvMethodStats.Location = New Point(700, 50)
        dgvMethodStats.Size = New Size(650, 300)
        dgvMethodStats.ReadOnly = True
        dgvMethodStats.AllowUserToAddRows = False
        dgvMethodStats.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        pnlStatistics.Controls.Add(dgvMethodStats)
    End Sub

    Private Sub LoadInitialData()
        LoadPaymentRecords()
        LoadReceivableRecords()
        LoadPayableRecords()
        LoadStatistics()
    End Sub

    Private Sub LoadPaymentRecords()
        Try
            Dim sql As String = "
                SELECT
                    pr.id AS 'ID',
                    pr.payment_number AS '收付款单号',
                    CASE pr.payment_type
                        WHEN 'receivable' THEN '应收款'
                        WHEN 'payable' THEN '应付款'
                        ELSE pr.payment_type
                    END AS '类型',
                    pr.payment_date AS '收付款日期',
                    pr.payment_amount AS '金额',
                    pr.payment_method AS '付款方式',
                    pr.bank_account AS '银行账户',
                    pr.reference_number AS '参考号码',
                    CASE pr.payment_type
                        WHEN 'receivable' THEN CONCAT('应收单号: ', ar.ar_number, ' - 客户: ', c.customer_name)
                        WHEN 'payable' THEN CONCAT('应付单号: ', ap.ap_number, ' - 供应商: ', s.supplier_name)
                        ELSE '未知'
                    END AS '关联信息',
                    pr.description AS '备注',
                    u.username AS '创建人',
                    pr.created_at AS '创建时间'
                FROM payment_records pr
                LEFT JOIN accounts_receivable ar ON pr.payment_type = 'receivable' AND pr.reference_id = ar.id
                LEFT JOIN accounts_payable ap ON pr.payment_type = 'payable' AND pr.reference_id = ap.id
                LEFT JOIN customers c ON ar.customer_id = c.id
                LEFT JOIN suppliers s ON ap.supplier_id = s.id
                LEFT JOIN users u ON pr.created_by = u.id
                WHERE 1=1"

            Dim parameters As New Dictionary(Of String, Object)

            ' 添加查询条件
            If dtpStartDate.Value.Date <= dtpEndDate.Value.Date Then
                sql += " AND pr.payment_date BETWEEN @start_date AND @end_date"
                parameters.Add("@start_date", dtpStartDate.Value.Date)
                parameters.Add("@end_date", dtpEndDate.Value.Date)
            End If

            If cmbPaymentType.SelectedIndex > 0 Then
                Dim paymentType As String = If(cmbPaymentType.Text = "应收款", "receivable", "payable")
                sql += " AND pr.payment_type = @payment_type"
                parameters.Add("@payment_type", paymentType)
            End If

            If cmbPaymentMethod.SelectedIndex > 0 Then
                sql += " AND pr.payment_method = @payment_method"
                parameters.Add("@payment_method", cmbPaymentMethod.Text)
            End If

            If Not String.IsNullOrWhiteSpace(txtPaymentNumber.Text) Then
                sql += " AND pr.payment_number LIKE @payment_number"
                parameters.Add("@payment_number", $"%{txtPaymentNumber.Text}%")
            End If

            If Not String.IsNullOrWhiteSpace(txtReferenceNumber.Text) Then
                sql += " AND pr.reference_number LIKE @reference_number"
                parameters.Add("@reference_number", $"%{txtReferenceNumber.Text}%")
            End If

            sql += " ORDER BY pr.payment_date DESC, pr.created_at DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(sql, parameters)
            dgvPaymentRecords.DataSource = dt

            ' 隐藏ID列
            If dgvPaymentRecords.Columns.Contains("ID") Then
                dgvPaymentRecords.Columns("ID").Visible = False
            End If

            ' 设置数字列格式
            SetNumericColumnFormat(dgvPaymentRecords)

            ' 更新统计信息
            UpdateStatistics(dt)

        Catch ex As Exception
            MessageBox.Show($"加载收付款记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadReceivableRecords()
        Try
            Dim sql As String = "
                SELECT
                    pr.id AS 'ID',
                    pr.payment_number AS '收款单号',
                    pr.payment_date AS '收款日期',
                    pr.payment_amount AS '收款金额',
                    pr.payment_method AS '收款方式',
                    pr.bank_account AS '银行账户',
                    pr.reference_number AS '参考号码',
                    ar.ar_number AS '应收单号',
                    c.customer_name AS '客户名称',
                    ar.invoice_number AS '发票号码',
                    ar.original_amount AS '应收原始金额',
                    ar.outstanding_amount AS '剩余应收金额',
                    pr.description AS '备注',
                    u.username AS '创建人',
                    pr.created_at AS '创建时间'
                FROM payment_records pr
                INNER JOIN accounts_receivable ar ON pr.reference_id = ar.id
                LEFT JOIN customers c ON ar.customer_id = c.id
                LEFT JOIN users u ON pr.created_by = u.id
                WHERE pr.payment_type = 'receivable'
                ORDER BY pr.payment_date DESC, pr.created_at DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvReceivableRecords.DataSource = dt

            ' 隐藏ID列
            If dgvReceivableRecords.Columns.Contains("ID") Then
                dgvReceivableRecords.Columns("ID").Visible = False
            End If

            ' 设置数字列格式
            SetNumericColumnFormat(dgvReceivableRecords)

        Catch ex As Exception
            MessageBox.Show($"加载应收款记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadPayableRecords()
        Try
            Dim sql As String = "
                SELECT
                    pr.id AS 'ID',
                    pr.payment_number AS '付款单号',
                    pr.payment_date AS '付款日期',
                    pr.payment_amount AS '付款金额',
                    pr.payment_method AS '付款方式',
                    pr.bank_account AS '银行账户',
                    pr.reference_number AS '参考号码',
                    ap.ap_number AS '应付单号',
                    s.supplier_name AS '供应商名称',
                    ap.invoice_number AS '发票号码',
                    ap.original_amount AS '应付原始金额',
                    ap.outstanding_amount AS '剩余应付金额',
                    pr.description AS '备注',
                    u.username AS '创建人',
                    pr.created_at AS '创建时间'
                FROM payment_records pr
                INNER JOIN accounts_payable ap ON pr.reference_id = ap.id
                LEFT JOIN suppliers s ON ap.supplier_id = s.id
                LEFT JOIN users u ON pr.created_by = u.id
                WHERE pr.payment_type = 'payable'
                ORDER BY pr.payment_date DESC, pr.created_at DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvPayableRecords.DataSource = dt

            ' 隐藏ID列
            If dgvPayableRecords.Columns.Contains("ID") Then
                dgvPayableRecords.Columns("ID").Visible = False
            End If

            ' 设置数字列格式
            SetNumericColumnFormat(dgvPayableRecords)

        Catch ex As Exception
            MessageBox.Show($"加载应付款记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadStatistics()
        LoadMonthlyStatistics()
        LoadPaymentMethodStatistics()
    End Sub

    Private Sub LoadMonthlyStatistics()
        Try
            Dim sql As String = "
                SELECT
                    DATE_FORMAT(pr.payment_date, '%Y-%m') AS '月份',
                    COUNT(CASE WHEN pr.payment_type = 'receivable' THEN 1 END) AS '收款笔数',
                    COALESCE(SUM(CASE WHEN pr.payment_type = 'receivable' THEN pr.payment_amount END), 0) AS '收款金额',
                    COUNT(CASE WHEN pr.payment_type = 'payable' THEN 1 END) AS '付款笔数',
                    COALESCE(SUM(CASE WHEN pr.payment_type = 'payable' THEN pr.payment_amount END), 0) AS '付款金额',
                    COUNT(*) AS '总笔数',
                    SUM(pr.payment_amount) AS '总金额'
                FROM payment_records pr
                WHERE pr.payment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(pr.payment_date, '%Y-%m')
                ORDER BY 月份 DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvMonthlyStats.DataSource = dt

            ' 设置数字列格式
            SetNumericColumnFormat(dgvMonthlyStats)

        Catch ex As Exception
            MessageBox.Show($"加载月度统计失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadPaymentMethodStatistics()
        Try
            Dim sql As String = "
                SELECT
                    pr.payment_method AS '付款方式',
                    COUNT(CASE WHEN pr.payment_type = 'receivable' THEN 1 END) AS '收款笔数',
                    COALESCE(SUM(CASE WHEN pr.payment_type = 'receivable' THEN pr.payment_amount END), 0) AS '收款金额',
                    COUNT(CASE WHEN pr.payment_type = 'payable' THEN 1 END) AS '付款笔数',
                    COALESCE(SUM(CASE WHEN pr.payment_type = 'payable' THEN pr.payment_amount END), 0) AS '付款金额',
                    COUNT(*) AS '总笔数',
                    SUM(pr.payment_amount) AS '总金额'
                FROM payment_records pr
                WHERE pr.payment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY pr.payment_method
                ORDER BY 总金额 DESC"

            Dim dt As DataTable = DatabaseManager.Instance.ExecuteQuery(sql)
            dgvMethodStats.DataSource = dt

            ' 设置数字列格式
            SetNumericColumnFormat(dgvMethodStats)

        Catch ex As Exception
            MessageBox.Show($"加载付款方式统计失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateStatistics(dt As DataTable)
        Try
            Dim totalRecords As Integer = dt.Rows.Count
            Dim totalAmount As Decimal = 0
            Dim receivableAmount As Decimal = 0
            Dim payableAmount As Decimal = 0

            For Each row As DataRow In dt.Rows
                Dim amount As Decimal = Convert.ToDecimal(row("金额"))
                totalAmount += amount

                Dim paymentType As String = row("类型").ToString()
                If paymentType = "应收款" Then
                    receivableAmount += amount
                ElseIf paymentType = "应付款" Then
                    payableAmount += amount
                End If
            Next

            lblTotalRecords.Text = $"总记录数: {totalRecords}"
            lblTotalAmount.Text = $"总金额: ¥{totalAmount:N2}"
            lblReceivableAmount.Text = $"收款金额: ¥{receivableAmount:N2}"
            lblPayableAmount.Text = $"付款金额: ¥{payableAmount:N2}"

        Catch ex As Exception
            ' 忽略统计错误
        End Try
    End Sub

    Private Sub SetNumericColumnFormat(dgv As DataGridView)
        For Each column As DataGridViewColumn In dgv.Columns
            If column.HeaderText.Contains("金额") OrElse column.HeaderText.Contains("数量") Then
                column.DefaultCellStyle.Format = "N2"
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
            ElseIf column.HeaderText.Contains("笔数") Then
                column.DefaultCellStyle.Format = "N0"
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
            End If
        Next
    End Sub

    ' 事件处理程序
    Private Sub BtnQuery_Click(sender As Object, e As EventArgs)
        LoadPaymentRecords()
    End Sub

    Private Sub BtnReset_Click(sender As Object, e As EventArgs)
        dtpStartDate.Value = DateTime.Now.AddMonths(-1)
        dtpEndDate.Value = DateTime.Now
        cmbPaymentType.SelectedIndex = 0
        cmbPaymentMethod.SelectedIndex = 0
        txtPaymentNumber.Clear()
        txtReferenceNumber.Clear()
        LoadPaymentRecords()
    End Sub

    Private Sub BtnExport_Click(sender As Object, e As EventArgs)
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel文件|*.xlsx|CSV文件|*.csv|文本文件|*.txt"
            saveDialog.Title = "导出收付款记录"
            saveDialog.FileName = $"收付款记录_{DateTime.Now:yyyyMMdd_HHmmss}"

            If saveDialog.ShowDialog() = DialogResult.OK Then
                Dim currentTab As String = tabControl.SelectedTab.Text
                Dim dgv As DataGridView = Nothing

                Select Case tabControl.SelectedIndex
                    Case 0 ' 所有收付款记录
                        dgv = dgvPaymentRecords
                    Case 1 ' 应收款记录
                        dgv = dgvReceivableRecords
                    Case 2 ' 应付款记录
                        dgv = dgvPayableRecords
                    Case 3 ' 统计分析
                        ExportStatistics(saveDialog.FileName)
                        Return
                End Select

                If dgv IsNot Nothing Then
                    ExportDataGridView(dgv, saveDialog.FileName, currentTab)
                End If

                MessageBox.Show("导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ExportDataGridView(dgv As DataGridView, fileName As String, sheetName As String)
        Dim sb As New System.Text.StringBuilder()
        sb.AppendLine($"{sheetName}报表")
        sb.AppendLine($"导出时间: {DateTime.Now}")
        sb.AppendLine($"查询条件: {dtpStartDate.Value:yyyy-MM-dd} 至 {dtpEndDate.Value:yyyy-MM-dd}")
        sb.AppendLine()

        ' 添加表头
        For Each column As DataGridViewColumn In dgv.Columns
            If column.Visible Then
                sb.Append(column.HeaderText + vbTab)
            End If
        Next
        sb.AppendLine()

        ' 添加数据行
        For Each row As DataGridViewRow In dgv.Rows
            If Not row.IsNewRow Then
                For Each cell As DataGridViewCell In row.Cells
                    If dgv.Columns(cell.ColumnIndex).Visible Then
                        sb.Append(cell.Value?.ToString() + vbTab)
                    End If
                Next
                sb.AppendLine()
            End If
        Next

        System.IO.File.WriteAllText(fileName, sb.ToString(), System.Text.Encoding.UTF8)
    End Sub

    Private Sub ExportStatistics(fileName As String)
        Dim sb As New System.Text.StringBuilder()
        sb.AppendLine("收付款统计分析报表")
        sb.AppendLine($"导出时间: {DateTime.Now}")
        sb.AppendLine()

        ' 导出月度统计
        sb.AppendLine("月度收付款统计:")
        For Each column As DataGridViewColumn In dgvMonthlyStats.Columns
            sb.Append(column.HeaderText + vbTab)
        Next
        sb.AppendLine()

        For Each row As DataGridViewRow In dgvMonthlyStats.Rows
            If Not row.IsNewRow Then
                For Each cell As DataGridViewCell In row.Cells
                    sb.Append(cell.Value?.ToString() + vbTab)
                Next
                sb.AppendLine()
            End If
        Next

        sb.AppendLine()
        sb.AppendLine("付款方式统计:")
        For Each column As DataGridViewColumn In dgvMethodStats.Columns
            sb.Append(column.HeaderText + vbTab)
        Next
        sb.AppendLine()

        For Each row As DataGridViewRow In dgvMethodStats.Rows
            If Not row.IsNewRow Then
                For Each cell As DataGridViewCell In row.Cells
                    sb.Append(cell.Value?.ToString() + vbTab)
                Next
                sb.AppendLine()
            End If
        Next

        System.IO.File.WriteAllText(fileName, sb.ToString(), System.Text.Encoding.UTF8)
    End Sub
End Class
