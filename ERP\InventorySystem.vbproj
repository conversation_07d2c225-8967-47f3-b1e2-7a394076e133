<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <RootNamespace>InventorySystem</RootNamespace>
    <AssemblyTitle>库存管理系统</AssemblyTitle>
    <AssemblyDescription>基于VB.NET和MySQL的库存管理系统</AssemblyDescription>
    <AssemblyCompany>您的公司名称</AssemblyCompany>
    <AssemblyProduct>库存管理系统</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>InventorySystem.Program</StartupObject>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
  </PropertyGroup>

  <ItemGroup>
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MySql.Data" Version="8.2.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
    <PackageReference Include="System.Windows.Forms.DataVisualization" Version="1.0.0-prerelease-00109" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="LoginForm.Designer.vb">
      <DependentUpon>LoginForm.vb</DependentUpon>
    </Compile>
    <Compile Update="MainForm.Designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
    </Compile>
    <Compile Update="CustomerForm.Designer.vb">
      <DependentUpon>CustomerForm.vb</DependentUpon>
    </Compile>
    <Compile Update="MaterialForm.Designer.vb">
      <DependentUpon>MaterialForm.vb</DependentUpon>
    </Compile>
    <Compile Update="LocationForm.Designer.vb">
      <DependentUpon>LocationForm.vb</DependentUpon>
    </Compile>
    <Compile Update="SupplierForm.Designer.vb">
      <DependentUpon>SupplierForm.vb</DependentUpon>
    </Compile>
    <Compile Update="InventoryForm.Designer.vb">
      <DependentUpon>InventoryForm.vb</DependentUpon>
    </Compile>
    <Compile Update="InboundForm.Designer.vb">
      <DependentUpon>InboundForm.vb</DependentUpon>
    </Compile>
    <Compile Update="OutboundForm.Designer.vb">
      <DependentUpon>OutboundForm.vb</DependentUpon>
    </Compile>
    <Compile Update="OrderForm.Designer.vb">
      <DependentUpon>OrderForm.vb</DependentUpon>
    </Compile>
    <Compile Update="OrderDetailForm.Designer.vb">
      <DependentUpon>OrderDetailForm.vb</DependentUpon>
    </Compile>
    <Compile Update="OrderSearchForm.Designer.vb">
      <DependentUpon>OrderSearchForm.vb</DependentUpon>
    </Compile>
    <Compile Update="UserForm.Designer.vb">
      <DependentUpon>UserForm.vb</DependentUpon>
    </Compile>
    <Compile Update="OrderAnalysisForm.Designer.vb">
      <DependentUpon>OrderAnalysisForm.vb</DependentUpon>
    </Compile>
    <Compile Update="AccountsReceivableForm.Designer.vb">
      <DependentUpon>AccountsReceivableForm.vb</DependentUpon>
    </Compile>
    <Compile Update="AccountsPayableForm.Designer.vb">
      <DependentUpon>AccountsPayableForm.vb</DependentUpon>
    </Compile>
    <Compile Update="PaymentRecordForm.Designer.vb">
      <DependentUpon>PaymentRecordForm.vb</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="LoginForm.resx">
      <DependentUpon>LoginForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Update="MainForm.resx">
      <DependentUpon>MainForm.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>

</Project>
