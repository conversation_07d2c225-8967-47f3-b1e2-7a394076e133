Imports System

Module TestConnection
    Sub Main()
        Try
            Console.WriteLine("正在测试数据库连接...")
            
            ' 初始化数据库管理器
            DatabaseManager.Instance.Initialize()
            Console.WriteLine("数据库连接成功！")
            
            ' 测试查询
            Dim sql = "SELECT COUNT(*) as count FROM users"
            Dim dt = DatabaseManager.Instance.ExecuteQuery(sql)
            
            If dt.Rows.Count > 0 Then
                Console.WriteLine($"用户表记录数: {dt.Rows(0)("count")}")
            End If
            
            ' 测试材料表
            sql = "SELECT COUNT(*) as count FROM materials"
            dt = DatabaseManager.Instance.ExecuteQuery(sql)
            
            If dt.Rows.Count > 0 Then
                Console.WriteLine($"物料表记录数: {dt.Rows(0)("count")}")
            End If
            
            ' 测试客户表
            sql = "SELECT COUNT(*) as count FROM customers"
            dt = DatabaseManager.Instance.ExecuteQuery(sql)
            
            If dt.Rows.Count > 0 Then
                Console.WriteLine($"客户表记录数: {dt.Rows(0)("count")}")
            End If
            
            Console.WriteLine("数据库测试完成！")
            
        Catch ex As Exception
            Console.WriteLine($"数据库连接测试失败: {ex.Message}")
            Console.WriteLine($"详细错误: {ex.StackTrace}")
        End Try
        
        Console.WriteLine("按任意键退出...")
        Console.ReadKey()
    End Sub
End Module
